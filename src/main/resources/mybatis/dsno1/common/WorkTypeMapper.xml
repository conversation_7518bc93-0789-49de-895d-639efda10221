<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.fh.mapper.dsno1.common.WorkTypeMapper">
    <resultMap id="BaseResultMap" type="org.fh.vo.common.WorkTypeVo">
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="created_by" jdbcType="VARCHAR" property="createdBy"/>
        <result column="created_time" jdbcType="TIMESTAMP" property="createdTime"/>
        <result column="update_by" jdbcType="VARCHAR" property="updateBy"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>
    <sql id="Base_Column_List">
    id, name, deleted, created_by, created_time, update_by, update_time
  </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from biz_work_type
        where id = #{id,jdbcType=VARCHAR}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from biz_work_type
    where id = #{id,jdbcType=VARCHAR}
  </delete>
    <insert id="insert" parameterType="org.fh.entity.common.WorkTypeEntity">
    insert into biz_work_type (id, name, deleted, 
      created_by, created_time, update_by, 
      update_time)
    values (#{id,jdbcType=VARCHAR}, #{name,jdbcType=VARCHAR}, #{deleted,jdbcType=BIT}, 
      #{createdBy,jdbcType=VARCHAR}, #{createdTime,jdbcType=TIMESTAMP}, #{updateBy,jdbcType=VARCHAR}, 
      #{updateTime,jdbcType=TIMESTAMP})
  </insert>
    <insert id="insertSelective" parameterType="org.fh.entity.common.WorkTypeEntity">
        insert into biz_work_type
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="name != null">
                name,
            </if>
            <if test="deleted != null">
                deleted,
            </if>
            <if test="createdBy != null">
                created_by,
            </if>
            <if test="createdTime != null">
                created_time,
            </if>
            <if test="updateBy != null">
                update_by,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=VARCHAR},
            </if>
            <if test="name != null">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="deleted != null">
                #{deleted,jdbcType=BIT},
            </if>
            <if test="createdBy != null">
                #{createdBy,jdbcType=VARCHAR},
            </if>
            <if test="createdTime != null">
                #{createdTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateBy != null">
                #{updateBy,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="org.fh.entity.common.WorkTypeEntity">
        update biz_work_type
        <set>
            <if test="name != null">
                name = #{name,jdbcType=VARCHAR},
            </if>
            <if test="deleted != null">
                deleted = #{deleted,jdbcType=BIT},
            </if>
            <if test="createdBy != null">
                created_by = #{createdBy,jdbcType=VARCHAR},
            </if>
            <if test="createdTime != null">
                created_time = #{createdTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateBy != null">
                update_by = #{updateBy,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=VARCHAR}
    </update>
    <update id="updateByPrimaryKey" parameterType="org.fh.entity.common.WorkTypeEntity">
    update biz_work_type
    set name = #{name,jdbcType=VARCHAR},
      deleted = #{deleted,jdbcType=BIT},
      created_by = #{createdBy,jdbcType=VARCHAR},
      created_time = #{createdTime,jdbcType=TIMESTAMP},
      update_by = #{updateBy,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=VARCHAR}
  </update>

    <insert id="addWorkType" parameterType="org.fh.entity.common.WorkTypeEntity">
        insert into biz_work_type
        <trim prefix="(" suffix=")" suffixOverrides=",">
            id,
            <if test="name != null and name !=''">
                name,
            </if>
            deleted,
            <if test="createdBy != null and createdBy !=''">
                created_by,
            </if>
            created_time
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            #{id,jdbcType=VARCHAR},
            <if test="name != null and name !=''">
                #{name,jdbcType=VARCHAR},
            </if>
            0,
            <if test="createdBy != null and createdBy !=''">
                #{createdBy,jdbcType=VARCHAR},
            </if>
            <if test="createdTime != null and createdTime !=''">
                #{createdTime,jdbcType=VARCHAR},
            </if>
            now()
        </trim>
    </insert>

    <update id="deleteWorkTypeData" parameterType="String">
    update biz_work_type set deleted = '1' where id = #{id}
  </update>

    <!-- 修改班次信息 -->
    <update id="updateWorkType" parameterType="org.fh.entity.common.WorkTypeEntity">
        update biz_work_type
        <set>
            <if test="name != null and name !=''">
                name = #{name},
            </if>
            <if test="updateBy != null and updateBy !=''">
                update_by = #{updateBy},
            </if>
            update_time = now()
        </set>
        where id = #{id}
    </update>

    <!-- 根据Id查询班次信息 -->
    <select id="queryWorkTypeDetail" parameterType="String" resultMap="BaseResultMap">
    select id,name,created_by, created_time, update_by, update_time
    from biz_work_type
    where id = #{id}
  </select>

    <!-- 分页查询班次信息 -->
    <select id="queryWorkTypePage" resultMap="BaseResultMap" parameterType="org.fh.entity.common.WorkTypeEntity">
        select id,name,created_by, created_time, update_by, update_time
        from biz_work_type
        <where>
            <if test="name != null and name !=''">
                and name like '%${name}%'
            </if>
            and deleted = '0'
            <if test="startQueryDate != null and startQueryDate !=''">
                and clock &gt; #{startQueryDate}
            </if>
            <if test="endQueryDate != null and endQueryDate !=''">
                and clock &lt; #{endQueryDate}
            </if>
        </where>
    </select>

    <!-- 根据班次Id查询班次名称 -->
    <select id="queryWorkTypeNameById" parameterType="string" resultType="string">
        select name from biz_work_type where id = #{workTypeId}
    </select>

    <!-- 判断班次名称是否已经存在 -->
    <select id="checkWorkTypeName" resultType="int" parameterType="org.fh.entity.common.WorkTypeEntity">
        select count(*)
        from biz_work_type
        where name  = #{name}
        <if test="id != null and id !=''">
            and id != #{id}
        </if>
        and deleted = '0'
    </select>
</mapper>