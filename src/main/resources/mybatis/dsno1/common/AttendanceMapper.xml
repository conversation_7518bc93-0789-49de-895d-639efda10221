<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.fh.mapper.dsno1.common.AttendanceMapper">
    <resultMap id="BaseResultMap" type="org.fh.vo.common.AttendanceVo">
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="emp_id" jdbcType="VARCHAR" property="empId"/>
        <result column="attendant_date" jdbcType="VARCHAR" property="attendantDate"/>
        <result column="attendant_time" jdbcType="VARCHAR" property="attendantTime"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="department" jdbcType="VARCHAR" property="department"/>
        <result column="plant" jdbcType="VARCHAR" property="plant"/>
        <result column="workshop" jdbcType="VARCHAR" property="workshop"/>
        <result column="production_line" jdbcType="VARCHAR" property="productionLine"/>
        <result column="work_team" jdbcType="VARCHAR" property="workTeam"/>
        <result column="work_type" jdbcType="VARCHAR" property="workType"/>
        <result column="machine_name" jdbcType="VARCHAR" property="machineName"/>
    </resultMap>
    <sql id="Base_Column_List">
    id, emp_id, attendant_date, attendant_time, machine_sn
  </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from biz_attendance
        where id = #{id,jdbcType=VARCHAR}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from biz_attendance
    where id = #{id,jdbcType=VARCHAR}
  </delete>
    <insert id="insert" parameterType="org.fh.entity.common.AttendanceEntity">
    insert into biz_attendance (id, emp_id, attendant_date, 
      attendant_time, machine_sn)
    values (#{id,jdbcType=VARCHAR}, #{empId,jdbcType=VARCHAR}, #{attendantDate,jdbcType=DATE}, 
      #{attendantTime,jdbcType=TIME}, #{machineSn,jdbcType=VARCHAR})
  </insert>
    <insert id="insertSelective" parameterType="org.fh.entity.common.AttendanceEntity">
        insert into biz_attendance
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="empId != null">
                emp_id,
            </if>
            <if test="attendantDate != null">
                attendant_date,
            </if>
            <if test="attendantTime != null">
                attendant_time,
            </if>
            <if test="machineSn != null">
                machine_sn,
            </if>
            <if test="attendantClock != null">
                attendant_clock,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=VARCHAR},
            </if>
            <if test="empId != null">
                #{empId,jdbcType=VARCHAR},
            </if>
            <if test="attendantDate != null">
                #{attendantDate,jdbcType=DATE},
            </if>
            <if test="attendantTime != null">
                #{attendantTime,jdbcType=TIME},
            </if>
            <if test="machineSn != null">
                #{machineSn,jdbcType=VARCHAR},
            </if>
            <if test="attendantClock != null">
                #{attendantClock,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="org.fh.entity.common.AttendanceEntity">
        update biz_attendance
        <set>
            <if test="empId != null">
                emp_id = #{empId,jdbcType=VARCHAR},
            </if>
            <if test="attendantDate != null">
                attendant_date = #{attendantDate,jdbcType=DATE},
            </if>
            <if test="attendantTime != null">
                attendant_time = #{attendantTime,jdbcType=TIME},
            </if>
            <if test="machineSn != null">
                machine_sn = #{machineSn,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=VARCHAR}
    </update>
    <update id="updateByPrimaryKey" parameterType="org.fh.entity.common.AttendanceEntity">
    update biz_attendance
    set emp_id = #{empId,jdbcType=VARCHAR},
      attendant_date = #{attendantDate,jdbcType=DATE},
      attendant_time = #{attendantTime,jdbcType=TIME},
      machine_sn = #{machineSn,jdbcType=VARCHAR}
    where id = #{id,jdbcType=VARCHAR}
  </update>

    <!-- 分页查询出勤记录 -->
    <select id="queryAttendancePage" resultMap="BaseResultMap">
        select e.`name`,a.emp_id,cc.department,cc.plant,cc.workshop,e.production_line,e.work_team,a.work_type,
        a.attendant_date,a.attendant_time,m.machine_name
        from biz_attendance a
        LEFT JOIN biz_machine m FORCE INDEX ( machine_sn ) ON m.machine_sn = a.machine_sn and m.deleted = 0
        LEFT JOIN biz_employee e ON e.emp_id = a.emp_id
        LEFT JOIN biz_cost_center cc ON e.cost_center = cc.cost_center
        <where>
            <if test="name != null and name !=''">
                and e.name like '%${name}%'
            </if>
            <if test="empId != null and empId !=''">
                and a.emp_id like '%${empId}%'
            </if>
            <if test="plant != null and plant !=''">
                and cc.plant = #{plant}
            </if>
            <if test="workshop != null and workshop !=''">
                and cc.workshop like '%${workshop}%'
            </if>
            <if test="vsm != null and vsm !=''">
                and cc.vsm like '%${vsm}%'
            </if>
            <if test="productionLine != null and productionLine !=''">
                and e.production_line like '%${productionLine}%'
            </if>
            <if test="department != null and department !=''">
                and cc.department like '%${department}%'
            </if>
            <if test="workTeam != null and workTeam !=''">
                and e.work_team like '%${workTeam}%'
            </if>
            <if test="workType != null and workType !=''">
                and a.work_type like '%${workType}%'
            </if>
            <if test="startQueryDate != null and startQueryDate !=''">
                and a.attendant_clock &gt; #{startQueryDate}
            </if>
            <if test="endQueryDate != null and endQueryDate !=''">
                and a.attendant_clock &lt; #{endQueryDate}
            </if>
        </where>
        order by a.attendant_clock desc
    </select>

    <!-- 查询某员工某日的打卡记录 -->
    <select id="queryAttendanceList" resultMap="BaseResultMap">
        select a.emp_id,a.attendant_date,a.attendant_time,m.machine_name
        from biz_attendance a
        left join biz_machine m on m.machine_sn = a.machine_sn and m.deleted = 0
        where
        a.emp_id = #{empId}
        and a.attendant_date like '%${attendantDate}%'
        order by attendant_clock desc
    </select>

    <!--  -->
    <select id="queryAttendanceMonth" resultType="string">
        select distinct attendant_date from biz_attendance a where
        a.emp_id = #{empId}
        and a.attendant_date like '%${attendantDate}%'
    </select>

    <!-- 根据条件查询可以导出的考勤机信息 -->
    <select id="exportAttendanceData" parameterType="org.fh.entity.common.AttendanceEntity"
            resultType="org.fh.model.exportModel.AttendanceExportModel">
        select e.`name`,a.emp_id as empId,cc.department,cc.plant,cc.workshop,e.production_line as
        productionLine,e.work_team as
        workTeam,
        a.attendant_date as attendantDate,a.attendant_time as attendantTime,m.machine_name as machineName,a.work_type as
        workType
        from biz_attendance a
        left join biz_employee e on e.emp_id = a.emp_id
        left join biz_cost_center cc on e.cost_center = cc.cost_center
        left join biz_machine m on m.machine_sn = a.machine_sn and m.deleted = 0
        <where>
            <if test="name != null and name !=''">
                and e.name like '%${name}%'
            </if>
            <if test="empId != null and empId !=''">
                and a.emp_id like '%${empId}%'
            </if>
            <if test="plant != null and plant !=''">
                and cc.plant = #{plant}
            </if>
            <if test="workshop != null and workshop !=''">
                and cc.workshop like '%${workshop}%'
            </if>
            <if test="productionLine != null and productionLine !=''">
                and e.production_line like '%${productionLine}%'
            </if>
            <if test="department != null and department !=''">
                and cc.department like '%${department}%'
            </if>
            <if test="workTeam != null and workTeam !=''">
                and e.work_team like '%${workTeam}%'
            </if>
            <if test="workType != null and workType !=''">
                and a.work_type like '%${workType}%'
            </if>
            <if test="startQueryDate != null and startQueryDate !=''">
                and a.attendant_clock &gt; #{startQueryDate}
            </if>
            <if test="endQueryDate != null and endQueryDate !=''">
                and a.attendant_clock &lt; #{endQueryDate}
            </if>
        </where>
        order by a.attendant_clock desc
    </select>

    <insert id="addAttendance" parameterType="org.fh.entity.common.AttendanceEntity">
        insert into biz_attendance
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null and id !=''">
                id,
            </if>
            <if test="empId != null and empId !=''">
                emp_id,
            </if>
            <if test="attendantDate != null and attendantDate !=''">
                attendant_date,
            </if>
            <if test="workType != null and workType !=''">
                work_type,
            </if>
            <if test="attendantTime != null and attendantTime !=''">
                attendant_time,
            </if>
            <if test="machineSn != null and machineSn !=''">
                machine_sn,
            </if>
            <if test="attendantClock != null and attendantClock !=''">
                attendant_clock,
            </if>
            status,create_time
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null and id !=''">
                #{id},
            </if>
            <if test="empId != null and empId !=''">
                #{empId},
            </if>
            <if test="attendantDate != null and attendantDate !=''">
                #{attendantDate},
            </if>
            <if test="workType != null and workType !=''">
                #{workType},
            </if>
            <if test="attendantTime != null and attendantTime !=''">
                #{attendantTime},
            </if>
            <if test="machineSn != null and machineSn !=''">
                #{machineSn},
            </if>
            <if test="attendantClock != null and attendantClock !=''">
                #{attendantClock},
            </if>
            0,now()
        </trim>
    </insert>

    <!-- 查询指定时间下的打卡记录 -->
    <select id="downloadAttendance" resultMap="BaseResultMap">
        select emp_id,attendant_date,attendant_time from biz_attendance
        where create_time &gt; #{startTime} and create_time &lt; #{endTime}
    </select>

    <select id="downloadAttendanceTxt" resultMap="BaseResultMap">
        select a.emp_id,a.attendant_date,a.attendant_time
        from biz_attendance a
        left join biz_employee e on e.emp_id = a.emp_id
        left join biz_cost_center cc on e.cost_center = cc.cost_center
        <where>
            <if test="name != null and name !=''">
                and e.name like '%${name}%'
            </if>
            <if test="empId != null and empId !=''">
                and a.emp_id like '%${empId}%'
            </if>
            <if test="plant != null and plant !=''">
                and cc.plant = #{plant}
            </if>
            <if test="workshop != null and workshop !=''">
                and cc.workshop like '%${workshop}%'
            </if>
            <if test="productionLine != null and productionLine !=''">
                and e.production_line like '%${productionLine}%'
            </if>
            <if test="department != null and department !=''">
                and cc.department like '%${department}%'
            </if>
            <if test="workTeam != null and workTeam !=''">
                and e.work_team like '%${workTeam}%'
            </if>
            <if test="workType != null and workType !=''">
                and a.work_type like '%${workType}%'
            </if>
            <if test="startQueryDate != null and startQueryDate !=''">
                and a.attendant_clock &gt; #{startQueryDate}
            </if>
            <if test="endQueryDate != null and endQueryDate !=''">
                and a.attendant_clock &lt; #{endQueryDate}
            </if>
        </where>
    </select>

    <!-- 查询为标记的打卡记录 -->
    <select id="queryAttendanceSendLog" resultMap="BaseResultMap">
        select id,emp_id,attendant_date,attendant_time from biz_attendance
        where status = 0
    </select>

    <!-- 批量修改考勤记录标记 -->
    <update id="updateAttendanceStatus" parameterType="collection">
        update biz_attendance set status = '1' where id in
        <foreach collection="list" separator="," item="item" close=")" open="(">
            #{item}
        </foreach>
    </update>

    <select id="queryAttendanceByPlant" resultMap="BaseResultMap">
        select at.emp_id,at.attendant_date,at.attendant_time,at.attendant_clock from biz_attendance at
        left join biz_employee e on e.emp_id = at.emp_id
	    left join biz_cost_center cc on cc.cost_center = e.cost_center
		where attendant_clock &gt; #{startTime} and attendant_clock &lt; #{endTime}
		and cc.plant = #{plant}
    </select>

    <select id="queryAttendanceHistory" resultType="int" parameterType="string">
        select count(*) from biz_attendance where emp_id = #{empId}
    </select>

    <select id="queryCaution" resultType="string" parameterType="string">
        SELECT
            distinct(e.open_id)
        FROM
            ( SELECT count( emp_id ) AS count, emp_id,plant, workshop, work_team
            FROM biz_attendance WHERE attendant_date > #{recordDate} GROUP BY plant, workshop, work_team ) ss
            left join biz_work_group wg on ss.plant= wg.plant and ss.workshop = wg.workshop and wg.name = ss.work_team
            left join biz_employee e on e.emp_id = wg.leader_id
        WHERE
            wg.deleted = '0'
            and ss.count &gt; 5
    </select>

    <!-- 查询对应的领班下员工的连续工作天数 -->
    <select id="queryCautionEmployee" resultType="map">
        SELECT
        e.emp_id as empId,ss.count,e.name,e.work_team as workTeam
        FROM
        (
        SELECT
        count( emp_id ) AS count,
        emp_id
        FROM
        biz_attendance
        WHERE
        emp_id IN (
        <foreach collection="list" item="item" separator=",">
            #{item}
        </foreach>
        )
        AND attendant_date &gt;= #{date}
        GROUP BY
        emp_id
        ) ss left join biz_employee e on e.emp_id = ss.emp_id
        WHERE
        ss.count > 6
    </select>

    <!-- 根据时间查询打卡记录(大于查询时间) -->
    <select id="queryAttendanceByTime" resultType="string" parameterType="string">
        select e.open_id from biz_attendance a
        left join biz_employee e on a.emp_id = e.emp_id
        where attendant_clock &gt;= #{recordTime}
    </select>

    <!-- 查询考勤机指定时间考勤统计 -->
    <select id="queryAttendanceTotal" resultType="map">
        select count(ate.machine_sn)as number,ate.machine_sn as machineSn,m.machine_name as machineName,m.plant from biz_machine m
        left join biz_attendance ate on m.machine_sn = ate.machine_sn
        where ate.attendant_clock &gt; #{startTime} and ate.attendant_clock &lt; #{endTime}
        group by m.machine_sn
        order by m.plant desc
    </select>

    <insert id="addAttendanceList">
        insert into biz_attendance ( id,emp_id,attendant_date,work_type,attendant_time,machine_sn,attendant_clock,status,create_time)
        values
        <foreach collection="list" item="item" separator=",">
            (
            #{item.id},#{item.empId},#{item.attendantDate},#{item.workType},#{item.attendantTime},#{item.machineSn},#{item.attendantClock},0,now()
            )
        </foreach>
    </insert>
</mapper>