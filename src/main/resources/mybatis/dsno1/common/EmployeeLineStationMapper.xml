<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.fh.mapper.dsno1.common.EmployeeLineStationMapper">
    <resultMap id="BaseResultMap" type="org.fh.vo.common.EmployeeLineStationVo">
        <id column="employee_line_station_id" jdbcType="VARCHAR" property="employeeLineStationId"/>
        <result column="line" jdbcType="VARCHAR" property="line"/>
        <result column="station" jdbcType="VARCHAR" property="station"/>
        <result column="emp_id" jdbcType="VARCHAR" property="empId"/>
        <result column="stationName" jdbcType="VARCHAR" property="stationName"/>
        <result column="lineName" jdbcType="VARCHAR" property="lineName"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="create_by" jdbcType="VARCHAR" property="createBy"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_by" jdbcType="VARCHAR" property="updateBy"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>
    <sql id="Base_Column_List">
    employee_line_station_id, line, station, emp_id, create_by, create_time, update_by, 
    update_time
  </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from biz_employee_line_station
        where employee_line_station_id = #{employeeLineStationId,jdbcType=VARCHAR}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from biz_employee_line_station
    where employee_line_station_id = #{employeeLineStationId,jdbcType=VARCHAR}
  </delete>
    <insert id="insert" parameterType="org.fh.entity.common.EmployeeLineStationEntity">
    insert into biz_employee_line_station (employee_line_station_id, line, station, 
      emp_id, create_by, create_time, 
      update_by, update_time)
    values (#{employeeLineStationId,jdbcType=VARCHAR}, #{line,jdbcType=VARCHAR}, #{station,jdbcType=VARCHAR}, 
      #{empId,jdbcType=VARCHAR}, #{createBy,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, 
      #{updateBy,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP})
  </insert>
    <insert id="insertSelective" parameterType="org.fh.entity.common.EmployeeLineStationEntity">
        insert into biz_employee_line_station
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="employeeLineStationId != null">
                employee_line_station_id,
            </if>
            <if test="line != null">
                line,
            </if>
            <if test="station != null">
                station,
            </if>
            <if test="empId != null">
                emp_id,
            </if>
            <if test="createBy != null">
                create_by,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateBy != null">
                update_by,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="employeeLineStationId != null">
                #{employeeLineStationId,jdbcType=VARCHAR},
            </if>
            <if test="line != null">
                #{line,jdbcType=VARCHAR},
            </if>
            <if test="station != null">
                #{station,jdbcType=VARCHAR},
            </if>
            <if test="empId != null">
                #{empId,jdbcType=VARCHAR},
            </if>
            <if test="createBy != null">
                #{createBy,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateBy != null">
                #{updateBy,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="org.fh.entity.common.EmployeeLineStationEntity">
        update biz_employee_line_station
        <set>
            <if test="line != null">
                line = #{line,jdbcType=VARCHAR},
            </if>
            <if test="station != null">
                station = #{station,jdbcType=VARCHAR},
            </if>
            <if test="empId != null">
                emp_id = #{empId,jdbcType=VARCHAR},
            </if>
            <if test="createBy != null">
                create_by = #{createBy,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateBy != null">
                update_by = #{updateBy,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where employee_line_station_id = #{employeeLineStationId,jdbcType=VARCHAR}
    </update>
    <update id="updateByPrimaryKey" parameterType="org.fh.entity.common.EmployeeLineStationEntity">
        update biz_employee_line_station
        set line = #{line,jdbcType=VARCHAR},
          station = #{station,jdbcType=VARCHAR},
          emp_id = #{empId,jdbcType=VARCHAR},
          create_by = #{createBy,jdbcType=VARCHAR},
          create_time = #{createTime,jdbcType=TIMESTAMP},
          update_by = #{updateBy,jdbcType=VARCHAR},
          update_time = #{updateTime,jdbcType=TIMESTAMP}
        where employee_line_station_id = #{employeeLineStationId,jdbcType=VARCHAR}
      </update>

    <!-- 单个添加员工与站点绑定信息 -->
    <insert id="addEmployeeLineStation" parameterType="org.fh.entity.common.EmployeeLineStationEntity">
        insert into biz_employee_line_station
        <trim prefix="(" suffix=")" suffixOverrides=",">
            employee_line_station_id,deleted,
            <if test="line != null and line !=''">
                line,
            </if>
            <if test="station != null and station !=''">
                station,
            </if>
            <if test="empId != null and empId !=''">
                emp_id,
            </if>
            <if test="createBy != null and createBy !=''">
                create_by,
            </if>
            create_time
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            #{employeeLineStationId},0,
            <if test="line != null and line !=''">
                #{line},
            </if>
            <if test="station != null and station !=''">
                #{station},
            </if>
            <if test="empId != null and empId !=''">
                #{empId},
            </if>
            <if test="createBy != null and createBy !=''">
                #{createBy},
            </if>
            now()
        </trim>
    </insert>

    <!-- 判断员工是否已经有记录 -->
    <select id="queryValidEmployeeLineStation" parameterType="string" resultType="int">
        select count(*) from biz_employee_line_station where emp_id = #{empId}
    </select>

    <!-- 修改员工站点绑定信息 -->
    <update id="updateEmployeeLineStation" parameterType="org.fh.entity.common.EmployeeLineStationEntity">
        update biz_employee_line_station
        <set>
            <if test="line != null and line !=''">
                line = #{line},
            </if>
            <if test="station != null and station !=''">
                station = #{station},
            </if>
            <if test="updateBy != null and updateBy !=''">
                update_by = #{updateBy},
            </if>
            update_time = now()
        </set>
        where emp_id = #{empId}
    </update>

    <select id="queryEmployeeLineStationPage" resultMap="BaseResultMap">
        select e.emp_id,e.name,l.name as lineName,s.name as stationName,ela.line,ela.station
        from biz_employee_line_station ela
        left join biz_employee e on e.emp_id = ela.emp_id
        left join biz_line l on l.id = ela.line
        left join biz_station s on s.id = ela.station
        left join biz_cost_center cc on e.cost_center = cc.cost_center
        <where>
            and ela.deleted = '0'
            <if test="name !=null and name !=''">
                and e.name like '%${name}%'
            </if>
            <if test="empId !=null and empId !=''">
                and ela.emp_id = #{empId}
            </if>
            <if test="lineName !=null and lineName !=''">
                and l.name like '%${lineName}%'
            </if>
            <if test="plant !=null and plant !=''">
                and cc.plant = #{plant}
            </if>
            <if test="workshop !=null and workshop !=''">
                and cc.workshop like '%${workshop}%'
            </if>
            <if test="vsm !=null and vsm !=''">
                and cc.vsm like '%${vsm}%'
            </if>
        </where>
    </select>

    <!-- 根据条件查询可以导出的员工班车统计信息(带班次) -->
    <select id="exportEmployeeLineStationData" parameterType="org.fh.entity.common.EmployeeLineStationEntity"
            resultType="org.fh.model.exportModel.EmployeeLineStationExportModel">
        select e.name,e.emp_id as empId,l.name as lineName,s.name as stationName,cc.plant,cc.workshop,e.work_team as workTeam,swt.work_type as workType
        from biz_employee_line_station ela
        left join biz_employee e on e.emp_id = ela.emp_id
        left join biz_line l on l.id = ela.line
        left join biz_station s on s.id = ela.station
        left join biz_cost_center cc on cc.cost_center = e.cost_center
        left join biz_schedule_work_team swt on e.work_team = swt.work_team
        <where>
            <if test="name != null and name !=''">
                and e.name like '%${name}%'
            </if>
            <if test="empId != null and empId !=''">
                and ela.emp_id like '%${empId}%'
            </if>
            <if test="lineName != null and lineName !=''">
                and l.name like '%${lineName}%'
            </if>
            <if test="station != null and stationName !=''">
                and s.name like '%${stationName}%'
            </if>
            <if test="scheduleDate !=null and scheduleDate !=''">
                and swt.schedule_date = #{scheduleDate}
            </if>
        </where>
        order by l.name,swt.work_type desc
    </select>

    <!-- 根据条件查询可以导出的员工班车统计信息(不带班次) -->
    <select id="queryEmployeeLineStationData" parameterType="org.fh.entity.common.EmployeeLineStationEntity"
            resultType="org.fh.model.exportModel.EmployeeLineStationExportModel">
        select e.name,e.emp_id as empId,l.name as lineName,s.name as stationName,cc.plant,cc.workshop,cc.vsm,e.work_team as workTeam
        from biz_employee_line_station ela
        left join biz_employee e on e.emp_id = ela.emp_id
        left join biz_line l on l.id = ela.line
        left join biz_station s on s.id = ela.station
        left join biz_cost_center cc on cc.cost_center = e.cost_center
        <where>
            <if test="name != null and name !=''">
                and e.name like '%${name}%'
            </if>
            <if test="empId != null and empId !=''">
                and ela.emp_id like '%${empId}%'
            </if>
            <if test="lineName != null and lineName !=''">
                and l.name like '%${lineName}%'
            </if>
            <if test="station != null and stationName !=''">
                and s.name like '%${stationName}%'
            </if>
            <if test="plant !=null and plant !=''">
                and cc.plant = #{plant}
            </if>
            <if test="workshop !=null and workshop !=''">
                and cc.workshop like '%${workshop}%'
            </if>
            <if test="vsm !=null and vsm !=''">
                and cc.vsm like '%${vsm}%'
            </if>
        </where>
    </select>

    <select id="queryEmployeeLineDetail" parameterType="string" resultMap="BaseResultMap">
        select  ela.line,l.name as lineName,s.name as stationName,ela.station
        from biz_employee_line_station ela
        left join biz_line l on l.id = ela.line
        left join biz_station s on s.id = ela.station
        where ela.deleted = '0' and ela.emp_id = #{empId} limit 1
    </select>
</mapper>