<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.fh.mapper.dsno1.common.OvertimeDetailMapper">
    <resultMap id="BaseResultMap" type="org.fh.entity.common.OvertimeDetailEntity">
        <id column="timeout_detail_id" jdbcType="VARCHAR" property="timeoutDetailId"/>
        <result column="one_time" jdbcType="DOUBLE" property="oneTime"/>
        <result column="two_time" jdbcType="DOUBLE" property="twoTime"/>
        <result column="three_time" jdbcType="DOUBLE" property="threeTime"/>
        <result column="emp_id" jdbcType="VARCHAR" property="empId"/>
        <result column="year_month" jdbcType="VARCHAR" property="yearMonth"/>
    </resultMap>
    <sql id="Base_Column_List">
    timeout_detail_id, one_time, two_time, three_time, emp_id, year_month
  </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from biz_overtime_detail
        where timeout_detail_id = #{timeoutDetailId,jdbcType=VARCHAR}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from biz_overtime_detail
    where timeout_detail_id = #{timeoutDetailId,jdbcType=VARCHAR}
  </delete>
    <insert id="insert" parameterType="org.fh.entity.common.OvertimeDetailEntity">
    insert into biz_overtime_detail (timeout_detail_id, one_time, two_time, 
      three_time, emp_id, year_month
      )
    values (#{timeoutDetailId,jdbcType=VARCHAR}, #{oneTime,jdbcType=DOUBLE}, #{twoTime,jdbcType=DOUBLE}, 
      #{threeTime,jdbcType=DOUBLE}, #{empId,jdbcType=VARCHAR}, #{yearMonth,jdbcType=VARCHAR}
      )
  </insert>
    <insert id="insertSelective" parameterType="org.fh.entity.common.OvertimeDetailEntity">
        insert into biz_overtime_detail
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="timeoutDetailId != null">
                timeout_detail_id,
            </if>
            <if test="oneTime != null">
                one_time,
            </if>
            <if test="twoTime != null">
                two_time,
            </if>
            <if test="threeTime != null">
                three_time,
            </if>
            <if test="empId != null">
                emp_id,
            </if>
            <if test="yearMonth != null">
                year_month,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="timeoutDetailId != null">
                #{timeoutDetailId,jdbcType=VARCHAR},
            </if>
            <if test="oneTime != null">
                #{oneTime,jdbcType=DOUBLE},
            </if>
            <if test="twoTime != null">
                #{twoTime,jdbcType=DOUBLE},
            </if>
            <if test="threeTime != null">
                #{threeTime,jdbcType=DOUBLE},
            </if>
            <if test="empId != null">
                #{empId,jdbcType=VARCHAR},
            </if>
            <if test="yearMonth != null">
                #{yearMonth,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="org.fh.entity.common.OvertimeDetailEntity">
        update biz_overtime_detail
        <set>
            <if test="oneTime != null">
                one_time = #{oneTime,jdbcType=DOUBLE},
            </if>
            <if test="twoTime != null">
                two_time = #{twoTime,jdbcType=DOUBLE},
            </if>
            <if test="threeTime != null">
                three_time = #{threeTime,jdbcType=DOUBLE},
            </if>
            <if test="empId != null">
                emp_id = #{empId,jdbcType=VARCHAR},
            </if>
            <if test="yearMonth != null">
                year_month = #{yearMonth,jdbcType=VARCHAR},
            </if>
        </set>
        where timeout_detail_id = #{timeoutDetailId,jdbcType=VARCHAR}
    </update>
    <update id="updateByPrimaryKey" parameterType="org.fh.entity.common.OvertimeDetailEntity">
    update biz_overtime_detail
    set one_time = #{oneTime,jdbcType=DOUBLE},
      two_time = #{twoTime,jdbcType=DOUBLE},
      three_time = #{threeTime,jdbcType=DOUBLE},
      emp_id = #{empId,jdbcType=VARCHAR},
      year_month = #{yearMonth,jdbcType=VARCHAR}
    where timeout_detail_id = #{timeoutDetailId,jdbcType=VARCHAR}
  </update>

    <!-- 根据年月删除加班明细数据 -->
    <delete id="deleteOvertimeDetailByDate" parameterType="string">
        delete from biz_overtime_detail where `year_month` = #{yearMonth}
    </delete>

    <insert id="addOvertimeDetailList" parameterType="collection">
        insert into biz_overtime_detail
        (
          timeout_detail_id,one_time,two_time,three_time,emp_id,`year_month`,create_by,create_time
        )
        values
        <foreach collection="list" item="item" separator=",">
            (
              #{item.timeoutDetailId},#{item.oneTime},#{item.twoTime},#{item.threeTime},#{item.empId},#{item.yearMonth},#{item.createBy},now()
            )
        </foreach>
    </insert>

    <!-- 查询导入的加班数据汇总(车间) -->
    <select id="queryReportOvertimeWorkshop" parameterType="string" resultType="org.fh.entity.common.ReportOvertimeEntity">
        select cc.plant,cc.workshop,sum(one_time) as oneTime,sum(two_time) as twoTime,sum(three_time)as threeTime,count(od.emp_id) as count
         from biz_overtime_detail od
        left join biz_employee e on od.emp_id = e.emp_id
        left join biz_cost_center cc on cc.cost_center = e.cost_center
        left join biz_position p on p.direct = e.position
        where od.`year_month` = #{yearMonth}
        and cc.plant is not null
	    and cc.workshop is not null
	    and p.direct_name in(
	    <foreach collection="list" separator="," item="item">
            #{item}
        </foreach>
	    )
        group by cc.plant,cc.workshop
    </select>

    <!-- 查询导入的加班数据汇总(VSM) -->
    <select id="queryReportOvertimeVSM" parameterType="string" resultType="org.fh.entity.common.ReportOvertimeEntity">
        select cc.plant,cc.vsm as workshop,sum(one_time) as oneTime,sum(two_time) as twoTime,sum(three_time)as threeTime,count(od.emp_id) as count
        from biz_overtime_detail od
        left join biz_employee e on od.emp_id = e.emp_id
        left join biz_cost_center cc on cc.cost_center = e.cost_center
        left join biz_position p on p.direct = e.position
        where od.`year_month` = #{yearMonth}
        and cc.plant is not null
	    and cc.vsm is not null
        and p.direct_name in(
        <foreach collection="list" separator="," item="item">
            #{item}
        </foreach>
        )
        group by cc.plant,cc.vsm
    </select>
</mapper>