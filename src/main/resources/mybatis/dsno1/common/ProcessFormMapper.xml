<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.fh.mapper.dsno1.common.ProcessFormMapper">
    <resultMap id="BaseResultMap" type="org.fh.vo.common.ProcessFormVo">
        <id column="name" jdbcType="VARCHAR" property="name"/>
        <result column="emp_id" jdbcType="VARCHAR" property="empId"/>
        <result column="node_name" jdbcType="VARCHAR" property="nodeName"/>
        <result column="plant" jdbcType="VARCHAR" property="plant"/>
        <result column="workshop" jdbcType="VARCHAR" property="workshop"/>
        <result column="production_line" jdbcType="VARCHAR" property="productionLine"/>
        <result column="department" jdbcType="VARCHAR" property="department"/>
        <result column="work_team" jdbcType="VARCHAR" property="workTeam"/>
        <result column="work_type" jdbcType="VARCHAR" property="workType"/>
        <result column="apply_status" jdbcType="INTEGER" property="applyStatus"/>
        <result column="applyStatusName" jdbcType="INTEGER" property="applyStatusName"/>
        <result column="leave_type_id" jdbcType="VARCHAR" property="leaveTypeId"/>
        <result column="leaveTypeName" jdbcType="VARCHAR" property="leaveTypeName"/>
        <result column="apply_time" jdbcType="VARCHAR" property="applyTime"/>
        <result column="start_time" jdbcType="VARCHAR" property="startTime"/>
        <result column="end_time" jdbcType="VARCHAR" property="endTime"/>
        <result column="time_count" jdbcType="DECIMAL" property="timeCount"/>
        <result column="apply_detail" jdbcType="VARCHAR" property="applyDetail"/>
        <result column="approver" jdbcType="VARCHAR" property="approver"/>
        <result column="approverName" jdbcType="VARCHAR" property="approverName"/>
        <result column="processinstanceid" jdbcType="VARCHAR" property="processinstanceid"/>
        <result column="process_name" jdbcType="VARCHAR" property="processName"/>
        <result column="process_key" jdbcType="VARCHAR" property="processKey"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
    </resultMap>

    <sql id="Base_Column_List">
    id, emp_id, plant, workshop, production_line, department, work_team, work_type, apply_status,
    node_name, leave_type_id, apply_time, start_time, end_time, time_count, apply_detail,
    approver, processinstanceid, process_name, process_key,remark, revocation_time
  </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from biz_process_form
        where id = #{id,jdbcType=VARCHAR}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from biz_process_form
    where id = #{id,jdbcType=VARCHAR}
  </delete>
    <insert id="insert" parameterType="org.fh.entity.common.ProcessFormEntity">
    insert into biz_process_form (id, emp_id, plant,
      workshop, production_line, department,
      work_team, work_type, apply_status,
      node_name, leave_type_id, apply_time,
      start_time, end_time, time_count,
      apply_detail, approver, processinstanceid,
      process_name, remark, revocation_time
      )
    values (#{id,jdbcType=VARCHAR}, #{empId,jdbcType=VARCHAR}, #{plant,jdbcType=VARCHAR},
      #{workshop,jdbcType=VARCHAR}, #{productionLine,jdbcType=VARCHAR}, #{department,jdbcType=VARCHAR},
      #{workTeam,jdbcType=VARCHAR}, #{workType,jdbcType=VARCHAR}, #{applyStatus,jdbcType=INTEGER},
      #{nodeName,jdbcType=VARCHAR}, #{leaveTypeId,jdbcType=INTEGER}, #{applyTime,jdbcType=TIMESTAMP},
      #{startTime,jdbcType=TIMESTAMP}, #{endTime,jdbcType=TIMESTAMP}, #{timeCount,jdbcType=DECIMAL},
      #{applyDetail,jdbcType=VARCHAR}, #{approver,jdbcType=VARCHAR}, #{processinstanceid,jdbcType=VARCHAR},
      #{processName,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR}, #{revocationTime,jdbcType=TIMESTAMP}
      )
  </insert>

    <insert id="addProcessForm" parameterType="org.fh.entity.common.ProcessFormEntity">
        insert into biz_process_form
        <trim prefix="(" suffix=")" suffixOverrides=",">
                id,emp_id,plant,workshop,
            <if test="productionLine != null and productionLine != ''">
                production_line,
            </if>
            <if test="department != null and department != ''">
                department,
            </if>
            <if test="workTeam != null and workTeam != ''">
                work_team,
            </if>
            <if test="workType != null and workType != ''">
                work_type,
            </if>
                apply_status,node_name,
            <if test="leaveTypeId != null">
                leave_type_id,
            </if>
                apply_time,
            <if test="overtimeDate != null">
                overtime_date,
            </if>
            <if test="startTime != null">
                start_time,
            </if>
            <if test="endTime != null">
                end_time,
            </if>
            <if test="timeCount != null">
                time_count,
            </if>
            <if test="applyDetail != null and applyDetail != ''">
                apply_detail,
            </if>
            <if test="approver != null and approver != ''">
                approver,
            </if>
            <if test="approveUserName != null and approveUserName != ''">
                approve_user_name,
            </if>
                processinstanceid,process_name,process_key
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                #{id},#{empId},#{plant},#{workshop},
            <if test="productionLine != null and productionLine != ''">
                #{productionLine},
            </if>
            <if test="department != null and department != ''">
                #{department},
            </if>
            <if test="workTeam != null and workTeam != ''">
                #{workTeam},
            </if>
            <if test="workType != null and workType != ''">
                #{workType},
            </if>
            <if test="applyStatus != null">
                #{applyStatus},
            </if>
                #{nodeName},
            <if test="leaveTypeId != null">
                #{leaveTypeId},
            </if>
                now(),
            <if test="overtimeDate != null">
                #{overtimeDate},
            </if>
            <if test="startTime != null">
                #{startTime},
            </if>
            <if test="endTime != null">
                #{endTime},
            </if>
            <if test="timeCount != null">
                #{timeCount},
            </if>
            <if test="applyDetail != null and applyDetail != ''">
                #{applyDetail},
            </if>
            <if test="approver != null and approver != ''">
                #{approver},
            </if>
            <if test="approveUserName != null and approveUserName != ''">
                #{approveUserName},
            </if>
                #{processinstanceid},#{processName},#{processKey}
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="org.fh.entity.common.ProcessFormEntity">
        update biz_process_form
        <set>
            <if test="empId != null">
                emp_id = #{empId,jdbcType=VARCHAR},
            </if>
            <if test="plant != null">
                plant = #{plant,jdbcType=VARCHAR},
            </if>
            <if test="workshop != null">
                workshop = #{workshop,jdbcType=VARCHAR},
            </if>
            <if test="productionLine != null">
                production_line = #{productionLine,jdbcType=VARCHAR},
            </if>
            <if test="department != null">
                department = #{department,jdbcType=VARCHAR},
            </if>
            <if test="workTeam != null">
                work_team = #{workTeam,jdbcType=VARCHAR},
            </if>
            <if test="workType != null">
                work_type = #{workType,jdbcType=VARCHAR},
            </if>
            <if test="applyStatus != null">
                apply_status = #{applyStatus,jdbcType=INTEGER},
            </if>
            <if test="nodeName != null">
                node_name = #{nodeName,jdbcType=VARCHAR},
            </if>
            <if test="leaveTypeId != null">
                leave_type_id = #{leaveTypeId,jdbcType=INTEGER},
            </if>
            <if test="applyTime != null">
                apply_time = #{applyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="startTime != null">
                start_time = #{startTime,jdbcType=TIMESTAMP},
            </if>
            <if test="endTime != null">
                end_time = #{endTime,jdbcType=TIMESTAMP},
            </if>
            <if test="timeCount != null">
                time_count = #{timeCount,jdbcType=DECIMAL},
            </if>
            <if test="applyDetail != null">
                apply_detail = #{applyDetail,jdbcType=VARCHAR},
            </if>
            <if test="approver != null">
                approver = #{approver,jdbcType=VARCHAR},
            </if>
            <if test="processinstanceid != null">
                processinstanceid = #{processinstanceid,jdbcType=VARCHAR},
            </if>
            <if test="processName != null">
                process_name = #{processName,jdbcType=VARCHAR},
            </if>
            <if test="remark != null">
                remark = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="revocationTime != null">
                revocation_time = #{revocationTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=VARCHAR}
    </update>

    <!-- 单个修改流程记录节点 -->
    <update id="updateProcessFormNodeName" parameterType="org.fh.entity.common.ProcessFormEntity">
        update biz_process_form
        <set>
            <if test="approver !=null and approver != ''">
                approver = #{approver},
            </if>
            <if test="nodeName !=null and nodeName != ''">
                node_name = #{nodeName}
            </if>
        </set>
        where processinstanceid = #{processinstanceid}
    </update>

    <update id="updateProcessFormNodeNameList" parameterType="collection">
        <foreach collection="list" item="item" separator=";">
            update biz_process_form set node_name = #{item.nodeName},approver = #{item.approver} where processinstanceid = #{item.processinstanceid}
        </foreach>
    </update>

    <!-- 批量撤销流程记录 -->
    <update id="revocationProcessList" parameterType="collection">
        <foreach collection="list" item="item" separator=";">
            update biz_process_form set node_name = #{item.nodeName},remark = #{item.remark},operator = #{item.operator},revocation_time = now() where processinstanceid = #{item.processinstanceid}
        </foreach>
    </update>

    <update id="updateProcessStatus" parameterType="collection">
        <foreach collection="list" item="item">
            update biz_leave set status = '9' where processinstanceid = #{item.processinstanceid};
            update biz_overtime_apply set status = '9' where processinstanceid = #{item.processinstanceid};
            update biz_replenish_attendance set status = '9' where processinstanceid = #{item.processinstanceid};
            update biz_borrow set status = '9' where processinstanceid = #{item.processinstanceid};
        </foreach>
    </update>


    <!-- 分页查询流程记录 -->
    <select id="queryProcessFormPage" resultMap="BaseResultMap" parameterType="org.fh.entity.common.ProcessFormEntity">
        select p.id,e.`name`,p.emp_id, p.plant,p.workshop, p.production_line, p.department,p.work_team, p.work_type, p.apply_status,
        p.node_name, p.leave_type_id,t.leave_type_name as leaveTypeName, p.apply_time,p.start_time, p.end_time, p.time_count,p.apply_detail, p.approver,ee.`name` as approverName,
        p.process_key,
        p.processinstanceid,p.approve_user_name as process_name, p.remark, p.revocation_time,
        case
        when p.apply_status = '1' then '请假'
        when p.apply_status = '2' then '加班'
        when p.apply_status = '3' then '补卡'
        when p.apply_status = '4' then '借调'
        end as applyStatusName
        from biz_process_form p
        left join biz_employee e on p.emp_id = e.emp_id
        left join biz_employee ee on ee.emp_id = p.approver
        left join biz_leave_type t on t.leave_type_id = p.leave_type_id
        <where>
            <if test="name != null and name !=''">
                and e.`name` like '%${name}%'
            </if>
            <if test="empId != null and empId !=''">
                and p.emp_id like '%${empId}%'
            </if>
            <if test="plant != null and plant !=''">
                and p.plant = #{plant}
            </if>
            <if test="workshop != null and workshop !=''">
                and p.workshop = #{workshop}
            </if>
            <if test="nodeName != null and nodeName !=''">
                and p.node_name like '%${nodeName}%'
            </if>
            <if test="productionLine != null and productionLine !=''">
                and p.production_line = #{productionLine}
            </if>
            <if test="department != null and department !=''">
                and p.department = #{department}
            </if>
            <if test="workTeam != null and workTeam !=''">
                and p.work_team = #{workTeam}
            </if>
            <if test="workType != null and workType !=''">
                and p.work_type = #{workType}
            </if>
            <if test="applyStartTime != null and applyStartTime !=''">
                and DATE_FORMAT(p.apply_time,'%Y-%m-%d') &gt;= #{applyStartTime}
            </if>
            <if test="applyEndTime != null and applyEndTime !=''">
                and DATE_FORMAT(p.apply_time,'%Y-%m-%d') &lt;= #{applyEndTime}
            </if>
            <if test="applyStatus != null and applyStatus !=''">
                and p.apply_status = #{applyStatus}
            </if>
        </where>
        order by p.apply_time desc
    </select>

    <!-- 导出审核记录 -->
    <select id="exportProcessForm" resultType="org.fh.model.exportModel.ProcessFormExportModel" parameterType="org.fh.entity.common.ProcessFormEntity">
        select p.id,e.`name`,p.emp_id as empId, p.plant,p.workshop, p.production_line as productionLine, p.department,p.work_team as workTeam, p.work_type as workType, p.apply_status as applyStatus,
        p.node_name as nodeName, p.leave_type_id,t.leave_type_name as leaveTypeName, p.apply_time as applyTime,p.start_time as startTime, p.end_time as endTime, p.time_count as timeCount,
        p.apply_detail as applyDetail, p.approver,ee.`name` as approverName,p.process_key,p.operator,
        concat(p.operator,'|',eee.`name`) as operatorUser,
        p.processinstanceid,p.approve_user_name as processName, p.remark, p.revocation_time as revocationTime,
        case
        when p.apply_status = '1' then '请假'
        when p.apply_status = '2' then '加班'
        when p.apply_status = '3' then '补卡'
        when p.apply_status = '4' then '借调'
        end as applyStatusName
        from biz_process_form p
        left join biz_employee e on p.emp_id = e.emp_id
        left join biz_employee ee on ee.emp_id = p.approver
        left join biz_employee eee on eee.emp_id = p.operator
        left join biz_leave_type t on t.leave_type_id = p.leave_type_id
        <where>
            <if test="name != null and name !=''">
                and e.`name` like '%${name}%'
            </if>
            <if test="empId != null and empId !=''">
                and p.emp_id like '%${empId}%'
            </if>
            <if test="plant != null and plant !=''">
                and p.plant = #{plant}
            </if>
            <if test="workshop != null and workshop !=''">
                and p.workshop = #{workshop}
            </if>
            <if test="nodeName != null and nodeName !=''">
                and p.node_name like '%${nodeName}%'
            </if>
            <if test="productionLine != null and productionLine !=''">
                and p.production_line = #{productionLine}
            </if>
            <if test="department != null and department !=''">
                and p.department = #{department}
            </if>
            <if test="workTeam != null and workTeam !=''">
                and p.work_team = #{workTeam}
            </if>
            <if test="workType != null and workType !=''">
                and p.work_type = #{workType}
            </if>
            <if test="applyStartTime != null and applyStartTime !=''">
                and DATE_FORMAT(p.apply_time,'%Y-%m-%d') &gt;= #{applyStartTime}
            </if>
            <if test="applyEndTime != null and applyEndTime !=''">
                and DATE_FORMAT(p.apply_time,'%Y-%m-%d') &lt;= #{applyEndTime}
            </if>
            <if test="applyStatus != null and applyStatus !=''">
                and p.apply_status = #{applyStatus}
            </if>
        </where>
        order by p.apply_time desc
    </select>

    <select id="queryProcessFormByList" parameterType="collection" resultType="org.fh.entity.common.ProcessFormEntity">
        select p.id,e.`name`,p.emp_id as empId, p.plant,p.workshop, p.production_line, p.department,p.work_team, p.work_type, p.apply_status,
        p.node_name, p.leave_type_id,t.leave_type_name as leaveTypeName, p.apply_time,p.start_time, p.end_time, p.time_count,p.apply_detail, p.approver,ee.`name` as approverName,
        p.process_key,
        p.processinstanceid,p.process_name, p.remark, p.revocation_time
        from biz_process_form p
        left join biz_employee e on p.emp_id = e.emp_id
        left join biz_employee ee on ee.emp_id = p.approver
        left join biz_leave_type t on t.leave_type_id = p.leave_type_id
        where p.processinstanceid in
        <foreach collection="list" separator="," close=")" open="(" item="item">
            #{item}
        </foreach>
    </select>

    <select id="queryApproveNoticeList" resultType="org.fh.dto.common.processForm.ApproveNoticeDto">
        SELECT
        b.apply_status AS applyType,
        b.approver,
        COUNT(*) AS total
        FROM
        biz_process_form b
        LEFT JOIN (
        SELECT
        PROC_INST_ID_,
        MAX(START_TIME_) AS original_start_time,
        DATE_ADD(MAX(START_TIME_), INTERVAL 8 HOUR) AS adjusted_start_time
        FROM
        act_hi_taskinst
        WHERE
        START_TIME_  <![CDATA[>=]]> #{startDateAjusted}
        AND START_TIME_  <![CDATA[<]]> #{endDateAjusted}
        AND END_TIME_ is null
        GROUP BY PROC_INST_ID_) max_task ON b.processinstanceid = max_task.PROC_INST_ID_ WHERE b.apply_time  <![CDATA[>=]]> #{startDate}
        AND b.apply_time  <![CDATA[<]]> #{endDate}
        AND b.node_name NOT IN ('已取消', '已完成', '已撤销', '被驳回')
        AND max_task.adjusted_start_time  <![CDATA[<=]]> DATE_SUB(NOW(), INTERVAL 3 DAY)
        GROUP BY
        b.approver,
        b.apply_status
    </select>

    <select id="sumEmpUnfinishedLeaveApply" parameterType="pd" resultType="double">
        SELECT COALESCE(sum(time_count), 0) as apply
        FROM biz_process_form
        WHERE emp_id = #{empId}
          AND apply_status = 1
          AND leave_type_id = #{leaveTypeId}
          AND node_name <![CDATA[<>]]> '已完成'
          AND node_name <![CDATA[<>]]> '已取消'
          AND node_name <![CDATA[<>]]> '被驳回'
          AND node_name <![CDATA[<>]]> '已撤销'
    </select>

</mapper>