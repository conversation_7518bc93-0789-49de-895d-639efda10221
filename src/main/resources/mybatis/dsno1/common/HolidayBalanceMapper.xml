<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.fh.mapper.dsno1.common.HolidayBalanceMapper">

  <!--表名 -->
  <sql id="tableName">
    BIZ_HOLIDAY_BALANCE
  </sql>

  <!--数据字典表名 -->
  <sql id="dicTableName">
    SYS_DICTIONARIES
  </sql>

  <!-- 字段 -->
  <sql id="Field">
    f.HOLIDAY_BALANCE_ID,
    f.LOCATION,
	f.EMP_ID,
    f.NAME,
    f.ACTIVED,
    f.HOLIDAY_TYPE,
    f.HOLIDAY_YEAR,
    f.VALID_FROM,
    f.VALID_TO,
    f.TOTAL,
    f.<PERSON>VA<PERSON>,
    f.USED,
    f.MONTH_TOTAL,
    f.MONTH_AVAILABLE,
    f.UOM,
    f.UPDATE_TIME
  </sql>

  <!-- 字段用于新增 -->
  <sql id="Field2">
    HOLIDAY_BALANCE_ID,
    LOCATION,
	EMP_ID,
    NAME,
    ACTIVED,
    HOLIDAY_TYPE,
    HOLIDAY_YEAR,
    VALID_FROM,
    VALID_TO,
    TOTAL,
    AVAILABLE,
    USED,
    MONTH_TOTAL,
    MONTH_AVAILABLE,
    UOM,
    UPDATE_TIME
  </sql>

  <!-- 字段值 -->
  <sql id="FieldValue">
    #{HOLIDAY_BALANCE_ID},
    #{LOCATION},
    #{EMP_ID},
    #{NAME},
    #{ACTIVED},
    #{HOLIDAY_TYPE},
    #{HOLIDAY_YEAR},
    #{VALID_FROM},
    #{VALID_TO},
    #{TOTAL},
    #{AVAILABLE},
    #{USED},
    #{MONTH_TOTAL},
    #{MONTH_AVAILABLE},
    #{UOM},
    #{UPDATE_TIME}
  </sql>

  <!-- 新增-->
  <insert id="save" parameterType="pd">
    insert into
    <include refid="tableName"></include>
    (
    <include refid="Field2"></include>
    ) values (
    <include refid="FieldValue"></include>
    )
  </insert>

  <!-- 删除-->
  <delete id="delete" parameterType="pd">
    delete from
    <include refid="tableName"></include>
    where
    HOLIDAY_BALANCE_ID = #{HOLIDAY_BALANCE_ID}
  </delete>

  <!-- 修改 -->
  <update id="edit" parameterType="pd">
    update
    <include refid="tableName"></include>
    set
    LOCATION = #{LOCATION},
    EMP_ID = #{EMP_ID},
    NAME = #{NAME},
    ACTIVED = #{ACTIVED},
    HOLIDAY_TYPE = #{HOLIDAY_TYPE},
    HOLIDAY_YEAR = #{HOLIDAY_YEAR},
    VALID_FROM = #{VALID_FROM},
    VALID_TO = #{VALID_TO},
    TOTAL = #{TOTAL},
    AVAILABLE = #{AVAILABLE},
    USED = #{USED},
    UOM = #{UOM},
    MONTH_TOTAL = #{MONTH_TOTAL},
    MONTH_AVAILABLE = #{MONTH_AVAILABLE},
    UPDATE_TIME = #{UPDATE_TIME}
    where
    HOLIDAY_BALANCE_ID = #{HOLIDAY_BALANCE_ID}
  </update>

  <!-- 通过ID获取数据 -->
  <select id="findById" parameterType="pd" resultType="pd">
    select
    <include refid="Field"></include>
    from
    <include refid="tableName"></include>
    f
    where
    f.HOLIDAY_BALANCE_ID = #{HOLIDAY_BALANCE_ID}
  </select>

  <!-- 列表 -->
  <select id="datalistPage" parameterType="page" resultType="pd">
    select
    <include refid="Field"></include>
    from
    <include refid="tableName"></include>
    f
    where 1=1
    <if test="pd.KEYWORDS != null and pd.KEYWORDS != ''"><!-- 关键词检索 -->
      and
      (
      <!--	根据需求自己加检索条件
        字段1 LIKE CONCAT(CONCAT('%', #{pd.KEYWORDS}),'%')
         or
        字段2 LIKE CONCAT(CONCAT('%', #{pd.KEYWORDS}),'%')
      -->
      )
    </if>
  </select>

  <!-- 列表(全部) -->
  <select id="listAll" parameterType="pd" resultType="pd">
    select
    <include refid="Field"></include>
    from
    <include refid="tableName"></include>
    f
  </select>

  <!-- 批量删除 -->
  <delete id="deleteAll" parameterType="String">
    delete from
    <include refid="tableName"></include>
    where
    HOLIDAY_BALANCE_ID in
    <foreach item="item" index="index" collection="array" open="(" separator="," close=")">
      #{item}
    </foreach>
  </delete>

  <select id="queryPage" parameterType="org.fh.dto.common.holiday.HolidayBalanceQueryDto"
    resultType="org.fh.vo.common.HolidayBalanceVo">
    SELECT
    hb.holiday_balance_id holidayBalanceId,
    hb.emp_id empId,
    hb.NAME,
    hb.actived,
    hb.holiday_type holidayType,
    hb.holiday_year holidayYear,
    hb.valid_from validFrom,
    hb.valid_to validTo,
    hb.total total,
    hb.available available,
    hb.used used,
    hb.uom uom,
    hb.update_time updateTime,
    hb.month_total monthTotal,
    hb.month_available monthAvailable
    FROM
    biz_holiday_balance hb
    left join biz_employee e on hb.emp_id = e.emp_id
    <where>
      <if test="name != null and name !=''">
        and hb.name like '%${name}%'
      </if>
      <if test="empId != null and empId !=''">
        and hb.emp_id = #{empId}
      </if>
      <if test="holidayType != null and holidayType !=''">
        and hb.holiday_type = #{holidayType}
      </if>
      <if test="startTime != null and startTime !='' and endTime != null and endTime !=''">
        and hb.update_time <![CDATA[>=]]> #{startTime} and hb.update_time <![CDATA[<=]]> #{endTime}
      </if>
      <if test="workTeam != null and workTeam !=''">
        and e.work_team = #{workTeam}
      </if>
    </where>
    order by hb.update_time desc
  </select>


  <select id="findByPd" parameterType="pd" resultType="org.fh.vo.common.HolidayBalanceVo">
    select holiday_balance_id holidayBalanceId, location, emp_id empId, name, actived, holiday_type holidayType,
    holiday_year holidayYear, valid_from validFrom, valid_to validTo, total total, available
    available, used used, month_total monthTotal, month_available monthAvailable, uom uom,update_time updateTime
    from
    <include refid="tableName"></include>
    f
    where emp_id = #{emp_id} and holiday_type = #{holiday_type} and holiday_year = #{holiday_year}
    limit 1
  </select>


</mapper>