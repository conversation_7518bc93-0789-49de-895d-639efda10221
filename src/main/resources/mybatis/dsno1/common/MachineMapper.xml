<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.fh.mapper.dsno1.common.MachineMapper">
    <resultMap id="BaseResultMap" type="org.fh.vo.common.MachineVo">
        <id column="machine_id" jdbcType="VARCHAR" property="machineId"/>
        <result column="machine_location" jdbcType="VARCHAR" property="machineLocation"/>
        <result column="machine_name" jdbcType="VARCHAR" property="machineName"/>
        <result column="machine_ip" jdbcType="VARCHAR" property="machineIp"/>
        <result column="machine_mac" jdbcType="VARCHAR" property="machineMac"/>
        <result column="machine_sn" jdbcType="VARCHAR" property="machineSn"/>
        <result column="plant" jdbcType="VARCHAR" property="plant"/>
        <result column="created_by" jdbcType="VARCHAR" property="createdBy"/>
        <result column="created_time" jdbcType="VARCHAR" property="createdTime"/>
        <result column="update_by" jdbcType="VARCHAR" property="updateBy"/>
        <result column="update_time" jdbcType="VARCHAR" property="updateTime"/>
    </resultMap>
    <sql id="Base_Column_List">
    machine_id, machine_location, machine_name, machine_ip, machine_mac, machine_sn, 
    deleted, created_by, created_time, update_by, update_time
  </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from biz_machine
        where machine_id = #{machineId,jdbcType=VARCHAR}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from biz_machine
    where machine_id = #{machineId,jdbcType=VARCHAR}
  </delete>
    <insert id="insert" parameterType="org.fh.entity.common.MachineEntity">
    insert into biz_machine (machine_id, machine_location, machine_name, 
      machine_ip, machine_mac, machine_sn, 
      deleted, created_by, created_time, 
      update_by, update_time)
    values (#{machineId,jdbcType=VARCHAR}, #{machineLocation,jdbcType=VARCHAR}, #{machineName,jdbcType=VARCHAR}, 
      #{machineIp,jdbcType=VARCHAR}, #{machineMac,jdbcType=VARCHAR}, #{machineSn,jdbcType=VARCHAR}, 
      #{deleted,jdbcType=TINYINT}, #{createdBy,jdbcType=VARCHAR}, #{createdTime,jdbcType=TIMESTAMP}, 
      #{updateBy,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP})
  </insert>
    <insert id="insertSelective" parameterType="org.fh.entity.common.MachineEntity">
        insert into biz_machine
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="machineId != null">
                machine_id,
            </if>
            <if test="machineLocation != null">
                machine_location,
            </if>
            <if test="machineName != null">
                machine_name,
            </if>
            <if test="machineIp != null">
                machine_ip,
            </if>
            <if test="machineMac != null">
                machine_mac,
            </if>
            <if test="machineSn != null">
                machine_sn,
            </if>
            <if test="deleted != null">
                deleted,
            </if>
            <if test="createdBy != null">
                created_by,
            </if>
            <if test="createdTime != null">
                created_time,
            </if>
            <if test="updateBy != null">
                update_by,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="machineId != null">
                #{machineId,jdbcType=VARCHAR},
            </if>
            <if test="machineLocation != null">
                #{machineLocation,jdbcType=VARCHAR},
            </if>
            <if test="machineName != null">
                #{machineName,jdbcType=VARCHAR},
            </if>
            <if test="machineIp != null">
                #{machineIp,jdbcType=VARCHAR},
            </if>
            <if test="machineMac != null">
                #{machineMac,jdbcType=VARCHAR},
            </if>
            <if test="machineSn != null">
                #{machineSn,jdbcType=VARCHAR},
            </if>
            <if test="deleted != null">
                #{deleted,jdbcType=TINYINT},
            </if>
            <if test="createdBy != null">
                #{createdBy,jdbcType=VARCHAR},
            </if>
            <if test="createdTime != null">
                #{createdTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateBy != null">
                #{updateBy,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="org.fh.entity.common.MachineEntity">
        update biz_machine
        <set>
            <if test="machineLocation != null">
                machine_location = #{machineLocation,jdbcType=VARCHAR},
            </if>
            <if test="machineName != null">
                machine_name = #{machineName,jdbcType=VARCHAR},
            </if>
            <if test="machineIp != null">
                machine_ip = #{machineIp,jdbcType=VARCHAR},
            </if>
            <if test="machineMac != null">
                machine_mac = #{machineMac,jdbcType=VARCHAR},
            </if>
            <if test="machineSn != null">
                machine_sn = #{machineSn,jdbcType=VARCHAR},
            </if>
            <if test="deleted != null">
                deleted = #{deleted,jdbcType=TINYINT},
            </if>
            <if test="createdBy != null">
                created_by = #{createdBy,jdbcType=VARCHAR},
            </if>
            <if test="createdTime != null">
                created_time = #{createdTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateBy != null">
                update_by = #{updateBy,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where machine_id = #{machineId,jdbcType=VARCHAR}
    </update>
    <update id="updateByPrimaryKey" parameterType="org.fh.entity.common.MachineEntity">
    update biz_machine
    set machine_location = #{machineLocation,jdbcType=VARCHAR},
      machine_name = #{machineName,jdbcType=VARCHAR},
      machine_ip = #{machineIp,jdbcType=VARCHAR},
      machine_mac = #{machineMac,jdbcType=VARCHAR},
      machine_sn = #{machineSn,jdbcType=VARCHAR},
      deleted = #{deleted,jdbcType=TINYINT},
      created_by = #{createdBy,jdbcType=VARCHAR},
      created_time = #{createdTime,jdbcType=TIMESTAMP},
      update_by = #{updateBy,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where machine_id = #{machineId,jdbcType=VARCHAR}
  </update>

    <insert id="addMachine" parameterType="org.fh.entity.common.MachineEntity">
        insert into biz_machine
        <trim prefix="(" suffix=")" suffixOverrides=",">
            machine_id,
            <if test="machineLocation != null and machineLocation !=''">
                machine_location,
            </if>
            <if test="machineName != null and machineName !=''">
                machine_name,
            </if>
            <if test="machineIp != null and machineIp !=''">
                machine_ip,
            </if>
            <if test="machineMac != null and machineMac !=''">
                machine_mac,
            </if>
            <if test="machineSn != null and machineSn !=''">
                machine_sn,
            </if>
            deleted,
            <if test="createdBy != null and createdBy !=''">
                created_by,
            </if>
            plant,
            created_time
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            #{machineId,jdbcType=VARCHAR},
            <if test="machineLocation != null and machineLocation !=''">
                #{machineLocation,jdbcType=VARCHAR},
            </if>
            <if test="machineName != null and machineName !=''">
                #{machineName,jdbcType=VARCHAR},
            </if>
            <if test="machineIp != null and machineIp !=''">
                #{machineIp,jdbcType=VARCHAR},
            </if>
            <if test="machineMac != null and machineMac !=''">
                #{machineMac,jdbcType=VARCHAR},
            </if>
            <if test="machineSn != null and machineSn !=''">
                #{machineSn,jdbcType=VARCHAR},
            </if>
            0,
            <if test="createdBy != null and createdBy !=''">
                #{createdBy,jdbcType=VARCHAR},
            </if>
            #{plant},
            now()
        </trim>
    </insert>

    <select id="queryMachineSnValid" parameterType="org.fh.entity.common.MachineEntity" resultType="int">
        select count(*) from biz_machine where machine_sn = #{machineSn}
        <if test="machineId !=null and machineId !=''">
            and machine_id != #{machineId}
        </if>
        and deleted = '0'
    </select>

    <update id="deleteMachineData" parameterType="string">
    update biz_machine set deleted = '1' where machine_id = #{machineId}
  </update>

    <!-- 单个修改考勤机信息 -->
    <update id="updateMachine" parameterType="org.fh.entity.common.MachineEntity">
        update biz_machine
        <set>
            <if test="machineName != null and machineName !=''">
                machine_name = #{machineName},
            </if>
            <if test="machineLocation != null and machineLocation !=''">
                machine_location = #{machineLocation},
            </if>
            <if test="machineMac != null and machineMac !=''">
                machine_mac = #{machineMac},
            </if>
            <if test="machineIp != null and machineIp !=''">
                machine_ip = #{machineIp},
            </if>
            <if test="machineSn != null and machineSn !=''">
                machine_sn = #{machineSn},
            </if>
            <if test="updateBy != null and updateBy !=''">
                update_by = #{updateBy},
            </if>
            <if test="plant != null and plant !=''">
                plant = #{plant},
            </if>
            update_time = now()
        </set>
        where machine_id = #{machineId}
    </update>

    <!-- 根据Id查询考勤机详细信息 -->
    <select id="queryMachineDetail" parameterType="string" resultMap="BaseResultMap">
        select machine_id, machine_location, machine_name, machine_ip, machine_mac, machine_sn,created_by, created_time, update_by, update_time,plant
        from biz_machine
        where machine_id = #{machineId}
    </select>

    <!-- 分页查询考勤机信息 -->
    <select id="queryMachinePage" parameterType="org.fh.entity.common.MachineEntity" resultMap="BaseResultMap">
        select machine_id, machine_location, machine_name, machine_ip, machine_mac, machine_sn,created_by, created_time, update_by, update_time,plant
        from biz_machine
        <where>
            <if test="machineName != null and machineName !=''">
                and machine_name like '%${machineName}%'
            </if>
            <if test="machineLocation != null and machineLocation !=''">
                and machine_location = #{machineLocation}
            </if>
            <if test="machineMac != null and machineMac !=''">
                and machine_mac = #{machineMac}
            </if>
            <if test="plant != null and plant !=''">
                and plant = #{plant}
            </if>
            and deleted = '0'
        </where>
    </select>

    <!-- 查询所有考勤机序号 -->
    <select id="queryMachineSnAll" resultType="string">
        select distinct machine_sn from biz_machine where deleted = 0
    </select>

    <!-- 根据厂区查询对应的考勤机信息 -->
    <select id="queryMachineListByPlant" resultMap="BaseResultMap" parameterType="string">
         select machine_id, machine_location, machine_name, machine_ip, machine_mac, machine_sn,created_by, created_time, update_by, update_time,plant
        from biz_machine
        where plant = #{plant}
        and deleted = '0'
    </select>

    <!-- 查询所有考勤机 -->
    <select id="queryMachineAll" resultMap="BaseResultMap">
        select machine_id, machine_location, machine_name, machine_ip, machine_mac, machine_sn,plant,created_by, created_time, update_by, update_time,plant
        from biz_machine
        where deleted = '0'
        order by plant desc
    </select>
</mapper>