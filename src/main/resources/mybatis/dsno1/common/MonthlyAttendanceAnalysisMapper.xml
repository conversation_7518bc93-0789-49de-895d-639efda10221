<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.fh.mapper.dsno1.common.MonthlyAttendanceAnalysisMapper">

  <!--表名 -->
  <sql id="tableName">
    BIZ_MONTHLY_ATTENDANCE_ANALYSIS
  </sql>

  <!--数据字典表名 -->
  <sql id="dicTableName">
    SYS_DICTIONARIES
  </sql>

  <!-- 字段 -->
  <sql id="Field">
    F.MONTHLY_ATTENDANCE_ANALYSIS_ID,
		F.TERM,	
		F.EMP_ID,	
		F.NAME,
		F.GROUP_ID,	
		F.COMP_ID,	
		F.LINE_ID,
		F.PNHR,
		F.<PERSON>,
		<PERSON><PERSON>,
		<PERSON><PERSON>,
		<PERSON><PERSON>,
		<PERSON><PERSON>,
		<PERSON><PERSON>,
		<PERSON><PERSON>,
		<PERSON><PERSON>,
		<PERSON><PERSON>,
		<PERSON><PERSON>2,
		<PERSON><PERSON>3,
		<PERSON><PERSON>,
		<PERSON><PERSON>15,
		<PERSON>.AN20,
		F.ATTF,
		F.ATOF,
		F.PERL,
		F.OCCL,
		F.OTCL,
		F.ANNL,
		F.ANST,
		F.ANWE,
		F.PSKL,
		F.BPSL,
		F.ISJL,
		F.COML,
		F.FANF,
		F.MARL,
		F.MLPL,
		F.PATL,
		F.MATL,
		F.DSFL,
		F.BRFL,
		F.CIFL,
		F.FPGL,
		F.MSCL,
		F.POFL,
        F.LADL,
        F.OTHL1,
        F.CHILDL,
        F.NURSL,
        F.NBOS,
        F.LSCH,
		F.CREATE_TIME
  </sql>

  <!-- 字段用于新增 -->
  <sql id="Field2">
      MONTHLY_ATTENDANCE_ANALYSIS_ID,
		TERM,	
		EMP_ID,	
		NAME,
		GROUP_ID,	
		COMP_ID,	
		LINE_ID,
		PNHR,
		WKHR,
		ADHR,
		CFHR,
		WHSV,
		UAHR,
		REWK,
		LIMN,
		EOMN,
		OT1,
		OT2,
		OT3,
		CLOT1,
		AN15,
		AN20,
		ATTF,
		ATOF,
		PERL,
		OCCL,
		OTCL,
		ANNL,
		ANST,
		ANWE,
		PSKL,
		BPSL,
		ISJL,
		COML,
		FANF,
		MARL,
		MLPL,
		PATL,
		MATL,
		DSFL,
		BRFL,
		CIFL,
		FPGL,
		MSCL,
		POFL,
        LADL,
        OTHL1,
        CHILDL,
        NURSL,
        NBOS,
        LSCH,
		CREATE_TIME
  </sql>

  <!-- 字段值 -->
  <sql id="FieldValue">
      #{MONTHLY_ATTENDANCE_ANALYSIS_ID},
      #{TERM},
      #{EMP_ID},
      #{NAME},
      #{GROUP_ID},
      #{COMP_ID},
      #{LINE_ID},
      #{PNHR},
      #{WKHR},
      #{ADHR},
      #{CFHR},
      #{WHSV},
      #{UAHR},
      #{REWK},
      #{LIMN},
      #{EOMN},
      #{OT1},
      #{OT2},
      #{OT3},
      #{CLOT1},
      #{AN15},
      #{AN20},
      #{ATTF},
      #{ATOF},
      #{PERL},
      #{OCCL},
      #{OTCL},
      #{ANNL},
      #{ANST},
      #{ANWE},
      #{PSKL},
      #{BPSL},
      #{ISJL},
      #{COML},
      #{FANF},
      #{MARL},
      #{MLPL},
      #{PATL},
      #{MATL},
      #{DSFL},
      #{BRFL},
      #{CIFL},
      #{FPGL},
      #{MSCL},
      #{POFL},
      #{LADL},
      #{OTHL1},
      #{CHILDL},
      #{NURSL},
      #{NBOS},
      #{LSCH},
      now()
  </sql>

  <sql id="FieldValue2">
    #{monthlyAttendanceAnalysisId},
    #{term},
    #{empId},
    #{name},
    #{groupId},
    #{compId},
    #{lineId},
    #{pnhr},
    #{wkhr},
    #{adhr},
    #{cfhr},
    #{whsv},
    #{uahr},
    #{rewk},
    #{limn},
    #{eomn},
    #{ot1},
    #{ot2},
    #{ot3},
    #{clot1},
    #{an15},
    #{an20},
    #{attf},
    #{atof},
    #{perl},
    #{occl},
    #{otcl},
    #{annl},
    #{anst},
    #{anwe},
    #{pskl},
    #{bpsl},
    #{isjl},
    #{coml},
    #{fanf},
    #{marl},
    #{mlpl},
    #{patl},
    #{matl},
    #{dsfl},
    #{brfl},
    #{cifl},
    #{fpgl},
    #{mscl},
    #{pofl},
    #{ladl},
    #{othl1},
    #{childl},
    #{nursl},
    #{nbos},
    #{lsch},
    now()
  </sql>

  <!-- 新增-->
  <insert id="save" parameterType="pd">
    insert into
    <include refid="tableName"></include>
    (
    <include refid="Field2"></include>
    ) values (
    <include refid="FieldValue"></include>
    )
  </insert>

  <insert id="upload" parameterType="org.fh.vo.common.MonthlyAttendanceAnalysisVO">
    insert into
    <include refid="tableName"></include>
    (
    <include refid="Field2"></include>
    ) values (
    <include refid="FieldValue2"></include>
    )
  </insert>

  <!-- 删除-->
  <delete id="delete" parameterType="pd">
    delete from
    <include refid="tableName"></include>
    where
    MONTHLY_ATTENDANCE_ANALYSIS_ID = #{MONTHLY_ATTENDANCE_ANALYSIS_ID}
  </delete>

  <!-- 修改 -->
  <update id="edit" parameterType="pd">
    update
    <include refid="tableName"></include>
    set
    MONTHLY_ATTENDANCE_ANALYSIS_ID = #{MONTHLY_ATTENDANCE_ANALYSIS_ID},
    TERM = #{TERM},
    EMP_ID = #{EMP_ID},
    NAME = #{NAME},
    CONSTCENTER = #{CONSTCENTER},
    GROUP_ID = #{GROUP_ID},
    COMP_ID = #{COMP_ID},
    LINE_ID = #{LINE_ID},
    EMP_KIND = #{EMP_KIND},
    ATTEND_EMP = #{ATTEND_EMP},
    PNHR = #{PNHR},
    WKHR = #{WKHR},
    ADHR = #{ADHR},
    CFHR = #{CFHR},
    WHSV = #{WHSV},
    UAHR = #{UAHR},
    REWK = #{REWK},
    LIMN = #{LIMN},
    EOMN = #{EOMN},
    OT1 = #{OT1},
    OT2 = #{OT2},
    OT3 = #{OT3},
    CLOT1 = #{CLOT1},
    AN15 = #{AN15},
    AN20 = #{AN20},
    ATTF = #{ATTF},
    ATOF = #{ATOF},
    PERL = #{PERL},
    OCCL = #{OCCL},
    OTCL = #{OTCL},
    ANNL = #{ANNL},
    ANST = #{ANST},
    ANWE = #{ANWE},
    PSKL = #{PSKL},
    BPSL = #{BPSL},
    ISJL = #{ISJL},
    COML = #{COML},
    FANF = #{FANF},
    MARL = #{MARL},
    MLPL = #{MLPL},
    PATL = #{PATL},
    MATL = #{MATL},
    DSFL = #{DSFL},
    BRFL = #{BRFL},
    CIFL = #{CIFL},
    FPGL = #{FPGL},
    MSCL = #{MSCL},
    POFL = #{POFL},
    LADL = #{LADL},
    OTHL1 = #{OTHL1},
    CHILDL = #{CHILDL},
    NURSL = #{NURSL},
    NBOS = #{NBOS},
    LSCH = #{LSCH}
    where
    MONTHLY_ATTENDANCE_ANALYSIS_ID = #{MONTHLY_ATTENDANCE_ANALYSIS_ID}
  </update>


  <update id="update" parameterType="org.fh.vo.common.MonthlyAttendanceAnalysisVO">
    update
    <include refid="tableName"></include>
    set
      PNHR = #{pnhr},
      WKHR = #{wkhr},
      ADHR = #{adhr},
      CFHR = #{cfhr},
      WHSV = #{whsv},
      UAHR = #{uahr},
      REWK = #{rewk},
      LIMN = #{limn},
      EOMN = #{eomn},
      OT1 = #{ot1},
      OT2 = #{ot2},
      OT3 = #{ot3},
      CLOT1 = #{clot1},
      AN15 = #{an15},
      AN20 = #{an20},
      ATTF = #{attf},
      ATOF = #{atof},
      PERL = #{perl},
      OCCL = #{occl},
      OTCL = #{otcl},
      ANNL = #{annl},
      ANST = #{anst},
      ANWE = #{anwe},
      PSKL = #{pskl},
      BPSL = #{bpsl},
      ISJL = #{isjl},
      COML = #{coml},
      FANF = #{fanf},
      MARL = #{marl},
      MLPL = #{mlpl},
      PATL = #{patl},
      MATL = #{matl},
      DSFL = #{dsfl},
      BRFL = #{brfl},
      CIFL = #{cifl},
      FPGL = #{fpgl},
      MSCL = #{mscl},
      POFL = #{pofl},
      LADL = #{ladl},
      OTHL1 = #{othl1},
      CHILDL = #{childl},
      NURSL = #{nursl},
      NBOS = #{nbos},
      LSCH = #{lsch},
      CREATE_TIME = now()
    where
    TERM = #{term}
    and EMP_ID = #{empId}
  </update>

  <!-- 通过ID获取数据 -->
  <select id="findById" parameterType="pd" resultType="pd">
    select
    <include refid="Field"></include>
    from
    <include refid="tableName"></include>
    f
    where
    f.MONTHLY_ATTENDANCE_ANALYSIS_ID = #{MONTHLY_ATTENDANCE_ANALYSIS_ID}
  </select>

  <!-- 列表 -->
  <select id="datalistPage" parameterType="page" resultType="pd">
    select
    <include refid="Field"></include>
    from
    <include refid="tableName"></include>
    f
    where 1=1
    <if test="pd.KEYWORDS != null and pd.KEYWORDS != ''"><!-- 关键词检索 -->
      and
      (
      <!--	根据需求自己加检索条件
        字段1 LIKE CONCAT(CONCAT('%', #{pd.KEYWORDS}),'%')
         or
        字段2 LIKE CONCAT(CONCAT('%', #{pd.KEYWORDS}),'%')
      -->
      )
    </if>
  </select>

  <!-- 列表(全部) -->
  <select id="listAll" parameterType="pd" resultType="pd">
    select
    <include refid="Field"></include>
    from
    <include refid="tableName"></include>
    f
  </select>

  <!-- 批量删除 -->
  <delete id="deleteAll" parameterType="String">
    delete from
    <include refid="tableName"></include>
    where
    MONTHLY_ATTENDANCE_ANALYSIS_ID in
    <foreach item="item" index="index" collection="array" open="(" separator="," close=")">
      #{item}
    </foreach>
  </delete>

  <select id="findByTermAndEmpId" parameterType="pd"
    resultType="org.fh.vo.common.MonthlyAttendanceAnalysisVO">
    select
    f.MONTHLY_ATTENDANCE_ANALYSIS_ID as monthlyAttendanceAnalysisId,
    f.TERM as term,
    f.EMP_ID as empId,
    f.NAME as name,
    f.GROUP_ID as groupId,
    f.COMP_ID as compId,
    f.LINE_ID as lineId,
    f.PNHR as pnhr,
    f.WKHR as wkhr,
    f.ADHR as adhr,
    f.CFHR as cfhr,
    f.WHSV as whsv,
    f.UAHR as uahr,
    f.REWK as rewk,
    f.LIMN as limn,
    f.EOMN as eomn,
    f.OT1 as ot1,
    f.OT2 as ot2,
    f.OT3 as ot3,
    f.CLOT1 as clot1,
    f.AN15 as an15,
    f.AN20 as an20,
    f.ATTF as attf,
    f.ATOF as atof,
    f.PERL as perl,
    f.OCCL as occl,
    f.OTCL as otcl,
    f.ANNL as annl,
    f.ANST as anst,
    f.ANWE as anwe,
    f.PSKL as pskl,
    f.BPSL as bpsl,
    f.ISJL as isjl,
    f.COML as coml,
    f.FANF as fanf,
    f.MARL as marl,
    f.MLPL as mlpl,
    f.PATL as patl,
    f.MATL as matl,
    f.DSFL as dsfl,
    f.BRFL as brfl,
    f.CIFL as cifl,
    f.FPGL as fpgl,
    f.MSCL as mscl,
    f.POFL as pofl,
    f.LADL as ladl,
    f.OTHL1 as othl1,
    f.CHILDL as childl,
    f.NURSL as nursl,
    f.NBOS as nbos,
    f.LSCH as lsch,
    f.CREATE_TIME as createTime
    from
    <include refid="tableName"></include>
    f
    where
    f.term = #{term}
    and f.emp_id = #{empId}
    limit 1
  </select>

  <select id="listMonthlyAttendanceAnalysisEmpIdsByTerm" parameterType="pd" resultType="string">
    SELECT DISTINCT
    a.emp_id empId
    FROM
    BIZ_MONTHLY_ATTENDANCE_ANALYSIS a
    LEFT JOIN biz_employee b ON a.emp_id = b.emp_id
    WHERE
    a.term = #{term}
    AND b.position_num = '6'
    AND b.actived != 'Withdrawn'
    AND b.entry_date <![CDATA[<=]]> #{endDate}
  </select>

  <select id="listMonthlyAttendanceAnalysisNotConfirmedEmpIdsByTerm" parameterType="pd" resultType="string">
    SELECT DISTINCT
      a.emp_id empId
    FROM
      BIZ_MONTHLY_ATTENDANCE_ANALYSIS_DETAIL a
      left join biz_employee b on a.emp_id=b.emp_id
    WHERE
      a.term = #{term}
      and a.confirm_status = 0
      <if test="empId !=null and empId !=''">
        and a.emp_id = #{empId}
      </if>
      <if test="loginUser !=null and loginUser !=''">
        and b.human_resource = #{loginUser}
      </if>
  </select>

</mapper>