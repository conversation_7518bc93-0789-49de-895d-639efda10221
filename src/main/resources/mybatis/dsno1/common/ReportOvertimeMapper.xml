<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.fh.mapper.dsno1.common.ReportOvertimeMapper">
    <resultMap id="BaseResultMap" type="org.fh.vo.common.ReportOvertimeVo">
        <result column="plant" jdbcType="VARCHAR" property="plant"/>
        <result column="workshop" jdbcType="VARCHAR" property="workshop"/>
        <result column="one_time" jdbcType="DOUBLE" property="oneTime"/>
        <result column="two_time" jdbcType="DOUBLE" property="twoTime"/>
        <result column="three_time" jdbcType="DOUBLE" property="threeTime"/>
        <result column="year_month" jdbcType="VARCHAR" property="yearMonth"/>
        <result column="average" jdbcType="DOUBLE" property="average"/>
        <result column="resource_count" jdbcType="DOUBLE" property="resourceCount"/>
    </resultMap>
    <sql id="Base_Column_List">
    report_overtime_id, plant, workshop, one_time, two_time, three_time, year_month
  </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from biz_report_overtime
        where report_overtime_id = #{reportOvertimeId,jdbcType=VARCHAR}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from biz_report_overtime
    where report_overtime_id = #{reportOvertimeId,jdbcType=VARCHAR}
  </delete>
    <insert id="insert" parameterType="org.fh.entity.common.ReportOvertimeEntity">
    insert into biz_report_overtime (report_overtime_id, plant, workshop, 
      one_time, two_time, three_time, 
      year_month)
    values (#{reportOvertimeId,jdbcType=VARCHAR}, #{plant,jdbcType=VARCHAR}, #{workshop,jdbcType=VARCHAR}, 
      #{oneTime,jdbcType=DOUBLE}, #{twoTime,jdbcType=DOUBLE}, #{threeTime,jdbcType=DOUBLE}, 
      #{yearMonth,jdbcType=VARCHAR})
  </insert>
    <insert id="insertSelective" parameterType="org.fh.entity.common.ReportOvertimeEntity">
        insert into biz_report_overtime
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="reportOvertimeId != null">
                report_overtime_id,
            </if>
            <if test="plant != null">
                plant,
            </if>
            <if test="workshop != null">
                workshop,
            </if>
            <if test="oneTime != null">
                one_time,
            </if>
            <if test="twoTime != null">
                two_time,
            </if>
            <if test="threeTime != null">
                three_time,
            </if>
            <if test="yearMonth != null">
                year_month,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="reportOvertimeId != null">
                #{reportOvertimeId,jdbcType=VARCHAR},
            </if>
            <if test="plant != null">
                #{plant,jdbcType=VARCHAR},
            </if>
            <if test="workshop != null">
                #{workshop,jdbcType=VARCHAR},
            </if>
            <if test="oneTime != null">
                #{oneTime,jdbcType=DOUBLE},
            </if>
            <if test="twoTime != null">
                #{twoTime,jdbcType=DOUBLE},
            </if>
            <if test="threeTime != null">
                #{threeTime,jdbcType=DOUBLE},
            </if>
            <if test="yearMonth != null">
                #{yearMonth,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="org.fh.entity.common.ReportOvertimeEntity">
        update biz_report_overtime
        <set>
            <if test="plant != null">
                plant = #{plant,jdbcType=VARCHAR},
            </if>
            <if test="workshop != null">
                workshop = #{workshop,jdbcType=VARCHAR},
            </if>
            <if test="oneTime != null">
                one_time = #{oneTime,jdbcType=DOUBLE},
            </if>
            <if test="twoTime != null">
                two_time = #{twoTime,jdbcType=DOUBLE},
            </if>
            <if test="threeTime != null">
                three_time = #{threeTime,jdbcType=DOUBLE},
            </if>
            <if test="yearMonth != null">
                year_month = #{yearMonth,jdbcType=VARCHAR},
            </if>
        </set>
        where report_overtime_id = #{reportOvertimeId,jdbcType=VARCHAR}
    </update>
    <update id="updateByPrimaryKey" parameterType="org.fh.entity.common.ReportOvertimeEntity">
    update biz_report_overtime
    set plant = #{plant,jdbcType=VARCHAR},
      workshop = #{workshop,jdbcType=VARCHAR},
      one_time = #{oneTime,jdbcType=DOUBLE},
      two_time = #{twoTime,jdbcType=DOUBLE},
      three_time = #{threeTime,jdbcType=DOUBLE},
      year_month = #{yearMonth,jdbcType=VARCHAR}
    where report_overtime_id = #{reportOvertimeId,jdbcType=VARCHAR}
  </update>

    <!-- 根据日期年月删除加班数据 -->
    <delete id="deleteReportOvertimeByDate" parameterType="string">
        delete from biz_report_overtime where `year_month` = #{yearMonth}
    </delete>

    <!-- 批量添加加班车间汇总数据 -->
    <insert id="addReportOvertimeList" parameterType="collection">
        insert into biz_report_overtime (
        report_overtime_id,plant,workshop,one_time,two_time,three_time,`year_month`,role,average,resource_count,create_by,create_time)
        values
        <foreach collection="list" item="item" separator=",">
            (
            #{item.reportOvertimeId},#{item.plant},#{item.workshop},#{item.oneTime},#{item.twoTime},#{item.threeTime},#{item.yearMonth},#{item.role},
            #{item.average},#{item.count},#{item.createBy},now()
            )
        </foreach>
    </insert>

    <select id="queryReportOvertimeData" resultMap="BaseResultMap">
        select plant,workshop,one_time,two_time,three_time,`year_month`,average,resource_count
        from biz_report_overtime
        <where>
            <if test="plant !=null and plant !=''">
                and plant = #{plant}
            </if>
            <if test="workshop !=null and workshop !=''">
                and workshop = #{workshop}
            </if>
            <if test="yearMonth !=null and yearMonth !=''">
                and `year_month` like '%${yearMonth}%'
            </if>
            <if test="role !=null and role !=''">
                and role = #{role}
            </if>
            <if test="startDate !=null and startDate !=''">
                and `year_month` &gt;= #{startDate}
            </if>
            <if test="endDate !=null and endDate !=''">
                and `year_month` &lt;= #{endDate}
            </if>
        </where>
    </select>
</mapper>