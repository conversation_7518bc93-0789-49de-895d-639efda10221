<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.fh.mapper.dsno1.common.CECMapper">

    <resultMap id="BaseResultMap" type="org.fh.vo.common.CECVo">
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="leader_sap_no" jdbcType="VARCHAR" property="leaderSapNo"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, leader_sap_no
    </sql>

    <!-- 新增CEC主管 -->
    <insert id="addCEC" parameterType="org.fh.entity.common.CECEntity">
        insert into biz_cec
        (id, leader_sap_no)
        values
        (#{id,jdbcType=VARCHAR}, #{leaderSapNo,jdbcType=VARCHAR})
    </insert>

    <!-- 检查主管SAP编号是否已存在 -->
    <select id="checkLeaderSapNo" resultType="int" parameterType="org.fh.entity.common.CECEntity">
        select count(*)
        from biz_cec
        where leader_sap_no = #{leaderSapNo,jdbcType=VARCHAR}
        <if test="id != null and id != ''">
            and id != #{id,jdbcType=VARCHAR}
        </if>
    </select>

    <!-- 删除CEC主管（逻辑删除，假设有 deleted 字段，如果没有，请使用物理删除） -->
    <!-- <update id="deleteCECById" parameterType="String">
        update biz_cec set deleted = '1' where id = #{id,jdbcType=VARCHAR}
    </update> -->
    <!-- 如果是物理删除 -->
    <delete id="deleteCECById" parameterType="String">
        delete from biz_cec where id = #{id,jdbcType=VARCHAR}
    </delete>

    <!-- 更新CEC主管 -->
    <update id="updateCEC" parameterType="org.fh.entity.common.CECEntity">
        update biz_cec
        <set>
            <if test="leaderSapNo != null and leaderSapNo != ''">
                leader_sap_no = #{leaderSapNo,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=VARCHAR}
    </update>

    <!-- 根据ID查询CEC主管详情 -->
    <select id="queryCECDetailById" parameterType="String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from biz_cec
        where id = #{id,jdbcType=VARCHAR}
    </select>

    <!-- 分页查询CEC主管列表 -->
    <select id="queryCECPage" resultMap="BaseResultMap" parameterType="org.fh.entity.common.CECEntity">
        select
        <include refid="Base_Column_List"/>
        from biz_cec
        <where>
            <if test="leaderSapNo != null and leaderSapNo != ''">
                and leader_sap_no like concat('%', #{leaderSapNo,jdbcType=VARCHAR}, '%')
            </if>
            <!-- 如果有 deleted 字段 -->
            <!-- and deleted = '0' -->
        </where>
        order by id desc /* 或其他排序字段 */
    </select>

    <!-- 查询所有CEC主管信息 -->
    <select id="queryCECAll" resultType="string">
        select
        distinct leader_sap_no
        from biz_cec
    </select>

</mapper> 