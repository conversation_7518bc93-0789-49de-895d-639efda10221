<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.fh.mapper.dsno1.common.MonthlyAttendanceAnalysisDetailMapper">

  <!--表名 -->
  <sql id="tableName">
    BIZ_MONTHLY_ATTENDANCE_ANALYSIS_DETAIL
  </sql>

  <!--数据字典表名 -->
  <sql id="dicTableName">
    SYS_DICTIONARIES
  </sql>

  <!-- 字段 -->
  <sql id="Field">
    f.MONTHLY_ATTENDANCE_ANALYSIS_DETAIL_ID,
    f.TERM,
    f.EMP_ID,
    f.CONFIRM_STATUS,
    f.CONFIRM_TIME,
    f.SEND_TIME
  </sql>

  <!-- 字段用于新增 -->
  <sql id="Field2">
    MONTHLY_ATTENDANCE_ANALYSIS_DETAIL_ID,
    TERM,
    EMP_ID,
    CONFIRM_STATUS,
    CONFIRM_TIME,
    SEND_TIME
  </sql>

  <!-- 字段值 -->
  <sql id="FieldValue">
    #{MONTHLY_ATTENDANCE_ANALYSIS_DETAIL_ID},
    #{TERM},
    #{EMP_ID},
    #{CONFIRM_STATUS},
    #{CONFIRM_TIME},
    #{SEND_TIME}
  </sql>

  <!-- 新增-->
  <insert id="save" parameterType="pd">
    insert into
    <include refid="tableName"></include>
    (
    <include refid="Field2"></include>
    ) values (
    <include refid="FieldValue"></include>
    )
  </insert>

  <!-- 删除-->
  <delete id="delete" parameterType="pd">
    delete from
    <include refid="tableName"></include>
    where
    MONTHLY_ATTENDANCE_ANALYSIS_DETAIL_ID = #{MONTHLY_ATTENDANCE_ANALYSIS_DETAIL_ID}
  </delete>

  <!-- 修改 -->
  <update id="edit" parameterType="pd">
    update
    <include refid="tableName"></include>
    set
    MONTHLY_ATTENDANCE_ANALYSIS_DETAIL_ID = #{MONTHLY_ATTENDANCE_ANALYSIS_DETAIL_ID},
    TERM = #{TERM},
    EMP_ID = #{EMP_ID},
    CONFIRM_STATUS = #{CONFIRM_STATUS},
    CONFIRM_TIME = #{CONFIRM_TIME}
    where
    MONTHLY_ATTENDANCE_ANALYSIS_DETAIL_ID = #{MONTHLY_ATTENDANCE_ANALYSIS_DETAIL_ID}
  </update>

  <!-- 通过ID获取数据 -->
  <select id="findById" parameterType="pd" resultType="pd">
    select
    <include refid="Field"></include>
    from
    <include refid="tableName"></include>
    f
    where
    f.MONTHLY_ATTENDANCE_ANALYSIS_DETAIL_ID = #{MONTHLY_ATTENDANCE_ANALYSIS_DETAIL_ID}
  </select>

  <!-- 列表 -->
  <select id="datalistPage" parameterType="page" resultType="pd">
    select
    <include refid="Field"></include>
    from
    <include refid="tableName"></include>
    f
    where 1=1
    <if test="pd.KEYWORDS != null and pd.KEYWORDS != ''"><!-- 关键词检索 -->
      and
      (
      <!--	根据需求自己加检索条件
        字段1 LIKE CONCAT(CONCAT('%', #{pd.KEYWORDS}),'%')
         or
        字段2 LIKE CONCAT(CONCAT('%', #{pd.KEYWORDS}),'%')
      -->
      )
    </if>
  </select>

  <!-- 列表(全部) -->
  <select id="listAll" parameterType="pd" resultType="pd">
    select
    <include refid="Field"></include>
    from
    <include refid="tableName"></include>
    f
  </select>

  <!-- 批量删除 -->
  <delete id="deleteAll" parameterType="String">
    delete from
    <include refid="tableName"></include>
    where
    MONTHLY_ATTENDANCE_ANALYSIS_DETAIL_ID in
    <foreach item="item" index="index" collection="array" open="(" separator="," close=")">
      #{item}
    </foreach>
  </delete>

  <delete id="deleteByTerm" parameterType="pd">
    delete from
    <include refid="tableName"></include>
    where
    term = #{term}
  </delete>


  <select id="queryPage"
    parameterType="org.fh.dto.common.attendanceAnalysis.MonthlyAttendanceAnalysisDetailQueryDto"
    resultType="org.fh.entity.PageData">
    SELECT
    DATE_FORMAT(a.term, '%Y-%m') AS ym,
    b.`name`,
    b.emp_id empId,
    b.department_code department,
    b.production_line productionLine,
    b.work_team workTeam,
    b.actived,
    b.eplant plant,
    a.confirm_time confirmTime,
    a.confirm_status confirmStatus,
    a.send_time sendTime,
    d.pnhr,
    d.wkhr,
    d.adhr,
    d.uahr,
    d.limn,
    d.eomn,
    d.ot1,
    d.ot2,
    d.ot3,
    d.an15,
    d.an20,
    d.perl,
    d.otcl,
    d.occl,
    d.annl,
    d.pskl,
    d.bpsl,
    d.isjl,
    d.coml,
    d.fanf,
    d.marl,
    d.mlpl,
    d.patl,
    d.matl,
    d.dsfl,
    d.brfl,
    d.cifl,
    d.fpgl,
    d.mscl,
    d.pofl,
    d.ladl,
    d.othl1,
    d.childl,
    d.nursl,
    d.nbos,
    d.lsch
    FROM
    biz_monthly_attendance_analysis_detail a
    LEFT JOIN biz_employee b ON a.emp_id = b.emp_id
<!--    LEFT JOIN biz_monthly_attendance_analysis_status c on a.term = c.term-->
    LEFT JOIN biz_monthly_attendance_analysis d on a.term=d.term and a.emp_id=d.emp_id
    WHERE
    a.term = #{term}
    AND a.confirm_status = #{confirmStatus}
    <if test="name !=null and name !=''">
      and b.name = #{name}
    </if>
    <if test="empId !=null and empId !=''">
      and b.emp_id = #{empId}
    </if>
    <if test="plant !=null and plant !=''">
      and b.plant = #{plant}
    </if>
    <if test="department !=null and department !=''">
      and b.department_code = #{department}
    </if>
    <if test="productionLine !=null and productionLine !=''">
      and b.production_line = #{productionLine}
    </if>
    <if test="workTeam !=null and workTeam !=''">
      and b.work_team = #{workTeam}
    </if>
    <if test="actived !=null and actived !=''">
      and b.actived = #{actived}
    </if>
    <if test="loginUser !=null and loginUser !=''">
      and b.human_resource = #{loginUser}
    </if>

  </select>

  <select id="queryMonthlyAttendanceInfoForConfirm"
    parameterType="org.fh.dto.common.attendanceAnalysis.MonthlyAttendanceAnalysisDetailQueryDto"
    resultType="org.fh.vo.common.MonthlyAttendanceAnalysisVO">
    SELECT a.term,
           a.emp_id         as empId,
           a.name,
           a.pnhr,
           a.wkhr,
           a.adhr,
           a.cfhr,
           a.whsv,
           a.uahr,
           a.rewk,
           a.limn,
           a.eomn,
           a.ot1,
           a.ot2,
           a.ot3,
           a.clot1,
           a.an15,
           a.an20,
           a.attf,
           a.atof,
           a.perl,
           a.occl,
           a.otcl,
           a.annl,
           a.anst,
           a.anwe,
           a.pskl,
           a.bpsl,
           a.isjl,
           a.coml,
           a.fanf,
           a.marl,
           a.mlpl,
           a.patl,
           a.matl,
           a.dsfl,
           a.brfl,
           a.cifl,
           a.fpgl,
           a.mscl,
           a.pofl,
           a.ladl,
           a.othl1,
           a.childl,
           a.nursl,
           a.nbos,
           a.lsch,
           b.confirm_status as sendStatus,
           c.confirm_status as confirmStatus
    FROM biz_monthly_attendance_analysis a
           LEFT JOIN biz_monthly_attendance_analysis_status b ON a.term = b.term
           LEFT JOIN biz_monthly_attendance_analysis_detail c ON a.term = c.term
      AND a.emp_id = c.emp_id
    WHERE a.emp_id = #{empId}
      AND a.term = #{term} limit 1
  </select>

  <update id="confirmMonthlyAttendance"
    parameterType="org.fh.dto.common.attendanceAnalysis.MonthlyAttendanceAnalysisDetailQueryDto">
    update
    <include refid="tableName"></include>
    set
    CONFIRM_STATUS = #{confirmStatus},
    CONFIRM_TIME = now()
    where
    TERM = #{term}
    and EMP_ID = #{empId}
  </update>

  <!-- 通过term和empId更新send_time为当前时间 -->
  <update id="updateSendTimeByTermAndEmpId" parameterType="pd">
    update
    <include refid="tableName"></include>
    set
    SEND_TIME = now()
    where
    TERM = #{term}
    and EMP_ID = #{empId}
  </update>

  <!-- 导出所有记录，不分页 -->
  <select id="queryAllForExport"
    parameterType="org.fh.dto.common.attendanceAnalysis.MonthlyAttendanceAnalysisDetailQueryDto"
    resultType="org.fh.entity.PageData">
      SELECT
      DATE_FORMAT(a.term, '%Y-%m') AS ym,
      b.`name`,
      b.emp_id empId,
      b.department_code department,
      b.production_line productionLine,
      b.work_team workTeam,
      b.actived,
      b.eplant plant,
      a.send_time sendTime,
      a.confirm_time confirmTime,
      a.confirm_status confirmStatus,
      d.pnhr,
      d.wkhr,
      d.adhr,
      d.uahr,
      d.limn,
      d.eomn,
      d.ot1,
      d.ot2,
      d.ot3,
      d.an15,
      d.an20,
      d.perl,
      d.otcl,
      d.occl,
      d.annl,
      d.pskl,
      d.bpsl,
      d.isjl,
      d.coml,
      d.fanf,
      d.marl,
      d.mlpl,
      d.patl,
      d.matl,
      d.dsfl,
      d.brfl,
      d.cifl,
      d.fpgl,
      d.mscl,
      d.pofl,
      d.ladl,
      d.childl,
      d.nursl,
      d.nbos,
      d.lsch
      FROM
      biz_monthly_attendance_analysis_detail a
      LEFT JOIN biz_employee b ON a.emp_id = b.emp_id
<!--      LEFT JOIN biz_monthly_attendance_analysis_status c on a.term = c.term-->
      LEFT JOIN biz_monthly_attendance_analysis d on a.term=d.term and a.emp_id=d.emp_id
    WHERE
    a.term = #{term}
    <if test="confirmStatus !=null">
      AND a.confirm_status = #{confirmStatus}
    </if>
    <if test="name !=null and name !=''">
      and b.name = #{name}
    </if>
    <if test="empId !=null and empId !=''">
      and b.emp_id = #{empId}
    </if>
    <if test="department !=null and department !=''">
      and b.department_code = #{department}
    </if>
    <if test="productionLine !=null and productionLine !=''">
      and b.production_line = #{productionLine}
    </if>
    <if test="workTeam !=null and workTeam !=''">
      and b.work_team = #{workTeam}
    </if>
    <if test="actived !=null and actived !=''">
      and b.actived = #{actived}
    </if>
    <if test="loginUser !=null and loginUser !=''">
      and b.human_resource = #{loginUser}
    </if>
    ORDER BY b.emp_id
  </select>

</mapper>