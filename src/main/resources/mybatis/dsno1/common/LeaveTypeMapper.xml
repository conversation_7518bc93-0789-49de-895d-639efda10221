<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.fh.mapper.dsno1.common.LeaveTypeMapper">
    <resultMap id="BaseResultMap" type="org.fh.vo.common.LeaveTypeVo">
        <id column="leave_type_id" jdbcType="VARCHAR" property="leaveTypeId"/>
        <result column="leave_type_name" jdbcType="VARCHAR" property="leaveTypeName"/>
        <result column="min_hours" jdbcType="VARCHAR" property="minHours"/>
        <result column="explains" jdbcType="VARCHAR" property="explains"/>
        <result column="required" jdbcType="VARCHAR" property="required"/>
        <result column="human_resource" jdbcType="INTEGER" property="humanResource"/>
        <result column="special" jdbcType="INTEGER" property="special"/>
        <result column="doctor_approve" jdbcType="INTEGER" property="doctorApprove"/>
        <result column="health_approve" jdbcType="INTEGER" property="healthApprove"/>
        <result column="work_flow_key" jdbcType="VARCHAR" property="workFlowKey"/>
        <result column="work_flow_name" jdbcType="VARCHAR" property="workFlowName"/>
        <result column="is_delete" jdbcType="BIT" property="isDelete"/>
        <result column="create_by" jdbcType="VARCHAR" property="createBy"/>
        <result column="create_time" jdbcType="VARCHAR" property="createTime"/>
        <result column="update_by" jdbcType="VARCHAR" property="updateBy"/>
        <result column="update_time" jdbcType="VARCHAR" property="updateTime"/>
    </resultMap>
    <sql id="Base_Column_List">
    leave_type_id, leave_type_name, min_hours, explains,required,work_flow_key,work_flow_name,is_delete,create_by, create_time, update_by, update_time
  </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from biz_leave_type
        where leave_type_id = #{leaveTypeId,jdbcType=VARCHAR}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from biz_leave_type
    where leave_type_id = #{leaveTypeId,jdbcType=VARCHAR}
  </delete>
    <insert id="insert" parameterType="org.fh.entity.common.LeaveTypeEntity">
    insert into biz_leave_type (leave_type_id, leave_type_name, min_hours, explains,required,work_flow_key,work_flow_name,is_delete,
      create_by, create_time, update_by,
      update_time)
    values (#{leaveTypeId,jdbcType=VARCHAR}, #{leaveTypeName,jdbcType=VARCHAR}, #{minHours,jdbcType=VARCHAR},#{isDelete,jdbcType=BIT},
      #{createBy,jdbcType=VARCHAR}, #{createTime,jdbcType=VARCHAR}, #{updateBy,jdbcType=VARCHAR},
      #{updateTime,jdbcType=VARCHAR}),#{explains,jdbcType=VARCHAR},#{required,jdbcType=VARCHAR},#{workFlowKey,jdbcType=VARCHAR},#{workFlowName,jdbcType=VARCHAR}
  </insert>
    <insert id="insertSelective" parameterType="org.fh.entity.common.LeaveTypeEntity">
        insert into biz_leave_type
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="leaveTypeId != null">
                leave_type_id,,
            </if>
            <if test="leaveTypeName != null">
                leave_type_name,
            </if>
            <if test="minHours != null">
                min_hours,
            </if>
            <if test="explains != null">
                explains,
            </if>
            <if test="required != null">
                required,
            </if>
            <if test="workFlowKey != null">
                work_flow_key,
            </if>
            <if test="workFlowName != null">
                work_flow_name,
            </if>
            <if test="isDelete != null">
                is_delete,
            </if>
            <if test="createBy != null">
                create_by,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateBy != null">
                update_by,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="leaveTypeId != null">
                #{leaveTypeId,jdbcType=VARCHAR},
            </if>
            <if test="leaveTypeName != null">
                #{leaveTypeName,jdbcType=VARCHAR},
            </if>
            <if test="minHours != null">
                #{minHours,jdbcType=VARCHAR},
            </if>
            <if test="explains != null">
                #{explains,jdbcType=VARCHAR},
            </if>
            <if test="required != null">
                #{required,jdbcType=VARCHAR},
            </if>
            <if test="workFlowKey != null">
                #{workFlowKey,jdbcType=VARCHAR},
            </if>
            <if test="workFlowName != null">
                #{workFlowName,jdbcType=VARCHAR},
            </if>
            <if test="isDelete != null">
                #{isDelete,jdbcType=BIT},
            </if>
            <if test="createBy != null">
                #{createBy,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=VARCHAR},
            </if>
            <if test="updateBy != null">
                #{updateBy,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="org.fh.entity.common.LeaveTypeEntity">
        update biz_leave_type
        <set>
            <if test="leaveTypeName != null">
                leave_type_name = #{leaveTypeName,jdbcType=VARCHAR},
            </if>
            <if test="minHours != null">
                min_hours = #{minHours,jdbcType=VARCHAR},
            </if>
            <if test="explains != null">
                explains = #{explains,jdbcType=VARCHAR},
            </if>
            <if test="required != null">
                required = #{required,jdbcType=VARCHAR},
            </if>
            <if test="workFlowKey != null">
                work_flow_key = #{workFlowKey,jdbcType=VARCHAR},
            </if>
            <if test="workFlowName != null">
                work_flow_name = #{workFlowName,jdbcType=VARCHAR},
            </if>
            <if test="isDelete != null">
                is_delete = #{isDelete,jdbcType=BIT},
            </if>
            <if test="createBy != null">
                create_by = #{createBy,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=VARCHAR},
            </if>
            <if test="updateBy != null">
                update_by = #{updateBy,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=VARCHAR},
            </if>
        </set>
        where leave_type_id = #{leaveTypeId,jdbcType=VARCHAR}
    </update>
    <update id="updateByPrimaryKey" parameterType="org.fh.entity.common.LeaveTypeEntity">
    update biz_leave_type
    set leave_type_name = #{leaveTypeName,jdbcType=VARCHAR},
      min_hours = #{minHours,jdbcType=VARCHAR},
      explains = #{explains,jdbcType=VARCHAR},
      required = #{required,jdbcType=VARCHAR},
      work_flow_key = #{workFlowKey,jdbcType=VARCHAR},
      work_flow_name = #{workFlowName,jdbcType=VARCHAR},
      is_delete = #{isDelete,jdbcType=BIT},
      create_by = #{createBy,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=VARCHAR},
      update_by = #{updateBy,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=VARCHAR}
    where leave_type_id = #{leaveTypeId,jdbcType=VARCHAR}
  </update>
    <!-- 添加假别信息 -->
    <insert id="addLeaveType" parameterType="org.fh.entity.common.LeaveTypeEntity">
        insert into biz_leave_type
        <trim prefix="(" suffix=")" suffixOverrides=",">
            leave_type_id,
            <if test="leaveTypeName != null and leaveTypeName !=''">
                leave_type_name,
            </if>
            <if test="minHours != null and minHours !=''">
                min_hours,
            </if>
            <if test="explains != null and explains !=''">
                explains,
            </if>
            <if test="required != null and required !=''">
                required,
            </if>
            <if test="humanResource != null">
                human_resource,
            </if>
            <if test="special != null">
                special,
            </if>
            <if test="doctorApprove != null">
                doctor_approve,
            </if>
            <if test="healthApprove != null">
                health_approve,
            </if>
            <if test="workFlowKey != null and workFlowKey !=''">
                work_flow_key,
            </if>
            <if test="workFlowName != null and workFlowName !=''">
                work_flow_name,
            </if>
            is_delete,
            <if test="createBy != null and createBy !=''">
                create_by,
            </if>
            create_time
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            #{leaveTypeId},
            <if test="leaveTypeName != null and leaveTypeName !=''">
                #{leaveTypeName},
            </if>
            <if test="minHours != null and minHours !=''">
                #{minHours},
            </if>
            <if test="explains != null and explains !=''">
                #{explains},
            </if>
            <if test="required != null and required !=''">
                #{required},
            </if>
            <if test="humanResource != null">
                #{humanResource},
            </if>
            <if test="special != null">
                #{special},
            </if>
            <if test="doctorApprove != null">
                #{doctorApprove},
            </if>
            <if test="healthApprove != null">
                #{healthApprove},
            </if>
            <if test="workFlowKey != null and workFlowKey !=''">
                #{workFlowKey},
            </if>
            <if test="workFlowName != null and workFlowName !=''">
                #{workFlowName},
            </if>
            0,
            <if test="createBy != null and createBy !=''">
                #{createBy},
            </if>
            now()
        </trim>
    </insert>
    <!-- 删除假别信息 -->
    <update id="deleteLeaveTypeData" parameterType="string">
    update  biz_leave_type set is_delete = '1' where leave_type_id = #{leaveTypeId}
  </update>
    <!-- 修改假别信息 -->
    <update id="updateLeaveType" parameterType="org.fh.entity.common.LeaveTypeEntity">
        update biz_leave_type
        <set>
            <if test="leaveTypeName != null and leaveTypeName !=''">
                leave_type_name = #{leaveTypeName},
            </if>
            <if test="minHours != null and minHours !=''">
                min_hours = #{minHours},
            </if>
            <if test="explains != null and explains !=''">
                explains = #{explains},
            </if>
            <if test="required != null and required !=''">
                required = #{required},
            </if>
            <if test="humanResource != null">
                human_resource = #{humanResource},
            </if>
            <if test="special != null">
                special = #{special},
            </if>
            <if test="doctorApprove != null">
                doctor_approve = #{doctorApprove},
            </if>
            <if test="healthApprove != null">
                health_approve = #{healthApprove},
            </if>
            <if test="workFlowKey != null and workFlowKey !=''">
                work_flow_key = #{workFlowKey},
            </if>
            <if test="workFlowName != null and workFlowName !=''">
                work_flow_name = #{workFlowName},
            </if>
            <if test="updateBy != null and updateBy !=''">
                update_by = #{updateBy},
            </if>
            update_time = now()
        </set>
        where leave_type_id = #{leaveTypeId}
    </update>
    <!-- 查询假别信息 -->
    <select id="queryLeaveTypeDetail" parameterType="string" resultMap="BaseResultMap">
    select leave_type_id,leave_type_name,min_hours,l.explains,required,work_flow_key,human_resource,special,
    doctor_approve,health_approve,multiple,work_flow_name,create_by,create_time,update_by,update_time
    from biz_leave_type l
    where leave_type_id = #{leaveTypeId}
  </select>

    <select id="queryLeaveTypeDetailByName" parameterType="string" resultMap="BaseResultMap">
        select leave_type_id,leave_type_name,min_hours,l.explains,required,work_flow_key,human_resource,special,
               doctor_approve,health_approve,multiple,work_flow_name,create_by,create_time,update_by,update_time
        from biz_leave_type l
        where leave_type_name = #{leaveTypeName}
    </select>



    <!-- 分页查询 -->
    <select id="queryLeaveTypePage" resultMap="BaseResultMap" parameterType="org.fh.entity.common.LeaveTypeEntity">
        select leave_type_id,leave_type_name,min_hours,l.explains,required,doctor_approve,health_approve,work_flow_key,work_flow_name,create_time,create_by,update_time,update_by
        from biz_leave_type l
        <where>
            <if test="leaveTypeName != null and leaveTypeName !=''">
                and leave_type_name = #{leaveTypeName}
            </if>
            <if test="minHours != null and minHours !=''">
                and min_hours = #{minHours}
            </if>
            <if test="explains != null and explains !=''">
                and l.explains = #{explains}
            </if>
            and is_delete = '0'
        </where>
    </select>

    <select id="queryLeaveTypeAll" resultMap="BaseResultMap">
        select leave_type_id,leave_type_name,min_hours,l.explains,required,doctor_approve,health_approve,work_flow_key,work_flow_name,create_time,create_by,update_time,update_by
        from biz_leave_type l
        where is_delete = '0'
    </select>
</mapper>