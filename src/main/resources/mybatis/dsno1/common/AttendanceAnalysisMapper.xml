<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.fh.mapper.dsno1.common.AttendanceAnalysisMapper">
    <resultMap id="BaseResultMap" type="org.fh.vo.common.AttendanceAnalysisVo">
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="calendar_day" jdbcType="DATE" property="calendarDay"/>
        <result column="emp_id" jdbcType="VARCHAR" property="empId"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="plant" jdbcType="VARCHAR" property="plant"/>
        <result column="department" jdbcType="VARCHAR" property="department"/>
        <result column="workshop" jdbcType="VARCHAR" property="workshop"/>
        <result column="cost_center" jdbcType="VARCHAR" property="costCenter"/>
        <result column="production_line" jdbcType="VARCHAR" property="productionLine"/>
        <result column="work_type" jdbcType="VARCHAR" property="workType"/>
        <result column="work_team" jdbcType="VARCHAR" property="workTeam"/>
        <result column="start_time" jdbcType="VARCHAR" property="startTime"/>
        <result column="end_time" jdbcType="VARCHAR" property="endTime"/>
        <result column="abnormal" jdbcType="VARCHAR" property="abnormal"/>
        <result column="late_minutes" jdbcType="INTEGER" property="lateMinutes"/>
        <result column="leave_early_minutes" jdbcType="INTEGER" property="leaveEarlyMinutes"/>
        <result column="lose_sign_count" jdbcType="INTEGER" property="loseSignCount"/>
        <result column="absenteeism_minutes" jdbcType="DECIMAL" property="absenteeismMinutes"/>
        <result column="overtime_1p5" jdbcType="DECIMAL" property="overtime1p5"/>
        <result column="overtime_2" jdbcType="DECIMAL" property="overtime2"/>
        <result column="overtime_3" jdbcType="DECIMAL" property="overtime3"/>
    </resultMap>
    <sql id="Base_Column_List">
    id, calendar_day, emp_id, name, work_type, work_team, abnormal, late_minutes, leave_early_minutes, 
    lose_sign_count, absenteeism_minutes, overtime_1p5, overtime_2, overtime_3
  </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from biz_attendance_analysis
        where id = #{id,jdbcType=VARCHAR}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from biz_attendance_analysis
    where id = #{id,jdbcType=VARCHAR}
  </delete>
    <insert id="insert" parameterType="org.fh.entity.common.AttendanceAnalysisEntity">
    insert into biz_attendance_analysis (id, calendar_day, emp_id, 
      name, work_type, work_team, 
      abnormal, late_minutes, leave_early_minutes, 
      lose_sign_count, absenteeism_minutes, overtime_1p5, 
      overtime_2, overtime_3)
    values (#{id,jdbcType=VARCHAR}, #{calendarDay,jdbcType=DATE}, #{empId,jdbcType=VARCHAR}, 
      #{name,jdbcType=VARCHAR}, #{workType,jdbcType=VARCHAR}, #{workTeam,jdbcType=VARCHAR}, 
      #{abnormal,jdbcType=VARCHAR}, #{lateMinutes,jdbcType=INTEGER}, #{leaveEarlyMinutes,jdbcType=INTEGER}, 
      #{loseSignCount,jdbcType=INTEGER}, #{absenteeismMinutes,jdbcType=DECIMAL}, #{overtime1p5,jdbcType=DECIMAL}, 
      #{overtime2,jdbcType=DECIMAL}, #{overtime3,jdbcType=DECIMAL})
  </insert>
    <insert id="insertSelective" parameterType="org.fh.entity.common.AttendanceAnalysisEntity">
        insert into biz_attendance_analysis
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="calendarDay != null">
                calendar_day,
            </if>
            <if test="empId != null">
                emp_id,
            </if>
            <if test="name != null">
                name,
            </if>
            <if test="workType != null">
                work_type,
            </if>
            <if test="workTeam != null">
                work_team,
            </if>
            <if test="abnormal != null">
                abnormal,
            </if>
            <if test="lateMinutes != null">
                late_minutes,
            </if>
            <if test="leaveEarlyMinutes != null">
                leave_early_minutes,
            </if>
            <if test="loseSignCount != null">
                lose_sign_count,
            </if>
            <if test="absenteeismMinutes != null">
                absenteeism_minutes,
            </if>
            <if test="overtime1p5 != null">
                overtime_1p5,
            </if>
            <if test="overtime2 != null">
                overtime_2,
            </if>
            <if test="overtime3 != null">
                overtime_3,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=VARCHAR},
            </if>
            <if test="calendarDay != null">
                #{calendarDay,jdbcType=DATE},
            </if>
            <if test="empId != null">
                #{empId,jdbcType=VARCHAR},
            </if>
            <if test="name != null">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="workType != null">
                #{workType,jdbcType=VARCHAR},
            </if>
            <if test="workTeam != null">
                #{workTeam,jdbcType=VARCHAR},
            </if>
            <if test="abnormal != null">
                #{abnormal,jdbcType=VARCHAR},
            </if>
            <if test="lateMinutes != null">
                #{lateMinutes,jdbcType=INTEGER},
            </if>
            <if test="leaveEarlyMinutes != null">
                #{leaveEarlyMinutes,jdbcType=INTEGER},
            </if>
            <if test="loseSignCount != null">
                #{loseSignCount,jdbcType=INTEGER},
            </if>
            <if test="absenteeismMinutes != null">
                #{absenteeismMinutes,jdbcType=DECIMAL},
            </if>
            <if test="overtime1p5 != null">
                #{overtime1p5,jdbcType=DECIMAL},
            </if>
            <if test="overtime2 != null">
                #{overtime2,jdbcType=DECIMAL},
            </if>
            <if test="overtime3 != null">
                #{overtime3,jdbcType=DECIMAL},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="org.fh.entity.common.AttendanceAnalysisEntity">
        update biz_attendance_analysis
        <set>
            <if test="calendarDay != null">
                calendar_day = #{calendarDay,jdbcType=DATE},
            </if>
            <if test="empId != null">
                emp_id = #{empId,jdbcType=VARCHAR},
            </if>
            <if test="name != null">
                name = #{name,jdbcType=VARCHAR},
            </if>
            <if test="workType != null">
                work_type = #{workType,jdbcType=VARCHAR},
            </if>
            <if test="workTeam != null">
                work_team = #{workTeam,jdbcType=VARCHAR},
            </if>
            <if test="abnormal != null">
                abnormal = #{abnormal,jdbcType=VARCHAR},
            </if>
            <if test="lateMinutes != null">
                late_minutes = #{lateMinutes,jdbcType=INTEGER},
            </if>
            <if test="leaveEarlyMinutes != null">
                leave_early_minutes = #{leaveEarlyMinutes,jdbcType=INTEGER},
            </if>
            <if test="loseSignCount != null">
                lose_sign_count = #{loseSignCount,jdbcType=INTEGER},
            </if>
            <if test="absenteeismMinutes != null">
                absenteeism_minutes = #{absenteeismMinutes,jdbcType=DECIMAL},
            </if>
            <if test="overtime1p5 != null">
                overtime_1p5 = #{overtime1p5,jdbcType=DECIMAL},
            </if>
            <if test="overtime2 != null">
                overtime_2 = #{overtime2,jdbcType=DECIMAL},
            </if>
            <if test="overtime3 != null">
                overtime_3 = #{overtime3,jdbcType=DECIMAL},
            </if>
        </set>
        where id = #{id,jdbcType=VARCHAR}
    </update>
    <update id="updateByPrimaryKey" parameterType="org.fh.entity.common.AttendanceAnalysisEntity">
    update biz_attendance_analysis
    set calendar_day = #{calendarDay,jdbcType=DATE},
      emp_id = #{empId,jdbcType=VARCHAR},
      name = #{name,jdbcType=VARCHAR},
      work_type = #{workType,jdbcType=VARCHAR},
      work_team = #{workTeam,jdbcType=VARCHAR},
      abnormal = #{abnormal,jdbcType=VARCHAR},
      late_minutes = #{lateMinutes,jdbcType=INTEGER},
      leave_early_minutes = #{leaveEarlyMinutes,jdbcType=INTEGER},
      lose_sign_count = #{loseSignCount,jdbcType=INTEGER},
      absenteeism_minutes = #{absenteeismMinutes,jdbcType=DECIMAL},
      overtime_1p5 = #{overtime1p5,jdbcType=DECIMAL},
      overtime_2 = #{overtime2,jdbcType=DECIMAL},
      overtime_3 = #{overtime3,jdbcType=DECIMAL}
    where id = #{id,jdbcType=VARCHAR}
  </update>

    <!-- 批量添加考勤分析数据 -->
    <insert id="addAttendanceAnalysisList" parameterType="collection">
        insert into biz_attendance_analysis
        (
        id,calendar_day,emp_id,name,work_type,work_team,start_time,end_time,abnormal,late_minutes,leave_early_minutes,lose_sign_count,
        absenteeism_minutes,overtime_1p5,overtime_2,overtime_3,crete_user,create_time
        )
        values
        <foreach collection="list" item="item" separator=",">
            (
            #{item.id},#{item.calendarDay},#{item.empId},#{item.name},#{item.workType},#{item.workTeam},#{item.startTime},#{item.endTime},#{item.abnormal},#{item.lateMinutes},
            #{item.leaveEarlyMinutes},#{item.loseSignCount},#{item.absenteeismMinutes},#{item.overtime1p5},#{item.overtime2},#{item.overtime3},#{item.creteUser},now()
            )
        </foreach>
    </insert>

    <!-- 查询加班工时和打卡次数yiji -->
    <select id="queryAttendanceTime" resultType="map" parameterType="org.fh.entity.common.AttendanceEntity">
        select sum(overtime_1p5+overtime_2+overtime_3) as overtime,count(*) as attendance,sum(lose_sign_count) as lackCount
        from biz_attendance_analysis where calendar_day like '%${attendantDate}%' and emp_id = #{empId}
    </select>

    <!-- 查询迟到次数 -->
    <select id="queryBeLateCount" parameterType="org.fh.entity.common.AttendanceEntity" resultType="int">
        select count(*) from biz_attendance_analysis where calendar_day like '%${attendantDate}%' and emp_id = #{empId} and late_minutes !=0
    </select>

    <!-- 查询早退次数 -->
    <select id="queryEarlyWorkCount" parameterType="org.fh.entity.common.AttendanceEntity" resultType="int">
         select count(*) from biz_attendance_analysis where calendar_day like '%${attendantDate}%' and emp_id = #{empId} and leave_early_minutes !=0
    </select>

    <select id="queryAbsenteeism" parameterType="org.fh.entity.common.AttendanceEntity" resultType="int">
        select count(*) from biz_attendance_analysis where calendar_day like '%${attendantDate}%' and emp_id = #{empId} and absenteeism_minutes !=0
    </select>

    <!-- 查询出勤异常 -->
    <select id="queryAttendanceAnalysisDetail" parameterType="string" resultMap="BaseResultMap">
    select aa.id,aa.name,aa.emp_id,aa.calendar_day,aa.work_type,aa.work_team,aa.abnormal,aa.late_minutes,aa.leave_early_minutes,aa.lose_sign_count,
        aa.absenteeism_minutes,aa.overtime_1p5,aa.overtime_2,overtime_3,cc.cost_center,cc.department,cc.plant,cc.workshop,e.production_line
    from biz_attendance_analysis aa
    left join biz_employee e on aa.emp_id = e.emp_id
    left join biz_cost_center cc on e.cost_center = cc.cost_center
    where aa.id = #{id}
  </select>


    <!-- 分页查询出勤异常 -->
    <select id="queryAttendanceAnalysisPage" resultMap="BaseResultMap" parameterType="org.fh.entity.common.AttendanceAnalysisEntity">
        select
        aa.id,aa.name,aa.emp_id,aa.calendar_day,aa.work_type,aa.work_team,aa.start_time,aa.end_time,aa.abnormal,aa.late_minutes,aa.leave_early_minutes,aa.lose_sign_count,
        aa.absenteeism_minutes,aa.overtime_1p5,aa.overtime_2,overtime_3,cc.cost_center,cc.department,cc.plant,cc.workshop,cc.vsm,e.production_line
        from biz_attendance_analysis aa
        left join biz_employee e on aa.emp_id = e.emp_id
        left join biz_cost_center cc on e.cost_center = cc.cost_center
        <where>
            <if test="name != null and name !=''">
                and aa.name like '%${name}%'
            </if>
            <if test="empId != null and empId !=''">
                and aa.emp_id like '%${empId}%'
            </if>
            <if test="calendarDay != null and calendarDay !=''">
                and aa.calendarDay like '%${calendarDay}%'
            </if>
            <if test="plant !=null and plant !=''">
                and cc.plant = #{plant}
            </if>
            <if test="workshop !=null and workshop !=''">
                and cc.workshop like '%${workshop}%'
            </if>
            <if test="vsm !=null and vsm !=''">
                and cc.vsm like '%${vsm}%'
            </if>
            <if test="productionLine !=null and productionLine !=''">
                and e.production_line like '%${productionLine}%'
            </if>
            <if test="startQueryDate != null and startQueryDate !=''">
                and aa.calendar_day &gt;= #{startQueryDate}
            </if>
            <if test="endQueryDate != null and endQueryDate !=''">
                and aa.calendar_day &lt;= #{endQueryDate}
            </if>
            <if test="workType != null and workType !=''">
                and aa.work_type like '%${workType}%'
            </if>
            <if test="workTeam != null and workTeam !=''">
                and aa.work_team like '%${workTeam}%'
            </if>
            <if test="costCenter != null and costCenter !=''">
                and cc.cost_center like '%${costCenter}%'
            </if>
            <if test="department != null and department !=''">
                and cc.department like '%${department}%'
            </if>

        </where>
        order by aa.calendar_day desc
    </select>

    <select id="queryAttendanceWorkshopList" resultType="map" parameterType="org.fh.entity.common.AttendanceAnalysisEntity">
        select count(aa.emp_id)as `count`,count(distinct aa.emp_id) as number,cc.workshop,cc.plant
        from biz_attendance_analysis aa
        left join biz_employee e on aa.emp_id = e.emp_id
        left join biz_cost_center cc on e.cost_center = cc.cost_center
        where cc.workshop is not null
        and cc.plant is not null
        <if test="startQueryDate !=null and startQueryDate !=''">
            and aa.calendar_day &gt; #{startQueryDate}
        </if>
        <if test="endQueryDate !=null and endQueryDate !=''">
            and aa.calendar_day &lt; #{endQueryDate}
        </if>
        <if test="plant !=null and plant !=''">
            and cc.plant = #{plant}
        </if>
        <if test="workshop !=null and workshop !=''">
            and cc.workshop like '%${workshop}%'
        </if>
        <if test="vsm !=null and vsm !=''">
            and cc.vsm like '%${vsm}%'
        </if>
        group by cc.workshop,cc.plant
    </select>

    <!-- 查询异常总数量 -->
    <select id="queryAttendanceCount" resultType="int" parameterType="org.fh.entity.common.AttendanceAnalysisEntity">
        select count(*)
        from biz_attendance_analysis aa
        left join biz_employee e on aa.emp_id = e.emp_id
        left join biz_cost_center cc on e.cost_center = cc.cost_center
        <where>
            cc.workshop is not null
            and cc.plant is not null
            <if test="startQueryDate !=null and startQueryDate !=''">
                and aa.calendar_day &gt; #{startQueryDate}
            </if>
            <if test="endQueryDate !=null and endQueryDate !=''">
                and aa.calendar_day &lt; #{endQueryDate}
            </if>
            <if test="plant !=null and plant !=''">
                and cc.plant = #{plant}
            </if>
            <if test="workshop !=null and workshop !=''">
                and cc.workshop like '%${workshop}%'
            </if>
            <if test="vsm !=null and vsm !=''">
                and cc.vsm like '%${vsm}%'
            </if>
        </where>
    </select>

    <select id="queryPeopleAttendanceCount" resultType="int" parameterType="org.fh.entity.common.AttendanceAnalysisEntity">
        select count(distinct aa.emp_id)
        from biz_attendance_analysis aa
        left join biz_employee e on aa.emp_id = e.emp_id
        left join biz_cost_center cc on e.cost_center = cc.cost_center
        <where>
            cc.workshop is not null
            and cc.plant is not null
            <if test="startQueryDate !=null and startQueryDate !=''">
                and aa.calendar_day &gt; #{startQueryDate}
            </if>
            <if test="endQueryDate !=null and endQueryDate !=''">
                and aa.calendar_day &lt; #{endQueryDate}
            </if>
            <if test="plant !=null and plant !=''">
                and cc.plant = #{plant}
            </if>
            <if test="workshop !=null and workshop !=''">
                and cc.workshop like '%${workshop}%'
            </if>
            <if test="vsm !=null and vsm !=''">
                and cc.vsm like '%${vsm}%'
            </if>
        </where>
    </select>

    <select id="queryAttendanceByDate" parameterType="string" resultType="org.fh.entity.common.AttendanceAnalysisEntity">
        select calendar_day as calendarDay,emp_id as empId
        from biz_attendance_analysis
        where calendar_day = #{calendarDay}
    </select>

</mapper>