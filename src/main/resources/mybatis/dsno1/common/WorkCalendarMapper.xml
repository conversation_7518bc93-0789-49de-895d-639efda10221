<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.fh.mapper.dsno1.common.WorkCalendarMapper">
    <resultMap id="BaseResultMap" type="org.fh.vo.common.WorkCalendarVo">
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="work_date" jdbcType="DATE" property="workDate"/>
        <result column="type" jdbcType="BIT" property="type"/>
        <result column="created_by" jdbcType="VARCHAR" property="createdBy"/>
        <result column="created_time" jdbcType="TIMESTAMP" property="createdTime"/>
        <result column="update_by" jdbcType="VARCHAR" property="updateBy"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>
    <sql id="Base_Column_List">
    id, work_date, type, created_by, created_time, update_by, update_time
  </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from biz_work_calendar
        where id = #{id,jdbcType=VARCHAR}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from biz_work_calendar
    where id = #{id,jdbcType=VARCHAR}
  </delete>
    <insert id="insert" parameterType="org.fh.entity.common.WorkCalendarEntity">
    insert into biz_work_calendar (id, work_date, type, 
      created_by, created_time, update_by, 
      update_time)
    values (#{id,jdbcType=VARCHAR}, #{workDate,jdbcType=DATE}, #{type,jdbcType=BIT}, 
      #{createdBy,jdbcType=VARCHAR}, #{createdTime,jdbcType=TIMESTAMP}, #{updateBy,jdbcType=VARCHAR}, 
      #{updateTime,jdbcType=TIMESTAMP})
  </insert>
    <insert id="insertSelective" parameterType="org.fh.entity.common.WorkCalendarEntity">
        insert into biz_work_calendar
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="workDate != null">
                work_date,
            </if>
            <if test="type != null">
                type,
            </if>
            <if test="createdBy != null">
                created_by,
            </if>
            <if test="createdTime != null">
                created_time,
            </if>
            <if test="updateBy != null">
                update_by,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=VARCHAR},
            </if>
            <if test="workDate != null">
                #{workDate,jdbcType=DATE},
            </if>
            <if test="type != null">
                #{type,jdbcType=BIT},
            </if>
            <if test="createdBy != null">
                #{createdBy,jdbcType=VARCHAR},
            </if>
            <if test="createdTime != null">
                #{createdTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateBy != null">
                #{updateBy,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="org.fh.entity.common.WorkCalendarEntity">
        update biz_work_calendar
        <set>
            <if test="workDate != null">
                work_date = #{workDate,jdbcType=DATE},
            </if>
            <if test="type != null">
                type = #{type,jdbcType=BIT},
            </if>
            <if test="createdBy != null">
                created_by = #{createdBy,jdbcType=VARCHAR},
            </if>
            <if test="createdTime != null">
                created_time = #{createdTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateBy != null">
                update_by = #{updateBy,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=VARCHAR}
    </update>
    <update id="updateByPrimaryKey" parameterType="org.fh.entity.common.WorkCalendarEntity">
    update biz_work_calendar
    set work_date = #{workDate,jdbcType=DATE},
      type = #{type,jdbcType=BIT},
      created_by = #{createdBy,jdbcType=VARCHAR},
      created_time = #{createdTime,jdbcType=TIMESTAMP},
      update_by = #{updateBy,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=VARCHAR}
  </update>

    <!-- 判断数据库里是否存在末年的日历数据 -->
    <select id="checkYearForData" resultType="int" parameterType="string">
        select count(*) from biz_work_calendar where work_date like '%${year}%'
    </select>

    <!-- 批量添加工作日历 -->
    <insert id="addWorkCalendarList" parameterType="collection">
        insert into biz_work_calendar(id,work_date,type,created_by,created_time)
        values
        <foreach collection="list" separator="," item="item">
            (#{item.id},#{item.workDate},#{item.type},#{item.createdBy},now())
        </foreach>
    </insert>

    <!-- 根据年月查询工作日历 -->
    <select id="queryWorkCalendarList" parameterType="string" resultMap="BaseResultMap">
        select id, work_date, type, created_by, created_time, update_by, update_time
        from biz_work_calendar where work_date like '%${yearMonth}%' order by work_date asc
    </select>

    <!-- 批量修改工作日历数据 -->
    <update id="updateWorkCalendarList" parameterType="collection">
        <foreach collection="list" item="item" separator=";">
            update biz_work_calendar set type = #{item.type},update_by = #{item.updateBy},update_time = now() where work_date = #{item.workDate}
        </foreach>
    </update>

    <!-- 单个修改工作日历数据 -->
    <update id="updateWorkCalendarData" parameterType="org.fh.entity.common.WorkCalendarEntity">
        update biz_work_calendar set type = #{type},update_by = #{updateBy},update_time = now() where work_date = #{workDate}
    </update>


    <!-- 查询指定日期内休息日期集合 -->
    <select id="queryHolidayList" resultType="string">
        select distinct work_date from biz_work_calendar where work_date &gt;= #{startDate} and work_date &lt;= #{endDate} and `type` != '1'
    </select>

    <!-- 查询指定日期内国假日期集合（type=3） -->
    <select id="queryNationalHolidayList" resultType="string">
        select distinct work_date from biz_work_calendar where work_date &gt;= #{startDate} and work_date &lt;= #{endDate} and `type` = '3'
    </select>
</mapper>