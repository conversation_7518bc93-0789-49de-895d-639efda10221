<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.fh.mapper.dsno1.common.OmitAttendanceMapper">
    <resultMap id="BaseResultMap" type="org.fh.entity.common.OmitAttendanceEntity">
        <id column="omit_id" jdbcType="VARCHAR" property="omitId"/>
        <result column="emp_id" jdbcType="VARCHAR" property="empId"/>
        <result column="open_id" jdbcType="VARCHAR" property="openId"/>
        <result column="attendance_date" jdbcType="DATE" property="attendanceDate"/>
        <result column="status" jdbcType="INTEGER" property="status"/>
    </resultMap>
    <sql id="Base_Column_List">
    omit_id, emp_id, open_id, attendance_date, status
  </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from biz_omit_attendance
        where omit_id = #{omitId,jdbcType=VARCHAR}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from biz_omit_attendance
    where omit_id = #{omitId,jdbcType=VARCHAR}
  </delete>
    <insert id="insert" parameterType="org.fh.entity.common.OmitAttendanceEntity">
    insert into biz_omit_attendance (omit_id, emp_id, open_id, 
      attendance_date, status)
    values (#{omitId,jdbcType=VARCHAR}, #{empId,jdbcType=VARCHAR}, #{openId,jdbcType=VARCHAR}, 
      #{attendanceDate,jdbcType=DATE}, #{status,jdbcType=INTEGER})
  </insert>
    <insert id="insertSelective" parameterType="org.fh.entity.common.OmitAttendanceEntity">
        insert into biz_omit_attendance
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="omitId != null">
                omit_id,
            </if>
            <if test="empId != null">
                emp_id,
            </if>
            <if test="openId != null">
                open_id,
            </if>
            <if test="attendanceDate != null">
                attendance_date,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="workType != null">
                work_type,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="omitId != null">
                #{omitId,jdbcType=VARCHAR},
            </if>
            <if test="empId != null">
                #{empId,jdbcType=VARCHAR},
            </if>
            <if test="openId != null">
                #{openId,jdbcType=VARCHAR},
            </if>
            <if test="attendanceDate != null">
                #{attendanceDate,jdbcType=DATE},
            </if>
            <if test="status != null">
                #{status,jdbcType=INTEGER},
            </if>
            <if test="workType != null">
                #{workType,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="org.fh.entity.common.OmitAttendanceEntity">
        update biz_omit_attendance
        <set>
            <if test="empId != null">
                emp_id = #{empId,jdbcType=VARCHAR},
            </if>
            <if test="openId != null">
                open_id = #{openId,jdbcType=VARCHAR},
            </if>
            <if test="attendanceDate != null">
                attendance_date = #{attendanceDate,jdbcType=DATE},
            </if>
            <if test="status != null">
                status = #{status,jdbcType=INTEGER},
            </if>
        </set>
        where omit_id = #{omitId,jdbcType=VARCHAR}
    </update>
    <update id="updateByPrimaryKey" parameterType="org.fh.entity.common.OmitAttendanceEntity">
    update biz_omit_attendance
        set emp_id = #{empId,jdbcType=VARCHAR},
          open_id = #{openId,jdbcType=VARCHAR},
          attendance_date = #{attendanceDate,jdbcType=DATE},
          status = #{status,jdbcType=INTEGER},
          work_type = #{workType}
        where omit_id = #{omitId,jdbcType=VARCHAR}
  </update>

    <insert id="addOmitAttendanceList">
        insert into biz_omit_attendance (omit_id,emp_id,open_id,attendance_date,status,work_type)
        values
        <foreach collection="list" separator="," item="item">
            (#{item.omitId},#{item.empId},#{item.openId},#{item.attendanceDate},#{item.status},#{item.workType})
        </foreach>

    </insert>
</mapper>