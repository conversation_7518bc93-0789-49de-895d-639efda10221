<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.fh.mapper.dsno1.common.UserMenuMapper">
  <resultMap id="BaseResultMap" type="org.fh.vo.common.UserMenuVo">
    <id column="menu_id" jdbcType="VARCHAR" property="menuId" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="url" jdbcType="VARCHAR" property="url" />
    <result column="pid" jdbcType="VARCHAR" property="pid" />
    <result column="order" jdbcType="INTEGER" property="order" />
  </resultMap>
  <sql id="Base_Column_List">
    menu_id, `name`, url, pid, `order`, create_by, create_time, update_by, update_time, deleted
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from biz_menu
    where menu_id = #{menuId,jdbcType=VARCHAR}
  </select>

  <select id="queryUserMenuListAll" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from biz_menu
    where deleted = 0
    order by `order` asc
  </select>

  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from biz_menu
    where menu_id = #{menuId,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="org.fh.entity.common.MenuEntity">
    insert into biz_menu (menu_id, name, url, 
      pid, order, create_by, 
      create_time, update_by, update_time, 
      deleted)
    values (#{menuId,jdbcType=VARCHAR}, #{name,jdbcType=VARCHAR}, #{url,jdbcType=VARCHAR}, 
      #{pid,jdbcType=VARCHAR}, #{order,jdbcType=INTEGER}, #{createBy,jdbcType=VARCHAR}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateBy,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{deleted,jdbcType=BIT})
  </insert>
  <insert id="insertSelective" parameterType="org.fh.entity.common.MenuEntity">
    insert into biz_menu
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="menuId != null">
        menu_id,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="url != null">
        url,
      </if>
      <if test="pid != null">
        pid,
      </if>
      <if test="order != null">
        order,
      </if>
      <if test="createBy != null">
        create_by,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateBy != null">
        update_by,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="menuId != null">
        #{menuId,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="url != null">
        #{url,jdbcType=VARCHAR},
      </if>
      <if test="pid != null">
        #{pid,jdbcType=VARCHAR},
      </if>
      <if test="order != null">
        #{order,jdbcType=INTEGER},
      </if>
      <if test="createBy != null">
        #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        #{updateBy,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=BIT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="org.fh.entity.common.MenuEntity">
    update biz_menu
    <set>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="url != null">
        url = #{url,jdbcType=VARCHAR},
      </if>
      <if test="pid != null">
        pid = #{pid,jdbcType=VARCHAR},
      </if>
      <if test="order != null">
        order = #{order,jdbcType=INTEGER},
      </if>
      <if test="createBy != null">
        create_by = #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        update_by = #{updateBy,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=BIT},
      </if>
    </set>
    where menu_id = #{menuId,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="org.fh.entity.common.MenuEntity">
    update biz_menu
    set name = #{name,jdbcType=VARCHAR},
      url = #{url,jdbcType=VARCHAR},
      pid = #{pid,jdbcType=VARCHAR},
      order = #{order,jdbcType=INTEGER},
      create_by = #{createBy,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_by = #{updateBy,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      deleted = #{deleted,jdbcType=BIT}
    where menu_id = #{menuId,jdbcType=VARCHAR}
  </update>
</mapper>