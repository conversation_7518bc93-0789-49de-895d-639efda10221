<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.fh.mapper.dsno1.common.BorrowUserMapper">
    <resultMap id="BaseResultMap" type="org.fh.vo.common.BorrowUserVo">
        <result column="processinstanceid" jdbcType="VARCHAR" property="processinstanceid"/>
        <result column="emp_id" jdbcType="VARCHAR" property="empId"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
    </resultMap>
    <sql id="Base_Column_List">
    borrow_user_id, processinstanceid, emp_id
  </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from biz_borrow_user
        where borrow_user_id = #{borrowUserId,jdbcType=VARCHAR}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from biz_borrow_user
    where borrow_user_id = #{borrowUserId,jdbcType=VARCHAR}
  </delete>
    <insert id="insert" parameterType="org.fh.entity.common.BorrowUserEntity">
    insert into biz_borrow_user (borrow_user_id, processinstanceid, 
      emp_id)
    values (#{borrowUserId,jdbcType=VARCHAR}, #{processinstanceid,jdbcType=VARCHAR}, 
      #{empId,jdbcType=VARCHAR})
  </insert>
    <insert id="insertSelective" parameterType="org.fh.entity.common.BorrowUserEntity">
        insert into biz_borrow_user
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="borrowUserId != null">
                borrow_user_id,
            </if>
            <if test="processinstanceid != null">
                processinstanceid,
            </if>
            <if test="empId != null">
                emp_id,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="borrowUserId != null">
                #{borrowUserId,jdbcType=VARCHAR},
            </if>
            <if test="processinstanceid != null">
                #{processinstanceid,jdbcType=VARCHAR},
            </if>
            <if test="empId != null">
                #{empId,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="org.fh.entity.common.BorrowUserEntity">
        update biz_borrow_user
        <set>
            <if test="processinstanceid != null">
                processinstanceid = #{processinstanceid,jdbcType=VARCHAR},
            </if>
            <if test="empId != null">
                emp_id = #{empId,jdbcType=VARCHAR},
            </if>
        </set>
        where borrow_user_id = #{borrowUserId,jdbcType=VARCHAR}
    </update>
    <update id="updateByPrimaryKey" parameterType="org.fh.entity.common.BorrowUserEntity">
        update biz_borrow_user
        set processinstanceid = #{processinstanceid,jdbcType=VARCHAR},
          emp_id = #{empId,jdbcType=VARCHAR}
        where borrow_user_id = #{borrowUserId,jdbcType=VARCHAR}
      </update>

    <!-- 根据流程Id查询借调人员 -->
    <select id="queryBorrowUserList" parameterType="string" resultMap="BaseResultMap">
        select  bu.processinstanceid,bu.emp_id,e.name
        from biz_borrow_user bu
        left join biz_employee e on e.emp_id = bu.emp_id
        where bu.processinstanceid = #{processinstanceid}
    </select>

    <!-- 批量添加借调人员 -->
    <insert id="addBorrowUserList" parameterType="list">
        insert into biz_borrow_user(borrow_user_id,processinstanceid,emp_id)
        values
        <foreach collection="list" separator="," item="item">
            (
            #{item.borrowUserId},#{item.processinstanceid},#{item.empId}
            )
        </foreach>
    </insert>

    <!-- 查询对应日期内员工是否属于借调状态 -->
    <select id="queryBorrowUserDetail" resultType="int">
        select count(*) from biz_borrow_user bu
        left join biz_borrow b on bu.processinstanceid = b.processinstanceid
        where b.start_time &lt;= #{borrowDate} and b.end_time &gt;= #{borrowDate}
        and bu.emp_id = #{empId}
    </select>

    <!-- 查询员工是否属于借调状态 -->
    <select id="queryBorrowUserByIdList" resultType="string">
        select distinct emp_id from biz_borrow_user bu
        left join biz_borrow b on bu.processinstanceid = b.processinstanceid
        where b.start_time &lt;= #{borrowDate} and b.end_time &gt;= #{borrowDate}
        and bu.emp_id in (
        <foreach collection="list" separator="," item="item">
            #{item}
        </foreach>
        )
    </select>
</mapper>