<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.fh.mapper.dsno1.common.ScheduleRuleMapper">
    <resultMap id="BaseResultMap" type="org.fh.vo.common.ScheduleRuleVo">
        <id column="schedule_rule_id" jdbcType="VARCHAR" property="scheduleRuleId"/>
        <result column="plant" jdbcType="VARCHAR" property="plant"/>
        <result column="work_team" jdbcType="VARCHAR" property="workTeam"/>
        <result column="workshop" jdbcType="VARCHAR" property="workshop"/>
        <result column="start_date" jdbcType="VARCHAR" property="startDate"/>
        <result column="cycle" jdbcType="INTEGER" property="cycle"/>
        <result column="cycle_day" jdbcType="INTEGER" property="cycleDay"/>
        <result column="rest_day" jdbcType="INTEGER" property="restDay"/>
        <result column="work_day" jdbcType="INTEGER" property="workDay"/>
        <result column="day_format" jdbcType="VARCHAR" property="dayFormat"/>
        <result column="work_type_one" jdbcType="VARCHAR" property="workTypeOne"/>
        <result column="work_type_two" jdbcType="VARCHAR" property="workTypeTwo"/>
        <result column="work_type_three" jdbcType="VARCHAR" property="workTypeThree"/>
        <result column="work_type_four" jdbcType="VARCHAR" property="workTypeFour"/>
        <result column="work_type_five" jdbcType="VARCHAR" property="workTypeFive"/>
        <result column="update_valid" jdbcType="INTEGER" property="updateValid"/>
        <result column="create_by" jdbcType="VARCHAR" property="createBy"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_by" jdbcType="VARCHAR" property="updateBy"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>
    <sql id="Base_Column_List">
    schedule_rule_id, plant, work_team, cycle, work_type_one, work_type_two, work_type_three, 
    work_type_four, work_type_five, update_valid, create_by, create_time, update_by, 
    update_time
  </sql>
    <update id="updateByPrimaryKeySelective" parameterType="org.fh.entity.common.ScheduleRuleEntity">
        update biz_schedule_rule
        <set>
            <if test="plant != null">
                plant = #{plant,jdbcType=VARCHAR},
            </if>
            <if test="workTeam != null">
                work_team = #{workTeam,jdbcType=VARCHAR},
            </if>
            <if test="cycle != null">
                cycle = #{cycle,jdbcType=INTEGER},
            </if>
            <if test="workTypeOne != null">
                work_type_one = #{workTypeOne,jdbcType=VARCHAR},
            </if>
            <if test="workTypeTwo != null">
                work_type_two = #{workTypeTwo,jdbcType=VARCHAR},
            </if>
            <if test="workTypeThree != null">
                work_type_three = #{workTypeThree,jdbcType=VARCHAR},
            </if>
            <if test="workTypeFour != null">
                work_type_four = #{workTypeFour,jdbcType=VARCHAR},
            </if>
            <if test="workTypeFive != null">
                work_type_five = #{workTypeFive,jdbcType=VARCHAR},
            </if>
            <if test="updateValid != null">
                update_valid = #{updateValid,jdbcType=INTEGER},
            </if>
            <if test="createBy != null">
                create_by = #{createBy,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateBy != null">
                update_by = #{updateBy,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where schedule_rule_id = #{scheduleRuleId,jdbcType=VARCHAR}
    </update>
    <update id="updateByPrimaryKey" parameterType="org.fh.entity.common.ScheduleRuleEntity">
        update biz_schedule_rule
        set plant = #{plant,jdbcType=VARCHAR},
          work_team = #{workTeam,jdbcType=VARCHAR},
          cycle = #{cycle,jdbcType=INTEGER},
          work_type_one = #{workTypeOne,jdbcType=VARCHAR},
          work_type_two = #{workTypeTwo,jdbcType=VARCHAR},
          work_type_three = #{workTypeThree,jdbcType=VARCHAR},
          work_type_four = #{workTypeFour,jdbcType=VARCHAR},
          work_type_five = #{workTypeFive,jdbcType=VARCHAR},
          update_valid = #{updateValid,jdbcType=INTEGER},
          create_by = #{createBy,jdbcType=VARCHAR},
          create_time = #{createTime,jdbcType=TIMESTAMP},
          update_by = #{updateBy,jdbcType=VARCHAR},
          update_time = #{updateTime,jdbcType=TIMESTAMP}
        where schedule_rule_id = #{scheduleRuleId,jdbcType=VARCHAR}
      </update>

    <select id="queryWorkTeamRuleAll" resultMap="BaseResultMap">
        select sr.schedule_rule_id,sr.plant,sr.work_team,wg.workshop,sr.cycle,sr.work_type_one,sr.work_type_two,sr.work_type_three,
        start_date,sr.work_type_four,sr.work_type_five,sr.update_valid,wg.cycle_day,wg.rest_day,wg.work_day,wg.day_format
        from biz_schedule_rule sr
        inner join biz_work_group wg on wg.name = sr.work_team
        and wg.deleted = '0'
        <where>
            <if test="plant !=null and plant!=''">
                and wg.plant = #{plant}
            </if>
            <if test="workshop !=null and workshop!=''">
                and wg.workshop = #{workshop}
            </if>
        </where>
    </select>

    <!-- 添加加班规则 -->
    <insert id="addScheduleRule" parameterType="org.fh.entity.common.ScheduleRuleEntity">
        insert into biz_schedule_rule
        <trim prefix="(" suffix=")" suffixOverrides=",">
            schedule_rule_id,
            <if test="plant != null and plant !=''">
                plant,
            </if>
            <if test="workshop != null and workshop !=''">
                workshop,
            </if>
            <if test="startDate != null and startDate !=''">
                start_date,
            </if>
            <if test="workTeam != null and workTeam !=''">
                work_team,
            </if>
            <if test="cycle != null">
                cycle,
            </if>
            <if test="workTypeOne != null and workTypeOne !=''">
                work_type_one,
            </if>
            <if test="workTypeTwo != null and workTypeTwo !=''">
                work_type_two,
            </if>
            <if test="workTypeThree != null and workTypeThree !=''">
                work_type_three,
            </if>
            <if test="workTypeFour != null and workTypeFour !=''">
                work_type_four,
            </if>
            <if test="workTypeFive != null and workTypeFive !=''">
                work_type_five,
            </if>
            <if test="updateValid != null">
                update_valid,
            </if>
            <if test="createBy != null and createBy !=''">
                create_by,
            </if>
                create_time
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                #{scheduleRuleId},
            <if test="plant != null and plant !=''">
                #{plant},
            </if>
            <if test="workshop != null and workshop !=''">
                #{workshop},
            </if>
            <if test="startDate != null and startDate !=''">
                #{startDate},
            </if>
            <if test="workTeam != null and workTeam !=''">
                #{workTeam},
            </if>
            <if test="cycle != null">
                #{cycle},
            </if>
            <if test="workTypeOne != null and workTypeOne !=''">
                #{workTypeOne},
            </if>
            <if test="workTypeTwo != null and workTypeTwo !=''">
                #{workTypeTwo},
            </if>
            <if test="workTypeThree != null and workTypeThree !=''">
                #{workTypeThree},
            </if>
            <if test="workTypeFour != null and workTypeFour !=''">
                #{workTypeFour},
            </if>
            <if test="workTypeFive != null and workTypeFive !=''">
                #{workTypeFive},
            </if>
            <if test="updateValid != null">
                #{updateValid},
            </if>
            <if test="createBy != null and createBy !=''">
                #{createBy},
            </if>
            now()
        </trim>
    </insert>

    <select id="queryScheduleRulePage" resultMap="BaseResultMap" parameterType="org.fh.entity.common.ScheduleRuleEntity">
        select schedule_rule_id, plant,workshop,work_team, cycle, work_type_one, work_type_two,
        work_type_three,
        work_type_four, work_type_five, update_valid,start_date
        from biz_schedule_rule
        <where>
            <if test="workTeam !=null and workTeam!=''">
                work_team like CONCAT(CONCAT('%', #{workTeam}),'%')
            </if>
        </where>
    </select>

    <!-- 单个修改排班规则 -->
    <update id="updateScheduleRule" parameterType="org.fh.entity.common.ScheduleRuleEntity">
        update biz_schedule_rule
        <set>
            <if test="cycle != null">
                cycle = #{cycle},
            </if>
            <if test="workTypeOne != null and workTypeOne !=''">
                work_type_one = #{workTypeOne},
            </if>
            <if test="workTypeTwo != null and workTypeTwo !=''">
                work_type_two = #{workTypeTwo},
            </if>
            <if test="workTypeThree != null and workTypeThree !=''">
                work_type_three = #{workTypeThree},
            </if>
            <if test="workTypeFour != null and workTypeFour !=''">
                work_type_four = #{workTypeFour},
            </if>
            <if test="workTypeFive != null and workTypeFive !=''">
                work_type_five = #{workTypeFive},
            </if>
            <if test="updateValid != null">
                update_valid = #{updateValid},
            </if>
            <if test="updateBy != null and updateBy !=''">
                update_by = #{updateBy},
            </if>
                update_time = now()
        </set>
        where schedule_rule_id = #{scheduleRuleId}
    </update>

    <select id="checkScheduleRuleWorkTeam" parameterType="string" resultType="integer">
        select count(*)
        from biz_schedule_rule
        where work_team = #{workTeam}
    </select>

    <!-- 单个删除排班规则 -->
    <delete id="deleteScheduleRule" parameterType="org.fh.entity.common.ScheduleRuleEntity">
        delete from biz_schedule_rule where schedule_rule_id = #{scheduleRuleId}
    </delete>
</mapper>