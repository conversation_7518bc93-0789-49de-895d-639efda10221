<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.fh.mapper.dsno1.common.PlVsmMapper">
	
	<!--表名 -->
	<sql id="tableName">
		BIZ_PL_VSM
	</sql>
	
	<!--数据字典表名 -->
	<sql id="dicTableName">
		SYS_DICTIONARIES
	</sql>
	
	<!-- 字段 -->
	<sql id="Field">
		f.PL,	
		f.VSM,	
		f.CREATED_BY,	
		f.CREATED_TIME,	
		f.UPDATED_BY,	
		f.UPDATED_TIME,	
		f.ID
	</sql>
	
	<!-- 字段用于新增 -->
	<sql id="Field2">
		PL,	
		VSM,	
		CREATED_BY,	
		CREATED_TIME,	
		UPDATED_BY,	
		UPDATED_TIME,	
		ID
	</sql>
	
	<!-- 字段值 -->
	<sql id="FieldValue">
		#{PL},	
		#{VSM},	
		#{CREATED_BY},	
		#{CREATED_TIME},	
		#{UPDATED_BY},	
		#{UPDATED_TIME},	
		#{ID}
	</sql>

	<!-- DTO字段值 -->
	<sql id="DtoFieldValue">
		#{pl},	
		#{vsm},	
		#{createBy},	
		now(),	
		#{createBy},	
		now(),
		#{plVsmId}
	</sql>
	
	<!-- 新增-->
	<insert id="save" parameterType="pd">
		insert into 
	<include refid="tableName"></include>
		(
	<include refid="Field2"></include>
		) values (
	<include refid="FieldValue"></include>
		)
	</insert>

	<!-- 添加产线VSM对照 -->
	<insert id="add" parameterType="org.fh.dto.common.plvsm.PlVsmAddDto">
		insert into
		<include refid="tableName"></include>
		(
		<include refid="Field2"></include>
		) values (
		<include refid="DtoFieldValue"></include>
		)
	</insert>

	<!-- 更新产线VSM对照 -->
	<update id="update" parameterType="org.fh.dto.common.plvsm.PlVsmAddDto">
		update
		<include refid="tableName"></include>
		set 
			PL = #{pl},
			VSM = #{vsm},
			UPDATED_BY = #{createBy},
			UPDATED_TIME = now()
		where 
			ID = #{plVsmId}
	</update>

	<!-- 删除产线VSM对照 -->
	<delete id="deleteByDto" parameterType="org.fh.dto.common.plvsm.PlVsmAddDto">
		delete from
		<include refid="tableName"></include>
		where 
			ID = #{plVsmId}
	</delete>

	<!-- 分页查询产线VSM对照 -->
	<select id="queryPage" parameterType="org.fh.dto.common.plvsm.PlVsmQueryDto" resultType="pd">
		select 
			f.ID as plVsmId,
			f.PL as pl,
			f.VSM as vsm,
			f.CREATED_BY as createdBy,
			f.CREATED_TIME as createdTime,
			f.UPDATED_BY as updatedBy,
			f.UPDATED_TIME as updatedTime
		from 
		<include refid="tableName"></include> f
		where 1=1
		<if test="pl != null and pl != ''">
			and f.PL LIKE CONCAT('%', #{pl}, '%')
		</if>
		<if test="vsm != null and vsm != ''">
			and f.VSM LIKE CONCAT('%', #{vsm}, '%')
		</if>
		order by f.CREATED_TIME desc
	</select>

	<!-- 查询产线VSM对照明细 -->
	<select id="queryDetail" parameterType="string" resultType="pd">
		select 
			f.ID as plVsmId,
			f.PL as pl,
			f.VSM as vsm,
			f.CREATED_BY as createdBy,
			f.CREATED_TIME as createdTime,
			f.UPDATED_BY as updatedBy,
			f.UPDATED_TIME as updatedTime
		from 
		<include refid="tableName"></include> f
		where 
			f.ID = #{plVsmId}
	</select>

	<!-- 检查指定产线和VSM组合是否已存在 -->
	<select id="checkExistsByPlAndVsm" resultType="int">
		select count(*)
		from 
		<include refid="tableName"></include>
		where 
			PL = #{param1}
			and VSM = #{param2}
	</select>

	<!-- 检查指定产线和VSM组合是否已存在，排除指定ID的记录 -->
	<select id="checkExistsByPlAndVsmExcludeId" resultType="int">
		select count(*)
		from 
		<include refid="tableName"></include>
		where 
			PL = #{param1}
			and VSM = #{param2}
			and ID != #{param3}
	</select>

	<!-- 下载产线VSM对照数据 -->
	<select id="download" resultType="org.fh.model.exportModel.PlVsmExportModel">
		select 
			f.PL as pl,
			f.VSM as vsm
		from 
		<include refid="tableName"></include> f
		order by f.PL, f.VSM
	</select>

	<!-- 根据产线和VSM查询对照记录 -->
	<select id="findByPlAndVsm" resultType="org.fh.dto.common.plvsm.PlVsmAddDto">
		select 
			f.ID as plVsmId,
			f.PL as pl,
			f.VSM as vsm,
			f.CREATED_BY as createBy
		from 
		<include refid="tableName"></include> f
		where 
			f.PL = #{param1}
			and f.VSM = #{param2}
	</select>

	<!-- 根据产线和VSM更新对照信息 -->
	<update id="updateByPlAndVsm" parameterType="org.fh.dto.common.plvsm.PlVsmAddDto">
		update
		<include refid="tableName"></include>
		set 
			UPDATED_BY = #{createBy},
			UPDATED_TIME = now()
		where 
			PL = #{pl}
			and VSM = #{vsm}
	</update>
	
	<!-- 删除-->
	<delete id="delete" parameterType="pd">
		delete from
		<include refid="tableName"></include>
		where 
			ID = #{ID}
	</delete>
	
	<!-- 修改 -->
	<update id="edit" parameterType="pd">
		update
		<include refid="tableName"></include>
		set 
			PL = #{PL},
			VSM = #{VSM},
			CREATED_BY = #{CREATED_BY},
			CREATED_TIME = #{CREATED_TIME},
			UPDATED_BY = #{UPDATED_BY},
			UPDATED_TIME = #{UPDATED_TIME}
		where 
			ID = #{ID}
	</update>
	
	<!-- 通过ID获取数据 -->
	<select id="findById" parameterType="pd" resultType="pd">
		select 
		<include refid="Field"></include>
		from 
		<include refid="tableName"></include> f
		where 
			f.ID = #{ID}
	</select>
	
	<!-- 列表 -->
	<select id="datalistPage" parameterType="page" resultType="pd">
		select
		<include refid="Field"></include>
		from 
		<include refid="tableName"></include> f
		where 1=1
		<if test="pd.KEYWORDS != null and pd.KEYWORDS != ''"><!-- 关键词检索 -->
			and
				(
					f.PL LIKE CONCAT(CONCAT('%', #{pd.KEYWORDS}),'%')
					 or 
					f.VSM LIKE CONCAT(CONCAT('%', #{pd.KEYWORDS}),'%') 
				)
		</if>
		order by f.CREATED_TIME desc
	</select>
	
	<!-- 列表(全部) -->
	<select id="listAll" parameterType="pd" resultType="pd">
		select
		<include refid="Field"></include>
		from 
		<include refid="tableName"></include> f
		order by f.CREATED_TIME desc
	</select>
	
	<!-- 批量删除 -->
	<delete id="deleteAll" parameterType="String">
		delete from
		<include refid="tableName"></include>
		where 
			ID in
		<foreach item="item" index="index" collection="array" open="(" separator="," close=")">
                 #{item}
		</foreach>
	</delete>
	
	<!-- fh313596790qq(青苔) -->
</mapper>