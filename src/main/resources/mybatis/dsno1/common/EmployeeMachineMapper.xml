<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.fh.mapper.dsno1.common.EmployeeMachineMapper">
    <resultMap id="BaseResultMap" type="org.fh.entity.common.EmployeeMachineEntity">
        <id column="emp_machine_id" jdbcType="VARCHAR" property="empMachineId"/>
        <result column="emp_id" jdbcType="VARCHAR" property="empId"/>
        <result column="machine_sn" jdbcType="VARCHAR" property="machineSn"/>
    </resultMap>
    <sql id="Base_Column_List">
    emp_machine_id, emp_id, machine_sn
  </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from biz_employee_machine
        where emp_machine_id = #{empMachineId,jdbcType=VARCHAR}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from biz_employee_machine
    where emp_machine_id = #{empMachineId,jdbcType=VARCHAR}
  </delete>
    <insert id="insert" parameterType="org.fh.entity.common.EmployeeMachineEntity">
    insert into biz_employee_machine (emp_machine_id, emp_id, machine_sn
      )
    values (#{empMachineId,jdbcType=VARCHAR}, #{empId,jdbcType=VARCHAR}, #{machineSn,jdbcType=VARCHAR}
      )
  </insert>
    <insert id="insertSelective" parameterType="org.fh.entity.common.EmployeeMachineEntity">
        insert into biz_employee_machine
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="empMachineId != null">
                emp_machine_id,
            </if>
            <if test="empId != null">
                emp_id,
            </if>
            <if test="machineSn != null">
                machine_sn,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="empMachineId != null">
                #{empMachineId,jdbcType=VARCHAR},
            </if>
            <if test="empId != null">
                #{empId,jdbcType=VARCHAR},
            </if>
            <if test="machineSn != null">
                #{machineSn,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="org.fh.entity.common.EmployeeMachineEntity">
        update biz_employee_machine
        <set>
            <if test="empId != null">
                emp_id = #{empId,jdbcType=VARCHAR},
            </if>
            <if test="machineSn != null">
                machine_sn = #{machineSn,jdbcType=VARCHAR},
            </if>
        </set>
        where emp_machine_id = #{empMachineId,jdbcType=VARCHAR}
    </update>
    <update id="updateByPrimaryKey" parameterType="org.fh.entity.common.EmployeeMachineEntity">
        update biz_employee_machine
        set emp_id = #{empId,jdbcType=VARCHAR},
          machine_sn = #{machineSn,jdbcType=VARCHAR}
        where emp_machine_id = #{empMachineId,jdbcType=VARCHAR}
    </update>

    <!-- 批量删除员工与考勤机关联关系 -->
    <delete id="deleteEmployeeMachineList" parameterType="collection">
        delete from biz_employee_machine where emp_id in
        <foreach collection="ids" separator="," close=")" open="(" item="item">
            #{item}
        </foreach>
    </delete>

    <!-- 批量添加员工与考勤机关联关系 -->
    <insert id="addEmployeeMachineList" parameterType="collection">
        insert into biz_employee_machine
        (emp_machine_id,emp_id,machine_sn)
        values
        <foreach collection="list" item="item" separator=",">
            (
              #{item.empMachineId},#{item.empId},#{item.machineSn}
            )
        </foreach>
    </insert>
</mapper>