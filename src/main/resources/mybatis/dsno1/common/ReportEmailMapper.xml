<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.fh.mapper.dsno1.common.ReportEmailMapper">
	
	<!--表名 -->
	<sql id="tableName">
		BIZ_REPORT_EMAIL
	</sql>
	
	<!--数据字典表名 -->
	<sql id="dicTableName">
		SYS_DICTIONARIES
	</sql>
	
	<!-- 字段 -->
	<sql id="Field">
		f.PLANT,	
		f.WORKSHOP,	
		f.LEADER_EMAIL,	
		f.MANAGER_EMAIL,	
		f.DIRECTOR_EMAIL,	
		f.CREATE_BY,	
		f.CREATE_TIME,	
		f.UPDATE_BY,	
		f.UPDATE_TIME,
		f.EMAIL_ID
	</sql>
	
	<!-- 字段用于新增 -->
	<sql id="Field2">
		PLANT,	
		WORKSHOP,	
		LEADER_EMAIL,	
		MANAGER_EMAIL,	
		DIRECTOR_EMAIL,	
		CREATE_BY,	
		CREATE_TIME,	
		UPDATE_BY,	
		UPDATE_TIME,
		EMAIL_ID
	</sql>
	
	<!-- 字段值 -->
	<sql id="FieldValue">
		#{PLANT},	
		#{WORKSHOP},	
		#{LEADER_EMAIL},	
		#{MANAGER_EMAIL},	
		#{DIRECTOR_EMAIL},	
		#{CREATE_BY},	
		#{CREATE_TIME},	
		#{UPDATE_BY},	
		#{UPDATE_TIME},
		#{EMAIL_ID}
	</sql>

	<!-- DTO字段值 -->
	<sql id="DtoFieldValue">
		#{plant},	
		#{workshop},	
		#{leaderEmail},	
		#{managerEmail},	
		#{directorEmail},	
		#{createBy},	
		now(),	
		#{createBy},	
		now(),
		#{emailId}
	</sql>
	
	<!-- 新增-->
	<insert id="save" parameterType="pd">
		insert into 
	<include refid="tableName"></include>
		(
	<include refid="Field2"></include>
		) values (
	<include refid="FieldValue"></include>
		)
	</insert>

	<insert id="add" parameterType="org.fh.dto.common.email.ReportEmailAddDto">
		insert into
		<include refid="tableName"></include>
		(
		<include refid="Field2"></include>
		) values (
		<include refid="DtoFieldValue"></include>
		)
	</insert>

	<!-- 更新邮件接收人 -->
	<update id="update" parameterType="org.fh.dto.common.email.ReportEmailAddDto">
		update
		<include refid="tableName"></include>
		set 
			PLANT = #{plant},
			WORKSHOP = #{workshop},
			LEADER_EMAIL = #{leaderEmail},
			MANAGER_EMAIL = #{managerEmail},
			DIRECTOR_EMAIL = #{directorEmail},
			UPDATE_BY = #{createBy},
			UPDATE_TIME = now()
		where 
			EMAIL_ID = #{emailId}
	</update>

	<!-- 删除邮件接收人 -->
	<delete id="deleteByDto" parameterType="org.fh.dto.common.email.ReportEmailAddDto">
		delete from
		<include refid="tableName"></include>
		where 
			EMAIL_ID = #{emailId}
	</delete>

	<!-- 分页查询邮件接收人 -->
	<select id="queryPage" parameterType="org.fh.dto.common.email.ReportEmailQueryDto" resultType="pd">
		select
		<include refid="Field"></include>
		from 
		<include refid="tableName"></include> f
		where 1=1
		<if test="plant != null and plant != ''">
			and f.PLANT = #{plant}
		</if>
		<if test="workshop != null and workshop != ''">
			and f.WORKSHOP = #{workshop}
		</if>
		order by f.CREATE_TIME desc
	</select>

	<!-- 查询邮件接收人明细 -->
	<select id="queryDetail" parameterType="string" resultType="pd">
		select 
		<include refid="Field"></include>
		from 
		<include refid="tableName"></include> f
		where 
			f.EMAIL_ID = #{emailId}
	</select>

	<!-- 检查指定厂区和车间下是否已存在邮件接收人 -->
	<select id="checkExistsByPlantAndWorkshop" resultType="int">
		select count(1)
		from 
		<include refid="tableName"></include> f
		where 
			f.PLANT = #{plant}
			and f.WORKSHOP = #{workshop}
	</select>

	<!-- 检查指定厂区和车间下是否已存在邮件接收人，排除指定ID的记录 -->
	<select id="checkExistsByPlantAndWorkshopExcludeId" resultType="int">
		select count(1)
		from 
		<include refid="tableName"></include> f
		where 
			f.PLANT = #{plant}
			and f.WORKSHOP = #{workshop}
			and f.EMAIL_ID != #{excludeEmailId}
	</select>

	<!-- 下载邮件接收人数据 -->
	<select id="download" resultType="org.fh.model.exportModel.ReportEmailExportModel">
		select 
			f.PLANT as plant,
			f.WORKSHOP as workshop,
			f.LEADER_EMAIL as leaderEmail,
			f.MANAGER_EMAIL as managerEmail,
			f.DIRECTOR_EMAIL as directorEmail
		from 
		<include refid="tableName"></include> f
		order by f.PLANT, f.WORKSHOP
	</select>

	<!-- 根据厂区和车间查询邮件接收人记录 -->
	<select id="findByPlantAndWorkshop" resultType="org.fh.dto.common.email.ReportEmailAddDto">
		select 
			f.EMAIL_ID as emailId,
			f.PLANT as plant,
			f.WORKSHOP as workshop,
			f.LEADER_EMAIL as leaderEmail,
			f.MANAGER_EMAIL as managerEmail,
			f.DIRECTOR_EMAIL as directorEmail,
			f.CREATE_BY as createBy
		from 
		<include refid="tableName"></include> f
		where 
			f.PLANT = #{param1}
			and f.WORKSHOP = #{param2}
	</select>

	<!-- 根据厂区和车间更新邮件接收人信息 -->
	<update id="updateByPlantAndWorkshop" parameterType="org.fh.dto.common.email.ReportEmailAddDto">
		update
		<include refid="tableName"></include>
		set 
			LEADER_EMAIL = #{leaderEmail},
			MANAGER_EMAIL = #{managerEmail},
			DIRECTOR_EMAIL = #{directorEmail},
			UPDATE_BY = #{createBy},
			UPDATE_TIME = now()
		where 
			PLANT = #{plant}
			and WORKSHOP = #{workshop}
	</update>
	
	<!-- 删除-->
	<delete id="delete" parameterType="pd">
		delete from
		<include refid="tableName"></include>
		where 
			EMAIL_ID = #{EMAIL_ID}
	</delete>
	
	<!-- 修改 -->
	<update id="edit" parameterType="pd">
		update
		<include refid="tableName"></include>
		set 
			PLANT = #{PLANT},
			WORKSHOP = #{WORKSHOP},
			LEADER_EMAIL = #{LEADER_EMAIL},
			MANAGER_EMAIL = #{MANAGER_EMAIL},
			DIRECTOR_EMAIL = #{DIRECTOR_EMAIL},
			CREATE_BY = #{CREATE_BY},
			CREATE_TIME = #{CREATE_TIME},
			UPDATE_BY = #{UPDATE_BY},
			UPDATE_TIME = #{UPDATE_TIME},
			EMAIL_ID = EMAIL_ID
		where 
			EMAIL_ID = #{EMAIL_ID}
	</update>
	
	<!-- 通过ID获取数据 -->
	<select id="findById" parameterType="pd" resultType="pd">
		select 
		<include refid="Field"></include>
		from 
		<include refid="tableName"></include> f
		where 
			f.EMAIL_ID = #{EMAIL_ID}
	</select>
	
	<!-- 列表 -->
	<select id="datalistPage" parameterType="page" resultType="pd">
		select
		<include refid="Field"></include>
		from 
		<include refid="tableName"></include> f
		where 1=1
		<if test="pd.KEYWORDS != null and pd.KEYWORDS != ''"><!-- 关键词检索 -->
			and
				(
				<!--	根据需求自己加检索条件
					字段1 LIKE CONCAT(CONCAT('%', #{pd.KEYWORDS}),'%')
					 or 
					字段2 LIKE CONCAT(CONCAT('%', #{pd.KEYWORDS}),'%') 
				-->
				)
		</if>
	</select>
	
	<!-- 列表(全部) -->
	<select id="listAll" parameterType="pd" resultType="pd">
		select
		<include refid="Field"></include>
		from 
		<include refid="tableName"></include> f
	</select>
	
	<!-- 批量删除 -->
	<delete id="deleteAll" parameterType="String">
		delete from
		<include refid="tableName"></include>
		where 
			EMAIL_ID in
		<foreach item="item" index="index" collection="array" open="(" separator="," close=")">
                 #{item}
		</foreach>
	</delete>
</mapper>