<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.fh.mapper.dsno1.common.ApproveHistoryMapper">
    <resultMap id="BaseResultMap" type="org.fh.vo.common.ApproveHistoryVo">
        <id column="approve_id" jdbcType="VARCHAR" property="approveId"/>
        <result column="approve_user" jdbcType="VARCHAR" property="approveUser"/>
        <result column="approveUserName" jdbcType="VARCHAR" property="approveUserName"/>
        <result column="approve_time" jdbcType="TIMESTAMP" property="approveTime"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="approve_type" jdbcType="INTEGER" property="approveType"/>
        <result column="processinstanceid" jdbcType="VARCHAR" property="processinstanceid"/>
        <result column="approve_status" jdbcType="INTEGER" property="approveStatus"/>
        <result column="approveStatusName" jdbcType="INTEGER" property="approveStatusName"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
    </resultMap>
    <sql id="Base_Column_List">
    approve_id, approve_user, approve_time, remark, approve_type, processinstanceid, 
    approve_status, create_time
  </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from biz_approve_history
        where approve_id = #{approveId,jdbcType=VARCHAR}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from biz_approve_history
    where approve_id = #{approveId,jdbcType=VARCHAR}
  </delete>
    <insert id="insert" parameterType="org.fh.entity.common.ApproveHistoryEntity">
    insert into biz_approve_history (approve_id, approve_user, approve_time, 
      remark, approve_type, processinstanceid, 
      approve_status, create_time)
    values (#{approveId,jdbcType=VARCHAR}, #{approveUser,jdbcType=VARCHAR}, #{approveTime,jdbcType=TIMESTAMP}, 
      #{remark,jdbcType=VARCHAR}, #{approveType,jdbcType=INTEGER}, #{processinstanceid,jdbcType=VARCHAR}, 
      #{approveStatus,jdbcType=INTEGER}, #{createTime,jdbcType=TIMESTAMP})
  </insert>
    <insert id="insertSelective" parameterType="org.fh.entity.common.ApproveHistoryEntity">
        insert into biz_approve_history
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="approveId != null">
                approve_id,
            </if>
            <if test="approveUser != null">
                approve_user,
            </if>
            <if test="approveTime != null">
                approve_time,
            </if>
            <if test="remark != null">
                remark,
            </if>
            <if test="approveType != null">
                approve_type,
            </if>
            <if test="processinstanceid != null">
                processinstanceid,
            </if>
            <if test="approveStatus != null">
                approve_status,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="approveId != null">
                #{approveId,jdbcType=VARCHAR},
            </if>
            <if test="approveUser != null">
                #{approveUser,jdbcType=VARCHAR},
            </if>
            <if test="approveTime != null">
                #{approveTime,jdbcType=TIMESTAMP},
            </if>
            <if test="remark != null">
                #{remark,jdbcType=VARCHAR},
            </if>
            <if test="approveType != null">
                #{approveType,jdbcType=INTEGER},
            </if>
            <if test="processinstanceid != null">
                #{processinstanceid,jdbcType=VARCHAR},
            </if>
            <if test="approveStatus != null">
                #{approveStatus,jdbcType=INTEGER},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="org.fh.entity.common.ApproveHistoryEntity">
        update biz_approve_history
        <set>
            <if test="approveUser != null">
                approve_user = #{approveUser,jdbcType=VARCHAR},
            </if>
            <if test="approveTime != null">
                approve_time = #{approveTime,jdbcType=TIMESTAMP},
            </if>
            <if test="remark != null">
                remark = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="approveType != null">
                approve_type = #{approveType,jdbcType=INTEGER},
            </if>
            <if test="processinstanceid != null">
                processinstanceid = #{processinstanceid,jdbcType=VARCHAR},
            </if>
            <if test="approveStatus != null">
                approve_status = #{approveStatus,jdbcType=INTEGER},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where approve_id = #{approveId,jdbcType=VARCHAR}
    </update>
    <update id="updateByPrimaryKey" parameterType="org.fh.entity.common.ApproveHistoryEntity">
    update biz_approve_history
    set approve_user = #{approveUser,jdbcType=VARCHAR},
      approve_time = #{approveTime,jdbcType=TIMESTAMP},
      remark = #{remark,jdbcType=VARCHAR},
      approve_type = #{approveType,jdbcType=INTEGER},
      processinstanceid = #{processinstanceid,jdbcType=VARCHAR},
      approve_status = #{approveStatus,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP}
    where approve_id = #{approveId,jdbcType=VARCHAR}
  </update>


    <!-- 查询流程审片记录历史 -->
    <select id="queryApproveHistoryList" parameterType="string" resultMap="BaseResultMap">
        select h.approve_id,h.approve_user,h.approve_time,h.remark,h.approve_type,h.processinstanceid,e.name as approveUserName,
        h.approve_status,h.create_time,
         case
        when h.approve_status = '1' then '同意'
        when h.approve_status = '2' then '驳回'
        when h.approve_status = '3' then '取消'
        when h.approve_status = '4' then '添加'
        else '提交'
        end as approveStatusName
        from biz_approve_history h
        left join biz_employee e on e.emp_id = h.approve_user
        where h.processinstanceid = #{processinstanceid}
        order by approve_time desc
    </select>

    <insert id="addApproveHistory" parameterType="org.fh.entity.common.ApproveHistoryEntity">
        insert into biz_approve_history
        <trim prefix="(" suffix=")" suffixOverrides=",">
            approve_id,
            <if test="approveUser != null and approveUser !=''">
                approve_user,
            </if>
            approve_time,
            <if test="remark != null and remark !=''">
                remark,
            </if>
            <if test="approveType != null">
                approve_type,
            </if>
            <if test="processinstanceid != null and processinstanceid !=''">
                processinstanceid,
            </if>
            <if test="approveStatus != null">
                approve_status,
            </if>
            create_time,
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            #{approveId},
            <if test="approveUser != null and approveUser !=''">
                #{approveUser,jdbcType=VARCHAR},
            </if>
            now(),
            <if test="remark != null and remark !=''">
                #{remark},
            </if>
            <if test="approveType != null">
                #{approveType},
            </if>
            <if test="processinstanceid != null and processinstanceid !=''">
                #{processinstanceid},
            </if>
            <if test="approveStatus != null">
                #{approveStatus},
            </if>
            now()
        </trim>
    </insert>
</mapper>