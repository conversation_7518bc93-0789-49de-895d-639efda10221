<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.fh.mapper.dsno1.common.ReportHcMapper">
    <resultMap id="BaseResultMap" type="org.fh.vo.common.ReportHcVo">
        <id column="report_hc_id" jdbcType="VARCHAR" property="reportHcId"/>
        <result column="plant" jdbcType="VARCHAR" property="plant"/>
        <result column="workshop" jdbcType="VARCHAR" property="workshop"/>
        <result column="finance_month" jdbcType="VARCHAR" property="financeMonth"/>
        <result column="working_hours" jdbcType="DOUBLE" property="workingHours"/>
        <result column="other_hours" jdbcType="VARCHAR" property="otherHours"/>
    </resultMap>
    <sql id="Base_Column_List">
    report_hc_id, plant, workshop, finance_month, working_hours, other_hours, create_by, 
    create_time
  </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from biz_report_hc
        where report_hc_id = #{reportHcId,jdbcType=VARCHAR}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from biz_report_hc
    where report_hc_id = #{reportHcId,jdbcType=VARCHAR}
  </delete>
    <insert id="insert" parameterType="org.fh.entity.common.ReportHcEntity">
    insert into biz_report_hc (report_hc_id, plant, workshop, 
      finance_month, working_hours, other_hours, 
      create_by, create_time)
    values (#{reportHcId,jdbcType=VARCHAR}, #{plant,jdbcType=VARCHAR}, #{workshop,jdbcType=VARCHAR}, 
      #{financeMonth,jdbcType=VARCHAR}, #{workingHours,jdbcType=DOUBLE}, #{otherHours,jdbcType=VARCHAR}, 
      #{createBy,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP})
  </insert>
    <insert id="insertSelective" parameterType="org.fh.entity.common.ReportHcEntity">
        insert into biz_report_hc
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="reportHcId != null">
                report_hc_id,
            </if>
            <if test="plant != null">
                plant,
            </if>
            <if test="workshop != null">
                workshop,
            </if>
            <if test="financeMonth != null">
                finance_month,
            </if>
            <if test="workingHours != null">
                working_hours,
            </if>
            <if test="otherHours != null">
                other_hours,
            </if>
            <if test="createBy != null">
                create_by,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="reportHcId != null">
                #{reportHcId,jdbcType=VARCHAR},
            </if>
            <if test="plant != null">
                #{plant,jdbcType=VARCHAR},
            </if>
            <if test="workshop != null">
                #{workshop,jdbcType=VARCHAR},
            </if>
            <if test="financeMonth != null">
                #{financeMonth,jdbcType=VARCHAR},
            </if>
            <if test="workingHours != null">
                #{workingHours,jdbcType=DOUBLE},
            </if>
            <if test="otherHours != null">
                #{otherHours,jdbcType=VARCHAR},
            </if>
            <if test="createBy != null">
                #{createBy,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="org.fh.entity.common.ReportHcEntity">
        update biz_report_hc
        <set>
            <if test="plant != null">
                plant = #{plant,jdbcType=VARCHAR},
            </if>
            <if test="workshop != null">
                workshop = #{workshop,jdbcType=VARCHAR},
            </if>
            <if test="financeMonth != null">
                finance_month = #{financeMonth,jdbcType=VARCHAR},
            </if>
            <if test="workingHours != null">
                working_hours = #{workingHours,jdbcType=DOUBLE},
            </if>
            <if test="otherHours != null">
                other_hours = #{otherHours,jdbcType=VARCHAR},
            </if>
            <if test="createBy != null">
                create_by = #{createBy,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where report_hc_id = #{reportHcId,jdbcType=VARCHAR}
    </update>
    <update id="updateByPrimaryKey" parameterType="org.fh.entity.common.ReportHcEntity">
    update biz_report_hc
    set plant = #{plant,jdbcType=VARCHAR},
      workshop = #{workshop,jdbcType=VARCHAR},
      finance_month = #{financeMonth,jdbcType=VARCHAR},
      working_hours = #{workingHours,jdbcType=DOUBLE},
      other_hours = #{otherHours,jdbcType=VARCHAR},
      create_by = #{createBy,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP}
    where report_hc_id = #{reportHcId,jdbcType=VARCHAR}
  </update>

    <!-- 根据月份删除人力报告数据 -->
    <delete id="deleteReportHCByDate" parameterType="string">
        delete from biz_report_hc where finance_month = #{financeMonth}
    </delete>

    <!-- 批量添加人力报告数据 -->
    <insert id="addReportHCList" parameterType="collection">
        insert into biz_report_hc
        (
         report_hc_id,plant,workshop,vsm,finance_month,working_hours,other_hours,create_by,create_time
        )
        values
        <foreach collection="list" separator="," item="item">
            (
            #{item.reportHcId},#{item.plant},#{item.workshop},#{item.vsm},#{item.financeMonth},#{item.workingHours},
            #{item.otherHours},#{item.createBy},now()
            )
        </foreach>
    </insert>

    <!-- 批量修改人力报告数据 -->
    <update id="updateReportHCList" parameterType="collection">
        <foreach collection="list" item="item" separator=";">
            update biz_report_hc
            set other_hours = #{item.otherHours}
            where finance_month = #{item.financeMonth}
            and plant = #{item.plant}
            and workshop = #{item.workshop}
        </foreach>
    </update>

    <!-- 根据财务月查询人力工时汇总报告数据 -->
    <select id="queryReportHCPage" parameterType="org.fh.entity.common.ReportHcEntity" resultMap="BaseResultMap">
        select plant,workshop,finance_month,working_hours,other_hours
        from biz_report_hc
        where finance_month = #{financeMonth}
        <if test="plant !=null and plant !=''">
            and plant = #{plant}
        </if>
        <if test="workshop !=null and workshop !=''">
            and workshop like '%${workshop}%'
        </if>
        <if test="vsm !=null and vsm !=''">
            and vsm like '%${vsm}%'
        </if>
        order by plant asc
    </select>

    <!-- 根据起始截止时间查询人力报告数据 -->
    <select id="queryReportHCByStartDate" resultMap="BaseResultMap">
        select plant,workshop,finance_month,working_hours,other_hours
        from biz_report_hc
        where finance_month &gt;= #{startDate}
        and finance_month &lt;= #{endDate}
        order by plant asc
    </select>
</mapper>