<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.fh.mapper.dsno1.common.OvertimeTypeMapper">
    <resultMap id="BaseResultMap" type="org.fh.vo.common.OvertimeTypeVo">
        <id column="overtime_type_id" jdbcType="VARCHAR" property="overtimeTypeId"/>
        <result column="overtime_type_name" jdbcType="VARCHAR" property="overtimeTypeName"/>
        <result column="multiple" jdbcType="VARCHAR" property="multiple"/>
    </resultMap>
    <sql id="Base_Column_List">
    overtime_type_id, overtime_type_name, multiple, created_by, created_time
  </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from biz_overtime_type
        where overtime_type_id = #{overtimeTypeId,jdbcType=VARCHAR}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from biz_overtime_type
    where overtime_type_id = #{overtimeTypeId,jdbcType=VARCHAR}
  </delete>
    <insert id="insert" parameterType="org.fh.entity.common.OvertimeTypeEntity">
    insert into biz_overtime_type (overtime_type_id, overtime_type_name, multiple,
       created_by, created_time)
    values (#{overtimeTypeId,jdbcType=VARCHAR}, #{overtimeTypeName,jdbcType=VARCHAR}, #{multiple,jdbcType=VARCHAR},
       #{createdBy,jdbcType=VARCHAR}, #{createdTime,jdbcType=TIMESTAMP})
  </insert>
    <insert id="insertSelective" parameterType="org.fh.entity.common.OvertimeTypeEntity">
        insert into biz_overtime_type
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="overtimeTypeId != null">
              overtime_type_id,
            </if>
            <if test="overtimeTypeName != null">
              overtime_type_name,
            </if>
            <if test="multiple != null">
              multiple,
            </if>
            <if test="createdBy != null">
                created_by,
            </if>
            <if test="createdTime != null">
                created_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="overtimeTypeId != null">
                #{overtimeTypeId,jdbcType=VARCHAR},
            </if>
            <if test="overtimeTypeName != null">
                #{overtimeTypeName,jdbcType=VARCHAR},
            </if>
            <if test="multiple != null">
                #{multiple,jdbcType=VARCHAR},
            </if>
            <if test="createdBy != null">
                #{createdBy,jdbcType=VARCHAR},
            </if>
            <if test="createdTime != null">
                #{createdTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="org.fh.entity.common.OvertimeTypeEntity">
        update biz_overtime_type
        <set>
            <if test="overtimeTypeName != null">
              overtime_type_name = #{overtimeTypeName,jdbcType=VARCHAR},
            </if>
            <if test="multiple != null">
              multiple = #{multiple,jdbcType=VARCHAR},
            </if>
            <if test="createdBy != null">
                created_by = #{createdBy,jdbcType=VARCHAR},
            </if>
            <if test="createdTime != null">
                created_time = #{createdTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where overtime_type_id = #{overtimeTypeId,jdbcType=VARCHAR}
    </update>
    <update id="updateByPrimaryKey" parameterType="org.fh.entity.common.OvertimeTypeEntity">
    update biz_overtime_type
    set overtime_type_name = #{overtimeTypeName,jdbcType=VARCHAR},
      multiple = #{multiple,jdbcType=VARCHAR},
      created_by = #{createdBy,jdbcType=VARCHAR},
      created_time = #{createdTime,jdbcType=TIMESTAMP},
    where overtime_type_id = #{overtimeTypeId,jdbcType=VARCHAR}
  </update>

  <!-- 查询加班类别 -->
  <select id="queryOvertimeTypeAll" resultMap="BaseResultMap" >
    select distinct overtime_type_id,overtime_type_name,multiple
    from biz_overtime_type
    order by multiple asc
  </select>


</mapper>