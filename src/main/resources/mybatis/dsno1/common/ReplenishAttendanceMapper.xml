<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.fh.mapper.dsno1.common.ReplenishAttendanceMapper">
    <resultMap id="BaseResultMap" type="org.fh.vo.common.ReplenishAttendanceVo">
        <result column="replenish_user" jdbcType="VARCHAR" property="replenishUser"/>
        <result column="replenish_user_name" jdbcType="VARCHAR" property="replenishUserName"/>
        <result column="replenish_time" jdbcType="TIMESTAMP" property="replenishTime"/>
        <result column="work_type_date" jdbcType="TIMESTAMP" property="workTypeDate"/>
        <result column="replenish_status" jdbcType="INTEGER" property="replenishStatus"/>
        <result column="replenishStatusName" jdbcType="VARCHAR" property="replenishStatusName"/>
        <result column="type" jdbcType="VARCHAR" property="type"/>
        <result column="typeName" jdbcType="VARCHAR" property="typeName"/>
        <result column="reason" jdbcType="VARCHAR" property="reason"/>
        <result column="atta_url" jdbcType="VARCHAR" property="attaUrl"/>
        <result column="open_id" jdbcType="VARCHAR" property="openId"/>
        <result column="status" jdbcType="INTEGER" property="status"/>
        <result column="statusName" jdbcType="INTEGER" property="statusName"/>
        <result column="reject_reason" jdbcType="VARCHAR" property="rejectReason"/>
        <result column="apply_date" jdbcType="VARCHAR" property="applyDate"/>
        <result column="finish_time" jdbcType="VARCHAR" property="finishTime"/>
    </resultMap>
    <sql id="Base_Column_List">
    replenish_id, replenish_user, replenish_time, work_type_date, reason, status, reject_reason
  </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from biz_replenish_attendance
        where replenish_id = #{replenishId,jdbcType=VARCHAR}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from biz_replenish_attendance
    where replenish_id = #{replenishId,jdbcType=VARCHAR}
  </delete>
    <insert id="insert" parameterType="org.fh.entity.common.ReplenishAttendanceEntity">
    insert into biz_replenish_attendance (replenish_id, replenish_user, replenish_time, 
      work_type_date, reason, status, 
      reject_reason)
    values (#{replenishId,jdbcType=VARCHAR}, #{replenishUser,jdbcType=VARCHAR}, #{replenishTime,jdbcType=TIMESTAMP}, 
      #{workTypeDate,jdbcType=TIMESTAMP}, #{reason,jdbcType=VARCHAR}, #{status,jdbcType=INTEGER}, 
      #{rejectReason,jdbcType=VARCHAR})
  </insert>
    <insert id="insertSelective" parameterType="org.fh.entity.common.ReplenishAttendanceEntity">
        insert into biz_replenish_attendance
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="replenishId != null">
                replenish_id,
            </if>
            <if test="replenishUser != null">
                replenish_user,
            </if>
            <if test="replenishTime != null">
                replenish_time,
            </if>
            <if test="workTypeDate != null">
                work_type_date,
            </if>
            <if test="reason != null">
                reason,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="rejectReason != null">
                reject_reason,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="replenishId != null">
                #{replenishId,jdbcType=VARCHAR},
            </if>
            <if test="replenishUser != null">
                #{replenishUser,jdbcType=VARCHAR},
            </if>
            <if test="replenishTime != null">
                #{replenishTime,jdbcType=TIMESTAMP},
            </if>
            <if test="workTypeDate != null">
                #{workTypeDate,jdbcType=TIMESTAMP},
            </if>
            <if test="reason != null">
                #{reason,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                #{status,jdbcType=INTEGER},
            </if>
            <if test="rejectReason != null">
                #{rejectReason,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="org.fh.entity.common.ReplenishAttendanceEntity">
        update biz_replenish_attendance
        <set>
            <if test="replenishUser != null">
                replenish_user = #{replenishUser,jdbcType=VARCHAR},
            </if>
            <if test="replenishTime != null">
                replenish_time = #{replenishTime,jdbcType=TIMESTAMP},
            </if>
            <if test="workTypeDate != null">
                work_type_date = #{workTypeDate,jdbcType=TIMESTAMP},
            </if>
            <if test="reason != null">
                reason = #{reason,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                status = #{status,jdbcType=INTEGER},
            </if>
            <if test="rejectReason != null">
                reject_reason = #{rejectReason,jdbcType=VARCHAR},
            </if>
        </set>
        where replenish_id = #{replenishId,jdbcType=VARCHAR}
    </update>
    <update id="updateByPrimaryKey" parameterType="org.fh.entity.common.ReplenishAttendanceEntity">
        update biz_replenish_attendance
        set replenish_user = #{replenishUser,jdbcType=VARCHAR},
          replenish_time = #{replenishTime,jdbcType=TIMESTAMP},
          work_type_date = #{workTypeDate,jdbcType=TIMESTAMP},
          reason = #{reason,jdbcType=VARCHAR},
          status = #{status,jdbcType=INTEGER},
          reject_reason = #{rejectReason,jdbcType=VARCHAR}
        where replenish_id = #{replenishId,jdbcType=VARCHAR}
      </update>

    <insert id="addReplenishAttendance" parameterType="org.fh.entity.common.ReplenishAttendanceEntity">
      insert into biz_replenish_attendance
      <trim prefix="(" suffix=")" suffixOverrides=",">
        replenish_id,processinstanceid,
        <if test="replenishUser != null and replenishUser !=''">
          replenish_user,
        </if>
        <if test="replenishTime != null">
          replenish_time,
        </if>
        <if test="workTypeDate != null and workTypeDate !=''">
          work_type_date,
        </if>
        <if test="type != null">
          type,
        </if>
        <if test="reason != null and reason !=''">
          reason,
        </if>
        <if test="attaUrl != null and attaUrl !=''">
          atta_url,
        </if>
        replenish_status,
        status,
        apply_date
      </trim>
      <trim prefix="values (" suffix=")" suffixOverrides=",">
        #{replenishId},#{processinstanceid},
        <if test="replenishUser != null and replenishUser !=''">
          #{replenishUser},
        </if>
        <if test="replenishTime != null">
          #{replenishTime},
        </if>
        <if test="workTypeDate != null and workTypeDate !=''">
          #{workTypeDate},
        </if>
        <if test="type != null">
          #{type},
        </if>
        <if test="reason != null and reason !=''">
          #{reason},
        </if>
        <if test="attaUrl != null and attaUrl !=''">
          #{attaUrl},
        </if>
        #{replenishStatus},
        1,
        now()
      </trim>
    </insert>

    <!-- 修改补卡申请状态 -->
    <update id="updateReplenishAttendance" parameterType="org.fh.entity.common.ReplenishAttendanceEntity">
        update biz_replenish_attendance
        <set>
            status = #{status},approve_user = #{approveUser},
            <if test="rejectReason !=null and rejectReason !=''">
                reject_reason = #{rejectReason},
            </if>
            <if test="status == 2">
                finish_time = now(),
            </if>
        </set>
        where processinstanceid = #{processinstanceid}
    </update>

    <!-- 根据流程实例Id查询补卡申请明细 -->
    <select id="queryReplenishDetail" parameterType="string" resultMap="BaseResultMap">
      select ra.replenish_user,
             ra.replenish_time,
             ra.work_type_date,
             ra.reason,
             ra.atta_url,
             ra.status,
             e.open_id,
             ra.reject_reason,
             ra.apply_date,
             ra.processinstanceid,
             e.name as replenish_user_name,
             case
               when ra.status = '1' then '待审批'
               when ra.status = '2' then '审批通过'
               when ra.status = '3' then '驳回'
               when ra.status = '9' then '已撤销'
               end  as statusName,
             case
               when ra.type = '1' then '班车迟到'
               when ra.type = '2' then '忘记打卡'
               when ra.type = '3' then '系统数据丢失'
               end  as typeName,
             ra.type,
             ra.replenish_status,
             case
               when ra.replenish_status = 1 then '进'
               when ra.replenish_status = 2 then '出'
               end  as replenishStatusName
      from biz_replenish_attendance ra
             left join biz_employee e on e.emp_id = ra.replenish_user
      where processinstanceid = #{processInstanceId} limit 1
    </select>

    <!-- 分页查询我的补卡申请 -->
    <select id="queryReplenishPage" resultMap="BaseResultMap" parameterType="string">
        select replenish_user,replenish_time,work_type_date,reason,status,reject_reason,apply_date,processinstanceid,
        case
        when ra.status = '1' then '待审批'
        when ra.status = '2' then '审批通过'
        when ra.status = '3' then '驳回'
        when ra.status = '9' then '已撤销'
        end as statusName,
        case
        when ra.type = '1' then '班车迟到'
        when ra.type = '2' then '忘记打卡'
        when ra.type = '3' then '系统数据丢失'
        end as typeName,ra.type,
        ra.replenish_status,
        case
        when ra.replenish_status = 1 then '进'
        when ra.replenish_status = 2 then '出'
        end as replenishStatusName
        from biz_replenish_attendance ra
        where replenish_user = #{empId}
        order by apply_date desc
    </select>

    <!-- 分页查询我的补卡申请 -->
    <select id="queryReplenishPageList" resultMap="BaseResultMap">
        select replenish_user,e.`name` as replenish_user_name,replenish_time,work_type_date,reason,status,
        reject_reason,apply_date,processinstanceid,ra.finish_time,
        case
        when ra.status = '1' then '待审批'
        when ra.status = '2' then '审批通过'
        when ra.status = '3' then '驳回'
        when ra.status = '9' then '已撤销'
        end as statusName,
        case
        when ra.type = '1' then '班车迟到'
        when ra.type = '2' then '忘记打卡'
        when ra.type = '3' then '系统数据丢失'
        end as typeName,ra.type,
        ra.replenish_status,
        case
        when ra.replenish_status = 1 then '进'
        when ra.replenish_status = 2 then '出'
        end as replenishStatusName,
        ra.atta_url
        from biz_replenish_attendance ra
        left join biz_employee e on ra.replenish_user = e.emp_id
        left join biz_cost_center cc on e.cost_center = cc.cost_center
        <where>
            ra.status = '2'
            <if test="empId !=null and empId !=''">
                and ra.replenish_user like '%${empId}%'
            </if>
            <if test="name !=null and name !=''">
                and e.name like '%${name}%'
            </if>
            <if test="plant !=null and plant !=''">
                and cc.plant = #{plant}
            </if>
            <if test="workshop !=null and workshop !=''">
                and cc.workshop like '%${workshop}%'
            </if>
            <if test="vsm !=null and vsm !=''">
                and cc.vsm like '%${vsm}%'
            </if>
            <if test="finishStartDate !=null and finishStartDate !=''">
                and ra.finish_time &gt;= #{finishStartDate}
            </if>
            <if test="finishEndDate !=null and finishEndDate !=''">
                and ra.finish_time &lt;= #{finishEndDate}
            </if>
        </where>
        order by apply_date desc
    </select>

    <!-- 查询补卡申请信息 -->
    <select id="queryPendingList" resultMap="BaseResultMap" parameterType="collection">
        select ra.replenish_user,ra.replenish_time,ra.work_type_date,ra.reason,ra.status,ra.reject_reason,ra.apply_date,ra.processinstanceid,
        case
        when ra.status = '1' then '待审批'
        when ra.status = '2' then '审批通过'
        when ra.status = '3' then '驳回'
        when ra.status = '9' then '已撤销'
        end as statusName,
        replenish_status,
        e.name as replenish_user_name,
        case
        when replenish_status = 1 then '进'
        when replenish_status = 2 then '出'
        end as replenishStatusName
        from biz_replenish_attendance ra
        left join biz_employee e on ra.replenish_user = e.emp_id
        where processinstanceid in (
        <foreach collection="list" item="item" separator=",">
            #{item}
        </foreach>
        )
        order by apply_date desc
    </select>


    <select id="exportReplenishData" resultType="org.fh.model.exportModel.ReplenishExportModel">
        select replenish_user as empId,e.`name` as name,replenish_time as replenishTime,work_type_date as workTypeDate,
        reason,ra.finish_time as finishTime,
        case
        when ra.type = '1' then '班车迟到'
        when ra.type = '2' then '忘记打卡'
        when ra.type = '3' then '系统数据丢失'
        end as typeName,
        ra.replenish_status,
        case
        when ra.replenish_status = 1 then '进'
        when ra.replenish_status = 2 then '出'
        end as replenishStatusName
        from biz_replenish_attendance ra
        left join biz_employee e on ra.replenish_user = e.emp_id
        <where>
            ra.status = '2'
            <if test="empId !=null and empId !=''">
                and ra.replenish_user like '%${empId}%'
            </if>
            <if test="name !=null and name !=''">
                and e.name like '%${name}%'
            </if>
            <if test="finishStartDate !=null and finishStartDate !=''">
                and ra.finish_time &gt; #{finishStartDate}
            </if>
            <if test="finishEndDate !=null and finishEndDate !=''">
                and ra.finish_time &lt; #{finishEndDate}
            </if>
        </where>
        order by apply_date desc
    </select>

    <select id="exportReplenishDataForKayang"
    resultType="org.fh.model.exportModel.ReplenishKayangExportModel">
    select replenish_user as empId,replenish_time as replenishTime,work_type_date as workTypeDate,
    reason,
    case
    when ra.replenish_status = 1 then '进'
    when ra.replenish_status = 2 then '出'
    end as replenishStatusName
    from biz_replenish_attendance ra
    left join biz_employee e on ra.replenish_user = e.emp_id
    <where>
      ra.status = '2'
      <if test="empId !=null and empId !=''">
        and ra.replenish_user like '%${empId}%'
      </if>
      <if test="name !=null and name !=''">
        and e.name like '%${name}%'
      </if>
      <if test="finishStartDate !=null and finishStartDate !=''">
        and ra.replenish_time &gt; #{finishStartDate}
      </if>
      <if test="finishEndDate !=null and finishEndDate !=''">
        and ra.replenish_time &lt; #{finishEndDate}
      </if>
    </where>
    order by apply_date desc
    </select>
</mapper>