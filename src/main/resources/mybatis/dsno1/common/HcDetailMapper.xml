<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.fh.mapper.dsno1.common.HcDetailMapper">
    <resultMap id="BaseResultMap" type="org.fh.entity.common.HcDetailEntity">
        <id column="hc_detail_id" jdbcType="VARCHAR" property="hcDetailId"/>
        <result column="emp_id" jdbcType="VARCHAR" property="empId"/>
        <result column="finance_month" jdbcType="VARCHAR" property="financeMonth"/>
        <result column="working_hours" jdbcType="DOUBLE" property="workingHours"/>
        <result column="create_by" jdbcType="VARCHAR" property="createBy"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
    </resultMap>
    <sql id="Base_Column_List">
    hc_detail_id, emp_id, finance_month, working_hours, create_by, create_time
  </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from biz_hc_detail
        where hc_detail_id = #{hcDetailId,jdbcType=VARCHAR}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from biz_hc_detail
    where hc_detail_id = #{hcDetailId,jdbcType=VARCHAR}
  </delete>
    <insert id="insert" parameterType="org.fh.entity.common.HcDetailEntity">
    insert into biz_hc_detail (hc_detail_id, emp_id, finance_month, 
      working_hours, create_by, create_time
      )
    values (#{hcDetailId,jdbcType=VARCHAR}, #{empId,jdbcType=VARCHAR}, #{financeMonth,jdbcType=VARCHAR}, 
      #{workingHours,jdbcType=DOUBLE}, #{createBy,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}
      )
  </insert>
    <insert id="insertSelective" parameterType="org.fh.entity.common.HcDetailEntity">
        insert into biz_hc_detail
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="hcDetailId != null">
                hc_detail_id,
            </if>
            <if test="empId != null">
                emp_id,
            </if>
            <if test="financeMonth != null">
                finance_month,
            </if>
            <if test="workingHours != null">
                working_hours,
            </if>
            <if test="createBy != null">
                create_by,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="hcDetailId != null">
                #{hcDetailId,jdbcType=VARCHAR},
            </if>
            <if test="empId != null">
                #{empId,jdbcType=VARCHAR},
            </if>
            <if test="financeMonth != null">
                #{financeMonth,jdbcType=VARCHAR},
            </if>
            <if test="workingHours != null">
                #{workingHours,jdbcType=DOUBLE},
            </if>
            <if test="createBy != null">
                #{createBy,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="org.fh.entity.common.HcDetailEntity">
        update biz_hc_detail
        <set>
            <if test="empId != null">
                emp_id = #{empId,jdbcType=VARCHAR},
            </if>
            <if test="financeMonth != null">
                finance_month = #{financeMonth,jdbcType=VARCHAR},
            </if>
            <if test="workingHours != null">
                working_hours = #{workingHours,jdbcType=DOUBLE},
            </if>
            <if test="createBy != null">
                create_by = #{createBy,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where hc_detail_id = #{hcDetailId,jdbcType=VARCHAR}
    </update>
    <update id="updateByPrimaryKey" parameterType="org.fh.entity.common.HcDetailEntity">
    update biz_hc_detail
    set emp_id = #{empId,jdbcType=VARCHAR},
      finance_month = #{financeMonth,jdbcType=VARCHAR},
      working_hours = #{workingHours,jdbcType=DOUBLE},
      create_by = #{createBy,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP}
    where hc_detail_id = #{hcDetailId,jdbcType=VARCHAR}
  </update>

    <!-- 根据财务月删除人力工时报告详细数据 -->
    <delete id="deleteHCDetailByDate" parameterType="string">
        delete from biz_hc_detail where finance_month = #{financeMonth}
    </delete>

    <!-- 批量添加人力工时报告详细数据 -->
    <insert id="addHCDetailList" parameterType="collection">
        insert into biz_hc_detail (hc_detail_id, emp_id, finance_month, working_hours, create_by, create_time)
        values
        <foreach collection="list" item="item" separator=",">
            (
            #{item.hcDetailId},#{item.empId},#{item.financeMonth},#{item.workingHours},#{item.createBy},now()
            )
        </foreach>
    </insert>

    <!-- 根据财务月查询人力工时报告汇总数据 -->
    <select id="queryReportHCData" parameterType="string" resultType="org.fh.entity.common.ReportHcEntity">
        select sum(hd.working_hours)as workingHours,cc.plant,cc.workshop,cc.vsm
        from biz_hc_detail hd
        left join biz_employee e on e.emp_id = hd.emp_id
        left join biz_cost_center cc  on cc.cost_center = e.cost_center
        where cc.plant is not null
        and cc.workshop is not null
        and hd.emp_id not in (select emp_id from biz_out_range_employee)
        and hd.finance_month = #{financeMonth}
        group by cc.plant,cc.workshop
    </select>

    <!-- 根据财务月查询人力工时汇总报告线外工时 -->
    <select id="queryReportHCOtherData" parameterType="string" resultType="org.fh.entity.common.ReportHcEntity">
        select sum(hd.working_hours)as otherHours,cc.plant,cc.workshop
        from biz_hc_detail hd
        left join biz_employee e on e.emp_id = hd.emp_id
        left join biz_cost_center cc  on cc.cost_center = e.cost_center
        where cc.plant is not null
        and cc.workshop is not null
        and hd.finance_month = #{financeMonth}
        and hd.emp_id in (select emp_id from biz_out_range_employee)
        group by cc.plant,cc.workshop
    </select>
</mapper>