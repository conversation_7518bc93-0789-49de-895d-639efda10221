<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.fh.mapper.dsno1.common.ScheduleWorkTeamMapper">
    <resultMap id="BaseResultMap" type="org.fh.vo.common.ScheduleWorkTeamVo">
        <id column="schedule_work_team_id" jdbcType="VARCHAR" property="scheduleWorkTeamId"/>
        <result column="schedule_date" jdbcType="VARCHAR" property="scheduleDate"/>
        <result column="work_team" jdbcType="VARCHAR" property="workTeam"/>
        <result column="workshop" jdbcType="VARCHAR" property="workshop"/>
        <result column="plant" jdbcType="VARCHAR" property="plant"/>
        <result column="work_type" jdbcType="VARCHAR" property="workType"/>
        <result column="create_by" jdbcType="VARCHAR" property="createBy"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_by" jdbcType="VARCHAR" property="updateBy"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>
    <sql id="Base_Column_List">
    schedule_work_team_id, schedule_date, work_team, plant, work_type, create_by, create_time, 
    update_by, update_time
  </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from biz_schedule_work_team
        where schedule_work_team_id = #{scheduleWorkTeamId,jdbcType=VARCHAR}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from biz_schedule_work_team
    where schedule_work_team_id = #{scheduleWorkTeamId,jdbcType=VARCHAR}
  </delete>
    <insert id="insert" parameterType="org.fh.entity.common.ScheduleWorkTeamEntity">
    insert into biz_schedule_work_team (schedule_work_team_id, schedule_date, 
      work_team, plant, work_type, 
      create_by, create_time, update_by, 
      update_time)
    values (#{scheduleWorkTeamId,jdbcType=VARCHAR}, #{scheduleDate,jdbcType=VARCHAR}, 
      #{workTeam,jdbcType=VARCHAR}, #{plant,jdbcType=VARCHAR}, #{workType,jdbcType=VARCHAR}, 
      #{createBy,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{updateBy,jdbcType=VARCHAR}, 
      #{updateTime,jdbcType=TIMESTAMP})
  </insert>
    <insert id="insertSelective" parameterType="org.fh.entity.common.ScheduleWorkTeamEntity">
        insert into biz_schedule_work_team
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="scheduleWorkTeamId != null">
                schedule_work_team_id,
            </if>
            <if test="scheduleDate != null">
                schedule_date,
            </if>
            <if test="workTeam != null">
                work_team,
            </if>
            <if test="plant != null">
                plant,
            </if>
            <if test="workType != null">
                work_type,
            </if>
            <if test="createBy != null">
                create_by,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateBy != null">
                update_by,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="scheduleWorkTeamId != null">
                #{scheduleWorkTeamId,jdbcType=VARCHAR},
            </if>
            <if test="scheduleDate != null">
                #{scheduleDate,jdbcType=VARCHAR},
            </if>
            <if test="workTeam != null">
                #{workTeam,jdbcType=VARCHAR},
            </if>
            <if test="plant != null">
                #{plant,jdbcType=VARCHAR},
            </if>
            <if test="workType != null">
                #{workType,jdbcType=VARCHAR},
            </if>
            <if test="createBy != null">
                #{createBy,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateBy != null">
                #{updateBy,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="org.fh.entity.common.ScheduleWorkTeamEntity">
        update biz_schedule_work_team
        <set>
            <if test="scheduleDate != null">
                schedule_date = #{scheduleDate,jdbcType=VARCHAR},
            </if>
            <if test="workTeam != null">
                work_team = #{workTeam,jdbcType=VARCHAR},
            </if>
            <if test="plant != null">
                plant = #{plant,jdbcType=VARCHAR},
            </if>
            <if test="workType != null">
                work_type = #{workType,jdbcType=VARCHAR},
            </if>
            <if test="createBy != null">
                create_by = #{createBy,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateBy != null">
                update_by = #{updateBy,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where schedule_work_team_id = #{scheduleWorkTeamId,jdbcType=VARCHAR}
    </update>
    <update id="updateByPrimaryKey" parameterType="org.fh.entity.common.ScheduleWorkTeamEntity">
        update biz_schedule_work_team
        set schedule_date = #{scheduleDate,jdbcType=VARCHAR},
          work_team = #{workTeam,jdbcType=VARCHAR},
          plant = #{plant,jdbcType=VARCHAR},
          work_type = #{workType,jdbcType=VARCHAR},
          create_by = #{createBy,jdbcType=VARCHAR},
          create_time = #{createTime,jdbcType=TIMESTAMP},
          update_by = #{updateBy,jdbcType=VARCHAR},
          update_time = #{updateTime,jdbcType=TIMESTAMP}
        where schedule_work_team_id = #{scheduleWorkTeamId,jdbcType=VARCHAR}
      </update>

    <select id="queryScheduleMaxHistory" resultMap="BaseResultMap">
      SELECT
        ss.work_team,
        ss.schedule_date,
        swt.work_type
      FROM
        ( SELECT work_team, MAX( schedule_date ) AS schedule_date FROM biz_schedule_work_team sw WHERE sw.schedule_date > #{maxDate} GROUP BY work_team ) ss
          LEFT JOIN biz_schedule_work_team swt ON swt.work_team = ss.work_team
          AND ss.schedule_date = swt.schedule_date
    </select>

    <!-- 查询最近周期内的排班（最后一周期） -->
    <select id="queryScheduleHistoryList" resultMap="BaseResultMap">
        select  schedule_work_team_id, schedule_date, work_team, plant, work_type
        from biz_schedule_work_team
        <where>
            <if test="workTeam !=null and workTeam !=''">
                and work_team = #{workTeam}
            </if>
            <if test="plant !=null and plant !=''">
                and plant = #{plant}
            </if>
            <if test="minDate !=null and minDate !=''">
                and schedule_date &lt;= #{minDate}
            </if>
        </where>
        order by schedule_date sesc
    </select>

    <!-- 批量添加班组排班 -->
    <insert id="addScheduleWorkTeamList" parameterType="collection">
        insert into biz_schedule_work_team
        (schedule_work_team_id,schedule_date,work_team,plant,workshop,work_type,create_by,create_time)
        values
        <foreach collection="list" separator="," item="item">
            (
              #{item.scheduleWorkTeamId},#{item.scheduleDate},#{item.workTeam},
              #{item.plant},#{item.workshop},#{item.workType},#{item.createBy},now()
            )
        </foreach>
    </insert>

    <!-- 批量修改班组排班 -->
    <update id="updateScheduleWorkTeamList" parameterType="collection">
        <foreach collection="list" item="item" separator=";">
            update biz_schedule_work_team set work_type = #{item.workType},update_time = now(),update_by = #{item.updateBy}
            where schedule_date = #{item.scheduleDate} and work_team = #{item.workTeam}
        </foreach>
    </update>

    <!-- 分页查询排班管理 -->
    <select id="queryPlanSchedulePage" resultMap="BaseResultMap" parameterType="org.fh.entity.common.ScheduleWorkTeamEntity">
        SELECT schedule_work_team_id,work_team,plant,workshop
        FROM
        biz_schedule_work_team
        <where>
            <if test="workTeam !=null and workTeam !=''">
                and work_team like '%${workTeam}%'
            </if>
            <if test="plant !=null and plant !=''">
                and plant = #{plant}
            </if>
            <if test="workshop !=null and workshop !=''">
                and workshop = #{workshop}
            </if>
        </where>
        group by work_team
    </select>

    <select id="queryPlanScheduleDateList" parameterType="org.fh.entity.common.ScheduleWorkTeamEntity" resultType="map">
        select schedule_date,work_type,work_team from biz_schedule_work_team
        <where>
            <if test="workTeam !=null and workTeam !=''">
                and work_team  like '%${workTeam}%'
            </if>
            <if test="scheduleDate !=null and scheduleDate !=''">
                and schedule_date like '%${scheduleDate}%'
            </if>
            <if test="startDate !=null and startDate !=''">
                and schedule_date &gt;= #{startDate}
            </if>
            <if test="endDate !=null and endDate !=''">
                and schedule_date &lt;= #{endDate}
            </if>
        </where>
        order by schedule_date asc
    </select>

    <!-- 查询班组对应日期的排班情况 -->
    <select id="queryPlanScheduleWorkTypeList" parameterType="org.fh.entity.common.ScheduleWorkTeamEntity" resultType="string">
        select work_type from biz_schedule_work_team
        <where>
            <if test="workTeam !=null and workTeam !=''">
                and work_team = #{workTeam}
            </if>
            <if test="scheduleDate !=null and scheduleDate !=''">
                and schedule_date like '%${scheduleDate}%'
            </if>
            <if test="startDate !=null and startDate !=''">
                and schedule_date &gt;= #{startDate}
            </if>
            <if test="endDate !=null and endDate !=''">
                and schedule_date &lt;= #{endDate}
            </if>
        </where>
        order by schedule_date asc
    </select>
    <select id="queryPlanScheduleDetail" resultMap="BaseResultMap" parameterType="org.fh.entity.common.ScheduleWorkTeamEntity">
        select schedule_date,work_type from biz_schedule_work_team
        <where>
            <if test="plant !=null and plant !=''">
                and plant = #{plant}
            </if>
            <if test="workTeam !=null and workTeam !=''">
                and work_team = #{workTeam}
            </if>
            <if test="workshop !=null and workshop !=''">
                and workshop = #{workshop}
            </if>
            <if test="scheduleDate !=null and scheduleDate !=''">
                and schedule_date = #{scheduleDate}
            </if>
        </where>
        limit 1
    </select>

    <update id="updateWorkTeamSchedule" parameterType="org.fh.entity.common.ScheduleWorkTeamEntity">
        update biz_schedule_work_team set work_type = #{workType}
        where work_team = #{workTeam}
        and schedule_date = #{scheduleDate}
    </update>

    <!-- 查询考勤日历排班情况 -->
    <select id="queryScheduleByEmployee" resultType="map">
        select schedule_date,work_type as workType from biz_schedule_work_team
        where
        work_team = #{workTeam}
        <if test="plant !=null and plant !=''">
            and plant = #{plant}
        </if>
        <if test="workshop !=null and workshop !=''">
            and workshop = #{workshop}
        </if>
        and schedule_date like '%${scheduleDate}%'
        order by schedule_date asc
    </select>

    <!-- 根据指定日期集合查询对应的排班信息 -->
    <select id="queryScheduleByDateList" parameterType="collection" resultMap="BaseResultMap">
        select work_team,workshop,plant, work_type,schedule_date
        from biz_schedule_work_team
        where schedule_date in
        (
        <foreach collection="list" separator="," item="item">
            #{item}
        </foreach>
        )
    </select>

    <select id="queryMonthCount" resultType="integer" parameterType="string">
        select count(*) from biz_schedule_work_team where create_time like '%${yearMonth}%'
        <if test="plant !=null and plant !=''">
            and plant = #{plant}
        </if>
        <if test="workshop !=null and workshop !=''">
            and workshop = #{workshop}
        </if>
    </select>

  <select id="queryFinancialMonthCount" resultType="integer" parameterType="java.util.Date">
    select count(*) from biz_schedule_work_team where create_time &gt;= #{startDate}
  </select>

    <select id="queryWorkTypeByScheduleDate" resultType="string" parameterType="org.fh.entity.common.ScheduleWorkTeamEntity">
      SELECT work_type FROM biz_schedule_work_team sw
      WHERE sw.schedule_date = #{scheduleDate}
      and plant = #{plant}
      and workshop = #{workshop}
      and work_team = #{workTeam}
      limit 1
    </select>

    <!-- 根据日期查询班组排班 -->
    <select id="queryWorkTeamScheduleByDate" resultMap="BaseResultMap">
        select work_team,workshop,plant,work_type,schedule_date
        from biz_schedule_work_team
        where schedule_date = #{scheduleDate}
    </select>

</mapper>