<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.fh.mapper.dsno1.common.MonthlyAttendanceAnalysisStatusMapper">

  <!--表名 -->
  <sql id="tableName">
    BIZ_MONTHLY_ATTENDANCE_ANALYSIS_STATUS
  </sql>

  <!--数据字典表名 -->
  <sql id="dicTableName">
    SYS_DICTIONARIES
  </sql>

  <!-- 字段 -->
  <sql id="Field">
    f
    .
    MONTHLY_ATTENDANCE_ANALYSIS_STATUS_ID
    ,
		f.TERM,	
		f.CREATE_TIME,	
		f.CONFIRM_STATUS
  </sql>

  <!-- 字段用于新增 -->
  <sql id="Field2">
    MONTHLY_ATTENDANCE_ANALYSIS_STATUS_ID
    ,
		TERM,	
		CREATE_TIME,	
		CONFIRM_STATUS
  </sql>

  <!-- 字段值 -->
  <sql id="FieldValue">
    #{MONTHLY_ATTENDANCE_ANALYSIS_STATUS_ID}
    ,
    #{TERM},
    #{CREATE_TIME},
    #{CONFIRM_STATUS}
  </sql>

  <!-- 新增-->
  <insert id="save" parameterType="pd">
    insert into
    <include refid="tableName"></include>
    (
    <include refid="Field2"></include>
    ) values (
    <include refid="FieldValue"></include>
    )
  </insert>

  <!-- 删除-->
  <delete id="delete" parameterType="pd">
    delete from
    <include refid="tableName"></include>
    where
    MONTHLY_ATTENDANCE_ANALYSIS_STATUS_ID = #{MONTHLY_ATTENDANCE_ANALYSIS_STATUS_ID}
  </delete>

  <!-- 修改 -->
  <update id="edit" parameterType="pd">
    update
    <include refid="tableName"></include>
    set
    MONTHLY_ATTENDANCE_ANALYSIS_STATUS_ID = #{MONTHLY_ATTENDANCE_ANALYSIS_STATUS_ID},
    TERM = #{TERM},
    CREATE_TIME = #{CREATE_TIME},
    CONFIRM_STATUS = #{CONFIRM_STATUS}
    where
    MONTHLY_ATTENDANCE_ANALYSIS_STATUS_ID = #{MONTHLY_ATTENDANCE_ANALYSIS_STATUS_ID}
  </update>

  <!-- 通过ID获取数据 -->
  <select id="findById" parameterType="pd" resultType="pd">
    select
    <include refid="Field"></include>
    from
    <include refid="tableName"></include>
    f
    where
    f.MONTHLY_ATTENDANCE_ANALYSIS_STATUS_ID = #{MONTHLY_ATTENDANCE_ANALYSIS_STATUS_ID}
  </select>

  <!-- 列表 -->
  <select id="datalistPage" parameterType="page" resultType="pd">
    select
    <include refid="Field"></include>
    from
    <include refid="tableName"></include>
    f
    where 1=1
    <if test="pd.KEYWORDS != null and pd.KEYWORDS != ''"><!-- 关键词检索 -->
      and
      (
      <!--	根据需求自己加检索条件
        字段1 LIKE CONCAT(CONCAT('%', #{pd.KEYWORDS}),'%')
         or
        字段2 LIKE CONCAT(CONCAT('%', #{pd.KEYWORDS}),'%')
      -->
      )
    </if>
  </select>

  <!-- 列表(全部) -->
  <select id="listAll" parameterType="pd" resultType="pd">
    select
    <include refid="Field"></include>
    from
    <include refid="tableName"></include>
    f
  </select>

  <!-- 批量删除 -->
  <delete id="deleteAll" parameterType="String">
    delete from
    <include refid="tableName"></include>
    where
    MONTHLY_ATTENDANCE_ANALYSIS_STATUS_ID in
    <foreach item="item" index="index" collection="array" open="(" separator="," close=")">
      #{item}
    </foreach>
  </delete>

  <select id="queryPage"
    parameterType="org.fh.dto.common.attendanceAnalysis.MonthlyAttendanceAnalysisStatusQueryDto"
    resultType="org.fh.entity.PageData">
    SELECT b1.term,
           DATE_FORMAT(b1.create_time, '%Y-%m-%d %H:%i:%s') as create_time,
           b1.confirm_status,
           SUM(b2.confirm_status = 0)                       as unconfirm_count,
           SUM(b2.confirm_status = 1)                       as confirm_count
    FROM biz_monthly_attendance_analysis_status as b1
    LEFT JOIN biz_monthly_attendance_analysis_detail as b2 ON b1.term = b2.term
    <if test="empId !=null and empId !=''">
      LEFT JOIN biz_employee b3 on b2.emp_id=b3.emp_id
    </if>
    WHERE b1.term = #{term}
    <if test="empId !=null and empId !=''">
      AND b3.human_resource = #{empId}
    </if>
    GROUP BY b1.term,
             b1.create_time,
             b1.confirm_status
  </select>

  <update id="initiateAttendanceConfirmation"
    parameterType="org.fh.dto.common.attendanceAnalysis.MonthlyAttendanceAnalysisStatusQueryDto">
    update
    <include refid="tableName"></include>
    set
    CONFIRM_STATUS = #{status}
    where
    TERM = #{term}
  </update>

  <select id="countByTerm" parameterType="pd" resultType="long">
    select count(*) from <include refid="tableName"></include> where TERM = #{term}
  </select>

  <select id="getConfirmStatusByTerm" parameterType="pd" resultType="int">
    select confirm_status from <include refid="tableName"></include> where TERM = #{term} limit 1
  </select>

  <update id="updateCreateTimeByTerm"
    parameterType="org.fh.dto.common.attendanceAnalysis.MonthlyAttendanceAnalysisStatusQueryDto">
    update
    <include refid="tableName"></include>
    set
    create_time = now()
    where
    TERM = #{term}
  </update>


</mapper>