<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.fh.mapper.dsno1.common.LeaveMapper">
    <resultMap id="BaseResultMap" type="org.fh.vo.common.LeaveVo">
        <id column="leave_id" jdbcType="VARCHAR" property="leaveId"/>
        <result column="type" jdbcType="VARCHAR" property="type"/>
        <result column="leave_type_name" jdbcType="VARCHAR" property="leaveTypeName"/>
        <result column="start_time" jdbcType="TIMESTAMP" property="startTime"/>
        <result column="end_time" jdbcType="TIMESTAMP" property="endTime"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="department" jdbcType="VARCHAR" property="department"/>
        <result column="plant" jdbcType="VARCHAR" property="plant"/>
        <result column="workshop" jdbcType="VARCHAR" property="workshop"/>
        <result column="vsm" jdbcType="VARCHAR" property="vsm"/>
        <result column="applyUserName" jdbcType="VARCHAR" property="applyUserName"/>
        <result column="productionLine" jdbcType="VARCHAR" property="productionLine"/>
        <result column="work_team" jdbcType="VARCHAR" property="workTeam"/>
        <result column="work_type" jdbcType="VARCHAR" property="workType"/>
        <result column="reason" jdbcType="VARCHAR" property="reason"/>
        <result column="status" jdbcType="INTEGER" property="status"/>
        <result column="explains" jdbcType="INTEGER" property="explains"/>
        <result column="empName" jdbcType="VARCHAR" property="applyUserName"/>
        <result column="statusName" jdbcType="INTEGER" property="statusName"/>
        <result column="emp_id" jdbcType="VARCHAR" property="empId"/>
        <result column="apply_date" jdbcType="TIMESTAMP" property="applyDate"/>
        <result column="processinstanceid" jdbcType="VARCHAR" property="processinstanceid"/>
        <result column="img_url" jdbcType="VARCHAR" property="imgUrl"/>
        <result column="leave_count" jdbcType="DECIMAL" property="leaveCount"/>
        <result column="finish_time" jdbcType="VARCHAR" property="finishTime"/>
        <result column="open_id" jdbcType="VARCHAR" property="openId"/>
    </resultMap>
    <sql id="Base_Column_List">
    leave_id, type, start_time, end_time, remark, department, reason, status, emp_id, 
    apply_date, processinstanceid, img_url, leave_count
  </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from biz_leave
        where leave_id = #{leaveId,jdbcType=VARCHAR}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from biz_leave
    where leave_id = #{leaveId,jdbcType=VARCHAR}
  </delete>
    <insert id="insert" parameterType="org.fh.entity.common.LeaveEntity">
    insert into biz_leave (leave_id, type, start_time, 
      end_time, remark, department, 
      reason, status, emp_id, 
      apply_date, processinstanceid, img_url,
      leave_count)
    values (#{leaveId,jdbcType=VARCHAR}, #{type,jdbcType=VARCHAR}, #{startTime,jdbcType=TIMESTAMP}, 
      #{endTime,jdbcType=TIMESTAMP}, #{remark,jdbcType=VARCHAR}, #{department,jdbcType=VARCHAR}, 
      #{reason,jdbcType=VARCHAR}, #{status,jdbcType=INTEGER}, #{empId,jdbcType=VARCHAR}, 
      #{applyDate,jdbcType=TIMESTAMP}, #{processinstanceid,jdbcType=VARCHAR}, #{imgUrl,jdbcType=VARCHAR},
      #{leaveCount,jdbcType=DECIMAL})
  </insert>
    <insert id="insertSelective" parameterType="org.fh.entity.common.LeaveEntity">
        insert into biz_leave
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="leaveId != null">
                leave_id,
            </if>
            <if test="type != null">
                type,
            </if>
            <if test="startTime != null">
                start_time,
            </if>
            <if test="endTime != null">
                end_time,
            </if>
            <if test="remark != null">
                remark,
            </if>
            <if test="department != null">
                department,
            </if>
            <if test="reason != null">
                reason,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="empId != null">
                emp_id,
            </if>
            <if test="applyDate != null">
                apply_date,
            </if>
            <if test="processinstanceid != null">
                processinstanceid,
            </if>
            <if test="imgUrl != null">
                img_url,
            </if>
            <if test="leaveCount != null">
                leave_count,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="leaveId != null">
                #{leaveId,jdbcType=VARCHAR},
            </if>
            <if test="type != null">
                #{type,jdbcType=VARCHAR},
            </if>
            <if test="startTime != null">
                #{startTime,jdbcType=TIMESTAMP},
            </if>
            <if test="endTime != null">
                #{endTime,jdbcType=TIMESTAMP},
            </if>
            <if test="remark != null">
                #{remark,jdbcType=VARCHAR},
            </if>
            <if test="department != null">
                #{department,jdbcType=VARCHAR},
            </if>
            <if test="reason != null">
                #{reason,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                #{status,jdbcType=INTEGER},
            </if>
            <if test="empId != null">
                #{empId,jdbcType=VARCHAR},
            </if>
            <if test="applyDate != null">
                #{applyDate,jdbcType=TIMESTAMP},
            </if>
            <if test="processinstanceid != null">
                #{processinstanceid,jdbcType=VARCHAR},
            </if>
            <if test="imgUrl != null">
                #{imgUrl,jdbcType=VARCHAR},
            </if>
            <if test="leaveCount != null">
                #{leaveCount,jdbcType=DECIMAL},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="org.fh.entity.common.LeaveEntity">
        update biz_leave
        <set>
            <if test="type != null">
                type = #{type,jdbcType=VARCHAR},
            </if>
            <if test="startTime != null">
                start_time = #{startTime,jdbcType=TIMESTAMP},
            </if>
            <if test="endTime != null">
                end_time = #{endTime,jdbcType=TIMESTAMP},
            </if>
            <if test="remark != null">
                remark = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="department != null">
                department = #{department,jdbcType=VARCHAR},
            </if>
            <if test="reason != null">
                reason = #{reason,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                status = #{status,jdbcType=INTEGER},
            </if>
            <if test="empId != null">
                emp_id = #{empId,jdbcType=VARCHAR},
            </if>
            <if test="applyDate != null">
                apply_date = #{applyDate,jdbcType=TIMESTAMP},
            </if>
            <if test="processinstanceid != null">
                processinstanceid = #{processinstanceid,jdbcType=VARCHAR},
            </if>
            <if test="imgUrl != null">
                img_url = #{imgUrl,jdbcType=VARCHAR},
            </if>
            <if test="leaveCount != null">
                leave_count = #{leaveCount,jdbcType=DECIMAL},
            </if>
        </set>
        where leave_id = #{leaveId,jdbcType=VARCHAR}
    </update>
    <update id="updateByPrimaryKey" parameterType="org.fh.entity.common.LeaveEntity">
    update biz_leave
    set type = #{type,jdbcType=VARCHAR},
      start_time = #{startTime,jdbcType=TIMESTAMP},
      end_time = #{endTime,jdbcType=TIMESTAMP},
      remark = #{remark,jdbcType=VARCHAR},
      department = #{department,jdbcType=VARCHAR},
      reason = #{reason,jdbcType=VARCHAR},
      status = #{status,jdbcType=INTEGER},
      emp_id = #{empId,jdbcType=VARCHAR},
      apply_date = #{applyDate,jdbcType=TIMESTAMP},
      processinstanceid = #{processinstanceid,jdbcType=VARCHAR},
      img_url = #{imgUrl,jdbcType=VARCHAR},
      leave_count = #{leaveCount,jdbcType=DECIMAL}
    where leave_id = #{leaveId,jdbcType=VARCHAR}
  </update>

    <!-- WEB查询请假申请记录 -->
    <select id="queryLeaveInternetPage" parameterType="org.fh.entity.common.LeaveEntity" resultMap="BaseResultMap">
        select l.leave_id,l.type,lt.leave_type_name,l.start_time,l.end_time,l.remark,l.department,l.reason,l.status,l.emp_id,
        l.apply_date,l.processinstanceid,l.img_url,l.leave_count,cc.plant,cc.workshop,cc.vsm,e.name as applyUserName,
        e.production_line as productionLine,e.work_team,l.work_type,l.finish_time,
        case
        when l.status = '1' then '待审批'
        when l.status = '2' then '审批中'
        when l.status = '3' then '审批完成'
        when l.status = '4' then '已取消'
        when l.status = '9' then '已撤销'
        else '被驳回'
        end as statusName
        from biz_leave l
        left join biz_leave_type lt on lt.leave_type_id = l.type
        left join biz_employee e on e.emp_id = l.emp_id
        left join biz_cost_center cc on e.cost_center = cc.cost_center
        <where>
            l.status = '3'
            <if test="plant !=null and plant !=''">
                and cc.plant = #{plant}
            </if>
            <if test="name !=null and name !=''">
                and e.name like '%${name}%'
            </if>
            <if test="empId !=null and empId !=''">
                and e.emp_id like '%${empId}%'
            </if>
            <if test="workshop !=null and workshop !=''">
                and cc.workshop like '%${workshop}%'
            </if>
            <if test="vsm !=null and vsm !=''">
                and cc.vsm like '%${vsm}%'
            </if>
            <if test="productionLine !=null and productionLine !=''">
                and e.production_line like '%${productionLine}%'
            </if>
            <if test="department !=null and department !=''">
                and cc.department like '%${department}%'
            </if>
            <if test="workTeam !=null and workTeam !=''">
                and e.work_team like '%${workTeam}%'
            </if>
            <if test="workType !=null and workType !=''">
                and l.work_type like '%${workType}%'
            </if>
            <if test="startDate !=null and startDate !=''">
                and l.apply_date &gt;= #{startDate}
            </if>
            <if test="endDate !=null and endDate !=''">
                and l.apply_date &lt;= #{endDate}
            </if>
            <if test="finishStartDate !=null and finishStartDate !=''">
                and l.finish_time &gt;= #{finishStartDate}
            </if>
            <if test="finishEndDate !=null and finishEndDate !=''">
                and l.finish_time &lt;= #{finishEndDate}
            </if>
        </where>
        order by l.apply_date desc
    </select>
    <!-- 根据条件查询可以导出的请假申请记录 -->
    <select id="exportLeaveData" parameterType="org.fh.entity.common.LeaveEntity" resultType="org.fh.model.exportModel.LeaveExportModel">
        select  e.name,l.emp_id as empId,cc.plant,cc.workshop,e.production_line as productionLine,e.work_team as workTeam,cc.department,l.leave_count as leaveCount,
        l.apply_date as applyDate,lt.leave_type_name as leaveTypeName,l.reason,l.start_time as startTime,l.end_time as endTime,l.finish_time as finishTime
        from biz_leave l
        left join biz_leave_type lt on lt.leave_type_id = l.type
        left join biz_employee e on e.emp_id = l.emp_id
        left join biz_cost_center cc on e.cost_center = cc.cost_center
        <where>
            l.status = '3'
            <if test="name != null and name !=''">
                and e.name like '%${name}%'
            </if>
            <if test="empId != null and empId !=''">
                and l.emp_id = #{empId}
            </if>
            <if test="plant != null and plant !=''">
                and cc.plant  = #{plant}
            </if>
            <if test="workshop != null and workshop !=''">
                and cc.workshop like '%${workshop}%'
            </if>
            <if test="productionLine != null and productionLine !=''">
                and e.production_line like '%${productionLine}%'
            </if>
            <if test="department != null and department !=''">
                and cc.department like '%${department}%'
            </if>
            <if test="workTeam != null and workTeam !=''">
                and e.work_team like '%${workTeam}%'
            </if>
            <if test="leaveTypeName != null and leaveTypeName !=''">
                and lt.leave_type_name like '%${leaveTypeName}%'
            </if>
            <if test="reason != null and reason">
                and l.reason like '%${reason}%'
            </if>
            <if test="startTime != null and startTime !=''">
                and l.start_time = #{startTime}
            </if>
            <if test="endTime != null and endTime !=''">
                and l.end_time = #{endTime}
            </if>
            <if test="startDate != null and startDate !=''">
                and l.apply_date &gt;= #{startDate}
            </if>
            <if test="endDate != null and endDate !=''">
                and l.apply_date &lt;= #{endDate}
            </if>
            <if test="finishStartDate !=null and finishStartDate !=''">
                and l.finish_time &gt;= #{finishStartDate}
            </if>
            <if test="finishEndDate !=null and finishEndDate !=''">
                and l.finish_time &lt;= #{finishEndDate}
            </if>
        </where>

    </select>

    <select id="exportKayangLeaveData" parameterType="org.fh.entity.common.LeaveEntity" resultType="org.fh.model.exportModel.LeaveKayangExportModel">
        SELECT
        l.processinstanceid AS pid,
        l.emp_id AS empId,
        '集中' AS applyType,
        l.leave_count AS leaveCount,
        l.apply_date AS applyDate,
        lt.leave_type_name AS leaveTypeName,
        l.reason,
        l.reason as reason2,
        l.start_time AS startTime,
        l.end_time AS endTime,
        CASE
        l.`status`
        WHEN '3' THEN
        0 ELSE 1
        END AS action
        FROM
        biz_leave l
        LEFT JOIN biz_leave_type lt ON lt.leave_type_id = l.type
        <where>
            (l.STATUS = '3'
            OR l.`status` = '9')
            <if test="finishStartDate !=null and finishStartDate !=''">
                and (l.start_time &gt;= #{finishStartDate} or l.finish_time &gt;= #{finishStartDate})
            </if>
        </where>
    </select>

    <insert id="addLeave" parameterType="org.fh.entity.common.LeaveEntity">
        insert into biz_leave
        <trim prefix="(" suffix=")" suffixOverrides=",">
            leave_id,
            <if test="type != null and type !=''">
                type,
            </if>
            <if test="startTime != null and startTime !=''">
                start_time,
            </if>
            <if test="endTime != null and endTime !=''">
                end_time,
            </if>
            <if test="remark != null and remark !=''">
                remark,
            </if>
            <if test="department != null and department !=''">
                department,
            </if>
            <if test="reason != null and reason !=''">
                reason,
            </if>
            status,
            <if test="empId != null and empId !=''">
                emp_id,
            </if>
            <if test="workType != null and workType !=''">
                work_type,
            </if>
            apply_date,
            <if test="processinstanceid != null and processinstanceid !=''">
                processinstanceid,
            </if>
            <if test="imgUrl != null and imgUrl !=''">
                img_url,
            </if>
            <if test="leaveCount != null">
                leave_count,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            #{leaveId},
            <if test="type != null and type !=''">
                #{type},
            </if>
            <if test="startTime != null and startTime !=''">
                #{startTime},
            </if>
            <if test="endTime != null and endTime !=''">
                #{endTime},
            </if>
            <if test="remark != null and remark !=''">
                #{remark,jdbcType=VARCHAR},
            </if>
            <if test="department != null and department !=''">
                #{department},
            </if>
            <if test="reason != null and reason !=''">
                #{reason},
            </if>
            1,
            <if test="empId != null and empId !=''">
                #{empId},
            </if>
            <if test="workType != null and workType !=''">
                #{workType},
            </if>
            now(),
            <if test="processinstanceid != null and processinstanceid !=''">
                #{processinstanceid},
            </if>
            <if test="imgUrl != null and imgUrl !=''">
                #{imgUrl},
            </if>
            <if test="leaveCount != null">
                #{leaveCount},
            </if>
        </trim>
    </insert>

    <!-- 根据请假类型查询流程定义key -->
    <select id="queryProcessKeyByLeaveType" resultType="string">
        select work_flow_key from biz_leave_type where leave_type_id = #{leaveTypeId} limit 1
    </select>

    <!-- 查询请假详细信息 -->
    <select id="queryLeaveDetail" parameterType="org.fh.entity.common.LeaveEntity" resultMap="BaseResultMap">
        select l.leave_id,l.type,lt.leave_type_name,l.start_time,l.end_time,l.remark,l.department,l.reason,l.status,l.emp_id,lt.explains,
        l.apply_date,l.processinstanceid,l.img_url,l.leave_count,e.name as empName,
        case
        when l.status = '1' then '待审批'
        when l.status = '2' then '审批中'
        when l.status = '3' then '审批完成'
        when l.status = '4' then '已取消'
        when l.status = '9' then '已撤销'
        else '被驳回'
        end as statusName,
        e.open_id
        from biz_leave l
        left join biz_leave_type lt on lt.leave_type_id = l.type
        left join biz_employee e on l.emp_id = e.emp_id
        where processinstanceid = #{processinstanceid} limit 1
    </select>

    <!-- 查询员工请假累计时间 -->
    <select id="queryLeaveCountHours" parameterType="org.fh.entity.common.LeaveEntity" resultType="double">
        select sum(leave_count) from biz_leave where start_time like '%${startTime}%' and emp_id = #{empId} and status != '4' and status != '5'
    </select>

    <!-- 修改请假记录 -->
    <update id="updateLeave" parameterType="org.fh.entity.common.LeaveEntity">
        update biz_leave
        <set>
            <if test="remark != null">
                remark = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                status = #{status},
            </if>
            <if test="status != null and status == 3">
                finish_time = now(),
            </if>
        </set>
        where processinstanceid = #{processinstanceid}
    </update>

    <!-- 根据流程实例Id集合查询对应的请假流程记录 -->
    <select id="queryProcessList" resultMap="BaseResultMap">
      select
      l.leave_id,l.type,lt.leave_type_name,l.start_time,l.end_time,l.remark,l.department,l.reason,l.status,l.emp_id,
      l.apply_date,l.processinstanceid,l.img_url,l.leave_count,e.`name` as applyUserName,
      case
      when l.status = '1' then '待审批'
      when l.status = '2' then '审批中'
      when l.status = '3' then '审批完成'
      when l.status = '4' then '已取消'
      when l.status = '9' then '已撤销'
      else '被驳回'
      end as statusName
      from biz_leave l
      left join biz_employee e on e.emp_id = l.emp_id
      left join biz_leave_type lt on lt.leave_type_id = l.type
      where l.processinstanceid in(
      <foreach collection="list" item="item" separator=",">
        #{item}
      </foreach>
      )
      <if test="type !=null and type !=''">
        and l.type = #{type}
      </if>
      <if test="orderType !=null and orderType == 1">
        order by l.apply_date asc
      </if>
      <if test="orderType == null">
        order by l.apply_date asc
      </if>
      <if test="orderType !=null and orderType == 2">
        order by l.leave_count desc
      </if>
    </select>


  <select id="queryProcessListDesc" resultMap="BaseResultMap">
    select
    l.leave_id,l.type,lt.leave_type_name,l.start_time,l.end_time,l.remark,l.department,l.reason,l.status,l.emp_id,
    l.apply_date,l.processinstanceid,l.img_url,l.leave_count,e.`name` as applyUserName,
    case
    when l.status = '1' then '待审批'
    when l.status = '2' then '审批中'
    when l.status = '3' then '审批完成'
    when l.status = '4' then '已取消'
    when l.status = '9' then '已撤销'
    else '被驳回'
    end as statusName
    from biz_leave l
    left join biz_employee e on e.emp_id = l.emp_id
    left join biz_leave_type lt on lt.leave_type_id = l.type
    where l.processinstanceid in(
    <foreach collection="list" item="item" separator=",">
      #{item}
    </foreach>
    )
    <if test="type !=null and type !=''">
      and l.type = #{type}
    </if>
    <if test="orderType !=null and orderType == 1">
      order by l.apply_date desc
    </if>
    <if test="orderType == null">
      order by l.apply_date desc
    </if>
    <if test="orderType !=null and orderType == 2">
      order by l.leave_count desc
    </if>
  </select>

    <select id="queryMyAccomplishedTasks" resultType="string">
      SELECT DISTINCT a.PROC_INST_ID_
      FROM act_hi_taskinst a
             INNER JOIN biz_leave b ON a.PROC_INST_ID_ = b.processinstanceid
      WHERE a.ASSIGNEE_ = #{empId}
        AND a.END_TIME_ IS NOT NULL
      ORDER BY a.START_TIME_ desc
    </select>

    <select id="queryMyAccomplishedTasksByApplicantAndLeaveType" resultType="string">
        SELECT DISTINCT a.PROC_INST_ID_
        FROM act_hi_taskinst a
                 INNER JOIN biz_leave b ON a.PROC_INST_ID_ = b.processinstanceid
        WHERE a.ASSIGNEE_ = #{empId}
          AND a.END_TIME_ IS NOT NULL
        <if test="applyUserEmpId !=null and applyUserEmpId !=''">
            AND b.emp_id = #{applyUserEmpId}
        </if>
        <if test="leaveTypeId !=null and leaveTypeId !=''">
            AND b.type = #{leaveTypeId}
        </if>
        ORDER BY a.START_TIME_ desc
    </select>

    <!-- 查询申请请假次数 -->
    <select id="queryLeaveStartCount" parameterType="string" resultType="int">
        select count(*) from biz_leave where emp_id = #{empId}
    </select>

    <select id="queryLeavePage" resultMap="BaseResultMap">
      select
      l.leave_id,l.type,lt.leave_type_name,l.start_time,l.end_time,l.remark,l.department,l.reason,l.status,l.emp_id,
      l.apply_date,l.processinstanceid,l.img_url,l.leave_count,e.`name` as applyUserName,
      case
      when l.status = '1' then '待审批'
      when l.status = '2' then '审批中'
      when l.status = '3' then '审批完成'
      when l.status = '4' then '已取消'
      when l.status = '9' then '已撤销'
      else '被驳回'
      end as statusName
      from biz_leave l
      left join biz_leave_type lt on lt.leave_type_id = l.type
      left join biz_employee e on e.emp_id = l.emp_id
      <where>
        <if test="empId !=null and empId !=''">
          and l.emp_id = #{empId}
        </if>
      </where>
      order by l.apply_date desc
    </select>

    <select id="queryLeaveTypeCount" resultType="org.fh.vo.common.LeaveTypeVo">
        select count(l.leave_id) as leaveCount,lt.leave_type_name as leaveTypeName,lt.leave_type_id as leaveTypeId
        from biz_leave l
        left join biz_leave_type lt on lt.leave_type_id = l.type
        <where>
            l.processinstanceid in
            <foreach collection="list" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </where>
        group by l.type
    </select>

    <!-- 分页查询员工请假申请记录 -->
    <select id="queryApplyLeavePage" resultMap="BaseResultMap" parameterType="string">
        select l.leave_id,l.type,lt.leave_type_name,l.start_time,l.end_time,l.remark,l.department,l.reason,l.status,l.emp_id,
        l.apply_date,l.processinstanceid,l.img_url,l.leave_count,cc.plant,cc.workshop,e.name as applyUserName,
        e.production_line as productionLine,e.work_team,
        case
        when l.status = '1' then '待审批'
        when l.status = '2' then '审批中'
        when l.status = '3' then '审批完成'
        when l.status = '4' then '已取消'
        when l.status = '9' then '已撤销'
        else '被驳回'
        end as statusName
        from biz_leave l
        left join biz_leave_type lt on lt.leave_type_id = l.type
        left join biz_employee e on e.emp_id = l.emp_id
        left join biz_cost_center cc on e.cost_center = cc.cost_center
        where l.emp_id = #{empId}
        order by l.apply_date desc
    </select>

    <!-- 查询假期余额 -->
    <select id="queryHolidayBalance" parameterType="string" resultType="org.fh.entity.PageData">
      SELECT a.available,a.month_available as monthAvailable,a.holiday_type as holidayType,
             DATE_FORMAT(a.update_time, '%Y-%m-%d %H:%i:%s') as update_time
      FROM biz_holiday_balance a
             INNER JOIN biz_leave_type b ON a.holiday_type = b.leave_type_name
      WHERE b.leave_type_id = #{type}
        AND a.emp_id = #{empId}
        AND CURDATE() BETWEEN a.valid_from AND a.valid_to limit  1
    </select>

</mapper>