<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.fh.mapper.dsno1.common.OutRangeEmployeeMapper">
    <resultMap id="BaseResultMap" type="org.fh.vo.common.OutRangeEmployeeVo">
        <id column="out_range_employee_id" jdbcType="VARCHAR" property="outRangeEmployeeId"/>
        <result column="emp_id" jdbcType="VARCHAR" property="empId"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="actived" jdbcType="VARCHAR" property="actived"/>
        <result column="reason" jdbcType="VARCHAR" property="reason"/>
        <result column="work_team" jdbcType="VARCHAR" property="workTeam"/>
        <result column="workshop" jdbcType="VARCHAR" property="workshop"/>
        <result column="plant" jdbcType="VARCHAR" property="plant"/>
        <result column="vsm" jdbcType="VARCHAR" property="vsm"/>
        <result column="department" jdbcType="VARCHAR" property="department"/>
        <result column="created_by" jdbcType="VARCHAR" property="createdBy"/>
        <result column="created_time" jdbcType="TIMESTAMP" property="createdTime"/>
        <result column="updated_by" jdbcType="VARCHAR" property="updatedBy"/>
        <result column="updated_time" jdbcType="TIMESTAMP" property="updatedTime"/>
    </resultMap>
    <sql id="Base_Column_List">
    out_range_employee_id, emp_id,reason, deleted,
    created_by, created_time, updated_by, updated_time
  </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from biz_out_range_employee
        where out_range_employee_id = #{outRangeEmployeeId,jdbcType=VARCHAR}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from biz_out_range_employee
    where out_range_employee_id = #{outRangeEmployeeId,jdbcType=VARCHAR}
  </delete>
    <insert id="insert" parameterType="org.fh.entity.common.OutRangeEmployeeEntity">
    insert into biz_out_range_employee (out_range_employee_id, emp_id,reason,deleted
       created_by, created_time,updated_by, updated_time)
    values (#{outRangeEmployeeId,jdbcType=VARCHAR}, #{empId,jdbcType=VARCHAR}, #{deleted,jdbcType=TINYINT},
    #{reason,jdbcType=VARCHAR},  #{createdBy,jdbcType=VARCHAR}, #{createdTime,jdbcType=TIMESTAMP},
    #{updatedBy,jdbcType=VARCHAR}, #{updatedTime,jdbcType=TIMESTAMP})
  </insert>
    <insert id="insertSelective" parameterType="org.fh.entity.common.OutRangeEmployeeEntity">
        insert into biz_out_range_employee
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="outRangeEmployeeId != null and outRangeEmployeeId !=''">
                out_range_employee_id,
            </if>
            <if test="empId != null">
                emp_id,
            </if>
            <if test="reason != null">
                reason,
            </if>
            <if test="deleted != null">
                deleted,
            </if>
            <if test="createdBy != null">
                created_by,
            </if>
            <if test="createdTime != null">
                created_time,
            </if>
            <if test="updateBy != null">
                update_by,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="outRangeEmployeeId != null and outRangeEmployeeId !=''">
                #{out_range_employee_id,jdbcType=VARCHAR},
            </if>
            <if test="empId != null">
                #{empId,jdbcType=VARCHAR},
            </if>
            <if test="reason != null">
                #{reason,jdbcType=VARCHAR},
            </if>
            <if test="deleted != null">
                #{deleted,jdbcType=TINYINT},
            </if>
            <if test="createdBy != null">
                #{createdBy,jdbcType=VARCHAR},
            </if>
            <if test="createdTime != null">
                #{createdTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateBy != null">
                #{updateBy,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="org.fh.entity.common.OutRangeEmployeeEntity">
        update biz_out_range_employee
        <set>
            <if test="empId != null and empId !=''">
                emp_id = #{empId,jdbcType=VARCHAR},
            </if>
            <if test="reason != null and reason !=''">
                reason = #{reason,jdbcType=VARCHAR},
            </if>
            <if test="deleted != null">
                deleted = #{deleted,jdbcType=TINYINT},
            </if>
            <if test="createdBy != null">
                created_by = #{createdBy,jdbcType=VARCHAR},
            </if>
            <if test="createdTime != null">
                created_time = #{createdTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updatedBy != null and updatedBy !=''">
                updated_by = #{updatedBy,jdbcType=VARCHAR},
            </if>
            <if test="updatedTime != null">
                updated_time = now(),
            </if>
        </set>
        where out_range_employee_id = #{outRangeEmployeeId,jdbcType=VARCHAR}
    </update>

    <update id="updateByPrimaryKey" parameterType="org.fh.entity.common.OutRangeEmployeeEntity">
    update biz_out_range_employee
    set emp_id = #{empId,jdbcType=VARCHAR},
      reason = #{reason,jdbcType=VARCHAR},
      deleted = #{deleted,jdbcType=TINYINT},
      created_by = #{createdBy,jdbcType=VARCHAR},
      created_time = #{createdTime,jdbcType=TIMESTAMP},
      updated_by = #{updatedBy,jdbcType=VARCHAR},
      updated_time = #{updatedTime,jdbcType=TIMESTAMP}
    where out_range_employee_id = #{outRangeEmployeeId,jdbcType=VARCHAR}
  </update>

  <!-- 批量添加不计工时员工信息 -->
  <insert id="addOutRangeEmployeeList" parameterType="collection">
    insert into biz_out_range_employee(out_range_employee_id,emp_id,reason,deleted,create_by,create_time)
    values
    <foreach collection="list" separator="," item="item">
      (#{item.outRangeEmployeeId},#{item.empId},#{item.reason},0,#{item.createdBy},now())
  </foreach>
  </insert>

    <!-- 删除不计工时员工信息 -->
    <delete id="deleteOutRangeEmployeeData" parameterType="string">
        delete from biz_out_range_employee where out_range_employee_id = #{outRangeEmployeeId}
    </delete>

    <!-- 查询不计工时员工信息 -->
  <select id="queryOutRangeEmployeeDetail" parameterType="string" resultMap="BaseResultMap">
    select o.emp_id,o.reason,e.name,e.actived,e.work_team,cc.workshop,cc.plant,cc.department
        from biz_out_range_employee o
        left join biz_employee e on e.emp_id = o.emp_id
        left join biz_cost_center cc on e.cost_center = cc.cost_center
        where out_range_employee_id = #{outRangeEmployeeId}
  </select>

    <!-- 分页查询不计工时员工信息 -->
    <select id="queryOutRangeEmployeePage" resultMap="BaseResultMap" parameterType="org.fh.entity.common.OutRangeEmployeeEntity">
        select o.out_range_employee_id,o.emp_id,o.reason,e.name,e.actived,e.work_team,cc.workshop,cc.plant,cc.department
        from biz_out_range_employee o
        left join biz_employee e on e.emp_id = o.emp_id
        left join biz_cost_center cc on e.cost_center = cc.cost_center
        <where>
            <if test="empId != null and empId !=''">
                and o.emp_id like '%${empId}%'
            </if>
            <if test="name != null and name !=''">
                and e.name like '%${name}%'
            </if>
            <if test="plant !=null and plant !=''">
                and cc.plant = #{plant}
            </if>
            <if test="workshop !=null and workshop !=''">
                and cc.workshop like '%${workshop}%'
            </if>
            <if test="vsm !=null and vsm !=''">
                and cc.vsm like '%${vsm}%'
            </if>
        </where>
    </select>

    <!-- 查询所有的不计时员工的员工号 -->
    <select id="queryOutRangeEmployeeIdAll" resultType="string">
        select emp_id from biz_out_range_employee
    </select>

    <!-- 根据条件查询可以导出不计工时员工信息 -->
    <select id="exportOutRangeEmployeeData" resultType="org.fh.model.exportModel.OutRangeEmployeeExportModel"
            parameterType="org.fh.entity.common.OutRangeEmployeeEntity">
        select e.name,o.emp_id as empId,o.reason,e.work_team as workTeam,cc.workshop,cc.plant,cc.department,e.actived,a.work_type as workType
        from biz_out_range_employee o
        left join biz_employee e on e.emp_id = o.emp_id
        left join biz_attendance a on e.work_team = a.work_team
        left join biz_cost_center cc on e.cost_center = cc.cost_center
        <where>
            <if test="name != null and name !=''">
                and e.name like '%${name}%'
            </if>
            <if test="empId != null and empId !=''">
                and o.emp_id like '%${empId}%'
            </if>
            <if test="reason != null and reason !=''">
                and o.reason like '%${reason}%'
            </if>
            <if test="department != null and department !=''">
                and cc.department like '%${department}%'
            </if>
            <if test="actived != null and actived !=''">
                and e.actived like '%${actived}%'
            </if>
            <if test="workType != null and workType !=''">
                and a.work_type like '%${workType}%'
            </if>
            <if test="plant !=null and plant !=''">
                and cc.plant = #{plant}
            </if>
            <if test="workshop !=null and workshop !=''">
                and cc.workshop like '%${workshop}%'
            </if>
            <if test="vsm !=null and vsm !=''">
                and cc.vsm like '%${vsm}%'
            </if>
        </where>

    </select>

</mapper>