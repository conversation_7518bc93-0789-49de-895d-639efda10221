<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.fh.mapper.dsno1.common.DailyAttendanceAnalysisMapper">

  <!--表名 -->
  <sql id="tableName">
    BIZ_DAILY_ATTENDANCE_ANALYSIS
  </sql>

  <!--数据字典表名 -->
  <sql id="dicTableName">
    SYS_DICTIONARIES
  </sql>

  <!-- 字段 -->
  <sql id="Field">
    F.DAILY_ATTENDANCE_ANALYSIS_ID,
		F.TERM,
		F.EMP_ID,
		F.NAME,
		F.GROUP_ID,
		F.COMP_ID,
		F.DEP_ID,
		F.LINE_ID,
		F.DAY_TYPE,
		F.SHIF<PERSON>,
		F.BEGIN_TIME,
		F<PERSON>_TIM<PERSON>,
		F.IS_EXCEP<PERSON>,
		F.<PERSON>,
		F.INITIALIZED,
		F.CDER,
		F.<PERSON>,
        F.<PERSON>R,
        F.CFHR,
        F.REWK,
        F.UAHR,
        F.LIMN,
        F.EOMN,
        F.OT1,
        F.OT2,
        F.OT3,
        F.CLOT1,
        F.PERL,
        F.OCCL,
        F.OTCL,
        F.ANNL,
        F.ANST,
        F.ANWE,
        F.PSKL,
		F.BPSL,
		F.ISJL,
		F.COML,
		F.FANF,
		F.MARL,
		F.MLPL,
		F.PATL,
		F.MATL,
		F.DSFL,
		F.BRFL,
		F.CIFL,
		F.FPGL,
		F.MSCL,
		F.POFL,
        F.LADL,
        F.OTHL1,
        F.CHILDL,
        F.NURSL,
        F.TNOM,
        F.NBOS,
        F.PROCESS_FLAG,
    f.CREATE_TIME
  </sql>

  <!-- 字段用于新增 -->
  <sql id="Field2">
      DAILY_ATTENDANCE_ANALYSIS_ID,
		TERM,
		EMP_ID,
		NAME,
		GROUP_ID,
		COMP_ID,
		DEP_ID,
		LINE_ID,
		DAY_TYPE,
		SHIFT,
		BEGIN_TIME,
		END_TIME,
		IS_EXCEPTION,
		ANALY_MODE,
		INITIALIZED,
		CDER,
		PNHR,
        WKHR,
        CFHR,
        REWK,
        UAHR,
        LIMN,
        EOMN,
        OT1,
        OT2,
        OT3,
        CLOT1,
        PERL,
        OCCL,
        OTCL,
        ANNL,
        ANST,
        ANWE,
        PSKL,
		BPSL,
		ISJL,
		COML,
		FANF,
		MARL,
		MLPL,
		PATL,
		MATL,
		DSFL,
		BRFL,
		CIFL,
		FPGL,
		MSCL,
		POFL,
        LADL,
        OTHL1,
        CHILDL,
        NURSL,
        TNOM,
        NBOS,
        PROCESS_FLAG,
    CREATE_TIME
  </sql>

  <!-- 字段值 -->
  <sql id="FieldValue">
      #{DAILY_ATTENDANCE_ANALYSIS_ID},
      #{TERM},
      #{EMP_ID},
      #{NAME},
      #{GROUP_ID},
      #{COMP_ID},
      #{DEP_ID},
      #{LINE_ID},
      #{DAY_TYPE},
      #{SHIFT},
      #{BEGIN_TIME},
      #{END_TIME},
      #{IS_EXCEPTION},
      #{ANALY_MODE},
      #{INITIALIZED},
      #{CDER},
      #{PNHR},
      #{WKHR},
      #{CFHR},
      #{REWK},
      #{UAHR},
      #{LIMN},
      #{EOMN},
      #{OT1},
      #{OT2},
      #{OT3},
      #{CLOT1},
      #{PERL},
      #{OCCL},
      #{OTCL},
      #{ANNL},
      #{ANST},
      #{ANWE},
      #{PSKL},
      #{BPSL},
      #{ISJL},
      #{COML},
      #{FANF},
      #{MARL},
      #{MLPL},
      #{PATL},
      #{MATL},
      #{DSFL},
      #{BRFL},
      #{CIFL},
      #{FPGL},
      #{MSCL},
      #{POFL},
      #{LADL},
      #{OTHL1},
      #{CHILDL},
      #{NURSL},
      #{TNOM},
      #{NBOS},
      #{PROCESS_FLAG},
    now()
  </sql>

  <sql id="FieldValue2">
    #{dailyAttendanceAnalysisId},
    #{term},
    #{empId},
    #{name},
    #{groupId},
    #{compId},
    #{depId},
    #{lineId},
    #{dayType},
    #{shift},
    #{beginTime},
    #{endTime},
    #{isException},
    #{analyMode},
    #{initialized},
    #{cder},
    #{pnhr},
    #{wkhr},
    #{cfhr},
    #{rewk},
    #{uahr},
    #{limn},
    #{eomn},
    #{ot1},
    #{ot2},
    #{ot3},
    #{clot1},
    #{perl},
    #{occl},
    #{otcl},
    #{annl},
    #{anst},
    #{anwe},
    #{pskl},
    #{bpsl},
    #{isjl},
    #{coml},
    #{fanf},
    #{marl},
    #{mlpl},
    #{patl},
    #{matl},
    #{dsfl},
    #{brfl},
    #{cifl},
    #{fpgl},
    #{mscl},
    #{pofl},
    #{ladl},
    #{othl1},
    #{childl},
    #{nursl},
    #{tnom},
    #{nbos},
    #{processFlag},
    now()
  </sql>

  <!-- 新增-->
  <insert id="save" parameterType="pd">
    insert into
    <include refid="tableName"></include>
    (
    <include refid="Field2"></include>
    ) values (
    <include refid="FieldValue"></include>
    )
  </insert>

  <insert id="upload" parameterType="org.fh.vo.common.DailyAttendanceAnalysisVo">
    insert into
    <include refid="tableName"></include>
    (
    <include refid="Field2"></include>
    ) values (
    <include refid="FieldValue2"></include>
    )
  </insert>

  <!-- 删除-->
  <delete id="delete" parameterType="pd">
    delete from
    <include refid="tableName"></include>
    where
    DAILY_ATTENDANCE_ANALYSIS_ID = #{DAILY_ATTENDANCE_ANALYSIS_ID}
  </delete>

  <!-- 修改 -->
  <update id="edit" parameterType="pd">
    update
    <include refid="tableName"></include>
    set
    DAILY_ATTENDANCE_ANALYSIS_ID = #{DAILY_ATTENDANCE_ANALYSIS_ID},
    TERM = #{TERM},
    EMP_ID = #{EMP_ID},
    NAME = #{NAME},
    GROUP_ID = #{GROUP_ID},
    COMP_ID = #{COMP_ID},
    DEP_ID = #{DEP_ID},
    LINE_ID = #{LINE_ID},
    DAY_TYPE = #{DAY_TYPE},
    SHIFT = #{SHIFT},
    BEGIN_TIME = #{BEGIN_TIME},
    END_TIME = #{END_TIME},
    IS_EXCEPTION = #{IS_EXCEPTION},
    ANALY_MODE = #{ANALY_MODE},
    INITIALIZED = #{INITIALIZED},
    CDER = #{CDER},
    PNHR = #{PNHR},
    WKHR = #{WKHR},
    CFHR = #{CFHR},
    REWK = #{REWK},
    UAHR = #{UAHR},
    LIMN = #{LIMN},
    EOMN = #{EOMN},
    OT1 = #{OT1},
    OT2 = #{OT2},
    OT3 = #{OT3},
    CLOT1 = #{CLOT1},
    PERL = #{PERL},
    OCCL = #{OCCL},
    OTCL = #{OTCL},
    ANNL = #{ANNL},
    ANST = #{ANST},
    ANWE = #{ANWE},
    PSKL = #{PSKL},
    BPSL = #{BPSL},
    ISJL = #{ISJL},
    COML = #{COML},
    FANF = #{FANF},
    MARL = #{MARL},
    MLPL = #{MLPL},
    PATL = #{PATL},
    MATL = #{MATL},
    DSFL = #{DSFL},
    BRFL = #{BRFL},
    CIFL = #{CIFL},
    FPGL = #{FPGL},
    MSCL = #{MSCL},
    POFL = #{POFL},
    LADL = #{LADL},
    OTHL1 = #{OTHL1},
    CHILDL = #{CHILDL},
    NURSL = #{NURSL},
    TNOM = #{TNOM},
    NBOS = #{NBOS},
    PROCESS_FLAG = #{PROCESS_FLAG}
    where
    DAILY_ATTENDANCE_ANALYSIS_ID = #{DAILY_ATTENDANCE_ANALYSIS_ID}
  </update>

  <!-- 通过ID获取数据 -->
  <select id="findById" parameterType="pd" resultType="pd">
    select
    <include refid="Field"></include>
    from
    <include refid="tableName"></include>
    f
    where
    f.DAILY_ATTENDANCE_ANALYSIS_ID = #{DAILY_ATTENDANCE_ANALYSIS_ID}
  </select>

  <!-- 列表 -->
  <select id="datalistPage" parameterType="page" resultType="pd">
    select
    <include refid="Field"></include>,
    b.EPLANT,
    b.WORK_TEAM,
    c.VSM,
    s.emp_id AS SUPERVISOR_ID,
    s.name AS SUPERVISOR_NAME
    from
    <include refid="tableName"></include>
    f
    left join biz_pl_vsm c on f.line_id=c.pl
    left join biz_employee b on f.emp_id=b.emp_id
    left join biz_employee m on b.first_level_mgr_id = m.emp_id
    left join biz_employee s on m.first_level_mgr_id = s.emp_id
    where 1=1
    <if test="pd.EMP_ID != null and pd.EMP_ID != ''">
      and f.EMP_ID = #{pd.EMP_ID}
    </if>
    <if test="pd.NAME != null and pd.NAME != ''">
      and f.NAME = #{pd.NAME}
    </if>
    <if test="pd.TERM != null and pd.TERM != ''">
      and f.TERM = #{pd.TERM}
    </if>
    <if test="pd.eplant != null and pd.eplant != ''">
      and b.eplant = #{pd.eplant}
    </if>
    <if test="pd.WORK_TEAM != null and pd.WORK_TEAM != ''">
      and f.WORK_TEAM = #{pd.WORK_TEAM}
    </if>
    and f.IS_EXCEPTION = 1
    and f.TERM <![CDATA[>=]]> #{pd.startDate} and f.TERM <![CDATA[<=]]> #{pd.endDate}
    order by f.create_time desc
  </select>

  <!-- 列表(全部) -->
  <select id="listAll" parameterType="pd" resultType="pd">
    select
    <include refid="Field"></include>
    from
    <include refid="tableName"></include>
    f
  </select>


  <select id="queryAttendanceMonthByKayang" parameterType="org.fh.dto.common.attendance.AttendanceQueryDto" resultType="org.fh.vo.common.DailyAttendanceAnalysisVo">
    select
    term,  emp_id empId, name,  group_id groupId, comp_id compId,
    dep_id depId, line_id lineId, day_type dayType, shift, begin_time beginTime,
    end_time endTime, is_exception isException, analy_mode analyMode, initialized, cder, pnhr, wkhr,
      cfhr, rewk,uahr,limn,eomn, ot1, ot2, ot3, clot1, perl, occl, otcl, annl, anst, anwe, pskl, bpsl,
      isjl, coml, fanf, marl, mlpl, patl, matl, dsfl, brfl, cifl, fpgl, mscl, pofl, ladl, othl1, childl,
      nursl, tnom, nbos, process_flag processFlag
    from
    <include refid="tableName"></include>
    where
    emp_id = #{empId}
    and term like '%${attendantDate}%'
    order by emp_id, term
  </select>

  <select id="queryAttendanceByKayang" parameterType="org.fh.dto.common.attendance.AttendanceQueryDto" resultType="org.fh.vo.common.DailyAttendanceAnalysisVo">
    select
    term, emp_id empId, name, group_id groupId, comp_id compId,
    dep_id depId, line_id lineId, day_type dayType, shift, begin_time beginTime,
    end_time endTime, is_exception isException, analy_mode analyMode, initialized, cder, pnhr, wkhr, cfhr, rewk,uahr,limn,eomn, ot1, ot2, ot3, clot1, perl, occl, otcl, annl, anst, anwe, pskl, bpsl,
      isjl, coml, fanf, marl, mlpl, patl, matl, dsfl, brfl, cifl, fpgl, mscl, pofl, ladl, othl1, childl,
      nursl, tnom, nbos, process_flag processFlag
    from
    <include refid="tableName"></include>
    where
    emp_id = #{empId}
    and term = #{attendantDate}
  </select>

  <select id="listAbnormalAttendances" parameterType="pd" resultType="org.fh.vo.common.AbnormalAttendanceVo">
    SELECT term,
           emp_id AS empId,
           NAME,
           uahr,
           limn,
           eomn,
           cder
    FROM biz_daily_attendance_analysis
    WHERE term = #{term}
      AND (
        uahr > 0
        OR limn > 0
        OR eomn > 0
        OR cder > 0)
  </select>
  
  <select id="listAbnormalAttendancesByDateRange" parameterType="pd" resultType="org.fh.vo.common.AbnormalAttendanceVo">
    SELECT term,
           emp_id AS empId,
           NAME,
           uahr,
           limn,
           eomn,
           cder
    FROM biz_daily_attendance_analysis
    WHERE term <![CDATA[>=]]> #{startDate}
      AND term <![CDATA[<=]]> #{endDate}
      AND is_exception = 1
  </select>

  <select id="findByTermAndEmpId" parameterType="pd"
    resultType="org.fh.vo.common.DailyAttendanceAnalysisVo">
    select
    f.DAILY_ATTENDANCE_ANALYSIS_ID as dailyAttendanceAnalysisId,
    f.TERM as term,
    f.EMP_ID as empId,
    f.NAME as name,
    f.GROUP_ID as groupId,
    f.COMP_ID as compId,
    f.DEP_ID as depId,
    f.LINE_ID as lineId,
    f.DAY_TYPE as dayType,
    f.SHIFT as shift,
    f.BEGIN_TIME as beginTime,
    f.END_TIME as endTime,
    f.IS_EXCEPTION as isException,
    f.ANALY_MODE as analyMode,
    f.INITIALIZED as initialized,
    f.CDER as cder,
    f.PNHR as pnhr,
    f.WKHR as wkhr,
    f.CFHR as cfhr,
    f.REWK as rewk,
    f.UAHR as uahr,
    f.LIMN as limn,
    f.EOMN as eomn,
    f.OT1 as ot1,
    f.OT2 as ot2,
    f.OT3 as ot3,
    f.CLOT1 as clot1,
    f.PERL as perl,
    f.OCCL as occl,
    f.OTCL as otcl,
    f.ANNL as annl,
    f.ANST as anst,
    f.ANWE as anwe,
    f.PSKL as pskl,
    f.BPSL as bpsl,
    f.ISJL as isjl,
    f.COML as coml,
    f.FANF as fanf,
    f.MARL as marl,
    f.MLPL as mlpl,
    f.PATL as patl,
    f.MATL as matl,
    f.DSFL as dsfl,
    f.BRFL as brfl,
    f.CIFL as cifl,
    f.FPGL as fpgl,
    f.MSCL as mscl,
    f.POFL as pofl,
    f.LADL as ladl,
    f.OTHL1 as othl1,
    f.CHILDL as childl,
    f.NURSL as nursl,
    f.TNOM as tnom,
    f.NBOS as nbos,
    f.PROCESS_FLAG as processFlag,
    f.CREATE_TIME as createTime
    from
    <include refid="tableName"></include>
    f
    where
    f.term = #{term}
    and f.emp_id = #{empId}
    limit 1
  </select>

  <update id="update" parameterType="org.fh.vo.common.DailyAttendanceAnalysisVo">
    update
    <include refid="tableName"></include>
    set
    TERM = #{term},
    EMP_ID = #{empId},
    NAME = #{name},
    GROUP_ID = #{groupId},
    COMP_ID = #{compId},
    DEP_ID = #{depId},
    LINE_ID = #{lineId},
    DAY_TYPE = #{dayType},
    SHIFT = #{shift},
    BEGIN_TIME = #{beginTime},
    END_TIME = #{endTime},
    IS_EXCEPTION = #{isException},
    ANALY_MODE = #{analyMode},
    INITIALIZED = #{initialized},
    CDER = #{cder},
    PNHR = #{pnhr},
    WKHR = #{wkhr},
    CFHR = #{cfhr},
    REWK = #{rewk},
    UAHR = #{uahr},
    LIMN = #{limn},
    EOMN = #{eomn},
    OT1 = #{ot1},
    OT2 = #{ot2},
    OT3 = #{ot3},
    CLOT1 = #{clot1},
    PERL = #{perl},
    OCCL = #{occl},
    OTCL = #{otcl},
    ANNL = #{annl},
    ANST = #{anst},
    ANWE = #{anwe},
    PSKL = #{pskl},
    BPSL = #{bpsl},
    ISJL = #{isjl},
    COML = #{coml},
    FANF = #{fanf},
    MARL = #{marl},
    MLPL = #{mlpl},
    PATL = #{patl},
    MATL = #{matl},
    DSFL = #{dsfl},
    BRFL = #{brfl},
    CIFL = #{cifl},
    FPGL = #{fpgl},
    MSCL = #{mscl},
    POFL = #{pofl},
    LADL = #{ladl},
    OTHL1 = #{othl1},
    CHILDL = #{childl},
    NURSL = #{nursl},
    TNOM = #{tnom},
    NBOS = #{nbos},
    PROCESS_FLAG = #{processFlag},
    CREATE_TIME = now()
    where
    TERM = #{term}
    and EMP_ID = #{empId}
  </update>

  <update id="updateList" parameterType="java.util.List">
  <foreach collection="list" item="item" separator=";">
    update
    <include refid="tableName"></include>
    set
    TERM = #{item.term},
    EMP_ID = #{item.empId},
    NAME = #{item.name},
    GROUP_ID = #{item.groupId},
    COMP_ID = #{item.compId},
    DEP_ID = #{item.depId},
    LINE_ID = #{item.lineId},
    DAY_TYPE = #{item.dayType},
    SHIFT = #{item.shift},
    BEGIN_TIME = #{item.beginTime},
    END_TIME = #{item.endTime},
    IS_EXCEPTION = #{item.isException},
    ANALY_MODE = #{item.analyMode},
    INITIALIZED = #{item.initialized},
    CDER = #{item.cder},
    PNHR = #{item.pnhr},
    WKHR = #{item.wkhr},
    CFHR = #{item.cfhr},
    REWK = #{item.rewk},
    UAHR = #{item.uahr},
    LIMN = #{item.limn},
    EOMN = #{item.eomn},
    OT1 = #{item.ot1},
    OT2 = #{item.ot2},
    OT3 = #{item.ot3},
    CLOT1 = #{item.clot1},
    PERL = #{item.perl},
    OCCL = #{item.occl},
    OTCL = #{item.otcl},
    ANNL = #{item.annl},
    ANST = #{item.anst},
    ANWE = #{item.anwe},
    PSKL = #{item.pskl},
    BPSL = #{item.bpsl},
    ISJL = #{item.isjl},
    COML = #{item.coml},
    FANF = #{item.fanf},
    MARL = #{item.marl},
    MLPL = #{item.mlpl},
    PATL = #{item.patl},
    MATL = #{item.matl},
    DSFL = #{item.dsfl},
    BRFL = #{item.brfl},
    CIFL = #{item.cifl},
    FPGL = #{item.fpgl},
    MSCL = #{item.mscl},
    POFL = #{item.pofl},
    LADL = #{item.ladl},
    OTHL1 = #{item.othl1},
    CHILDL = #{item.childl},
    NURSL = #{item.nursl},
    TNOM = #{item.tnom},
    NBOS = #{item.nbos},
    PROCESS_FLAG = #{item.processFlag},
    CREATE_TIME = now()
    where
    TERM = #{item.term}
    and EMP_ID = #{item.empId}
  </foreach>
  </update>


  <!-- 批量删除 -->
  <delete id="deleteAll" parameterType="String">
    delete from
    <include refid="tableName"></include>
    where
    DAILY_ATTENDANCE_ANALYSIS_ID in
    <foreach item="item" index="index" collection="array" open="(" separator="," close=")">
      #{item}
    </foreach>
  </delete>

  <select id="listAbnormalAttendancesThreeDayByLeader" parameterType="pd" resultType="pd">
    SELECT first_level_mgr_id, GROUP_CONCAT(NAME) AS employees FROM biz_employee WHERE emp_id IN (SELECT emp_id FROM biz_daily_attendance_analysis WHERE term <![CDATA[>=]]> #{startDate}
    AND term <![CDATA[<]]> #{endDate}
    AND is_exception = 1)
    AND actived = 'Active'
    AND (first_level_mgr_id is not null AND first_level_mgr_id<![CDATA[<>]]>'')
    GROUP BY
    first_level_mgr_id
  </select>

  <select id="listAbnormalAttendancesSevenDayByManager" parameterType="pd" resultType="pd">
    SELECT second_level_mgr_id, GROUP_CONCAT(NAME) AS employees FROM biz_employee WHERE emp_id IN (SELECT emp_id FROM biz_daily_attendance_analysis WHERE term <![CDATA[>=]]> #{startDate}
    AND term <![CDATA[<]]> #{endDate}
    AND is_exception = 1)
    AND actived = 'Active'
    AND (second_level_mgr_id is not null  second_level_mgr_id<![CDATA[<>]]>'')
    GROUP BY
      second_level_mgr_id
  </select>

  <select id="getEmployeesWorkedSixConsecutiveDays" parameterType="pd" resultType="string">
    SELECT
      emp_id
    FROM
      biz_daily_attendance_analysis
    WHERE
      term <![CDATA[>=]]> #{startDate}
      AND begin_time IS NOT NULL
    GROUP BY
      emp_id
    HAVING
      count(emp_id) >= 6
  </select>

  <!-- 获取连续工作员工详细信息 -->
  <select id="getContinuousWorkEmployeeDetails" parameterType="pd" resultType="org.fh.model.exportModel.ContinuousWorkEmployeeExportModel">
    SELECT 
      e.eplant as plant,
      e.production_line as workshop,
      e.name as name,
      e.emp_id as empId,
      e.work_team as workTeam,
      COUNT(da.emp_id) as continuousDays
    FROM 
      biz_daily_attendance_analysis da
    LEFT JOIN 
      biz_employee e ON da.emp_id = e.emp_id
    WHERE 
      da.term <![CDATA[>=]]> #{startDate}
      AND da.begin_time IS NOT NULL
      AND e.actived = 'Active'
    GROUP BY 
      da.emp_id, e.eplant, e.production_line, e.name, e.work_team
    HAVING 
      COUNT(da.emp_id) >= 6
    ORDER BY 
      e.eplant, e.production_line, e.name
  </select>

</mapper>