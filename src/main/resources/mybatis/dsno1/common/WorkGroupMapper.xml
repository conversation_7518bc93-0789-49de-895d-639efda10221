<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.fh.mapper.dsno1.common.WorkGroupMapper">
    <resultMap id="BaseResultMap" type="org.fh.vo.common.WorkGroupVo">
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="cycle_day" jdbcType="INTEGER" property="cycleDay"/>
        <result column="work_day" jdbcType="INTEGER" property="workDay"/>
        <result column="rest_day" jdbcType="INTEGER" property="restDay"/>
        <result column="plant" jdbcType="VARCHAR" property="plant"/>
        <result column="day_format" jdbcType="VARCHAR" property="dayFormat"/>
        <result column="workshop" jdbcType="VARCHAR" property="workshop"/>
        <result column="vsm" jdbcType="VARCHAR" property="vsm"/>
        <result column="production_line" jdbcType="VARCHAR" property="productionLine"/>
        <result column="valid_work_type" jdbcType="VARCHAR" property="validWorkType"/>
        <result column="not_valid_type" jdbcType="VARCHAR" property="notValidType"/>
        <result column="leader_id" jdbcType="VARCHAR" property="leaderId"/>
        <result column="leaderName" jdbcType="VARCHAR" property="leaderName"/>
        <result column="created_by" jdbcType="VARCHAR" property="createdBy"/>
        <result column="created_time" jdbcType="TIMESTAMP" property="createdTime"/>
        <result column="update_by" jdbcType="VARCHAR" property="updateBy"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>
    <sql id="Base_Column_List">
    id, name, cycle_day, work_day, rest_day, plant, workshop, production_line, valid_work_type, 
    not_valid_type, leader_id, created_by, created_time, update_by, update_time
  </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from biz_work_group
        where id = #{id,jdbcType=VARCHAR}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from biz_work_group
    where id = #{id,jdbcType=VARCHAR}
  </delete>
    <insert id="insert" parameterType="org.fh.entity.common.WorkGroupEntity">
    insert into biz_work_group (id, name, cycle_day, 
      work_day, rest_day, plant, 
      workshop, production_line, valid_work_type, 
      not_valid_type, leader_id,
      created_by, created_time, update_by, 
      update_time)
    values (#{id,jdbcType=VARCHAR}, #{name,jdbcType=VARCHAR}, #{cycleDay,jdbcType=INTEGER}, 
      #{workDay,jdbcType=INTEGER}, #{restDay,jdbcType=INTEGER}, #{plant,jdbcType=VARCHAR}, 
      #{workshop,jdbcType=VARCHAR}, #{productionLine,jdbcType=VARCHAR}, #{validWorkType,jdbcType=VARCHAR}, 
      #{notValidType,jdbcType=VARCHAR}, #{leaderId,jdbcType=VARCHAR},
      #{createdBy,jdbcType=VARCHAR}, #{createdTime,jdbcType=TIMESTAMP}, #{updateBy,jdbcType=VARCHAR}, 
      #{updateTime,jdbcType=TIMESTAMP})
  </insert>
    <insert id="insertSelective" parameterType="org.fh.entity.common.WorkGroupEntity">
        insert into biz_work_group
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="name != null">
                name,
            </if>
            <if test="cycleDay != null">
                cycle_day,
            </if>
            <if test="workDay != null">
                work_day,
            </if>
            <if test="restDay != null">
                rest_day,
            </if>
            <if test="plant != null">
                plant,
            </if>
            <if test="workshop != null">
                workshop,
            </if>
            <if test="productionLine != null">
                production_line,
            </if>
            <if test="validWorkType != null">
                valid_work_type,
            </if>
            <if test="notValidType != null">
                not_valid_type,
            </if>
            <if test="leaderId != null">
                leader_id,
            </if>
            <if test="createdBy != null">
                created_by,
            </if>
            <if test="createdTime != null">
                created_time,
            </if>
            <if test="updateBy != null">
                update_by,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=VARCHAR},
            </if>
            <if test="name != null">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="cycleDay != null">
                #{cycleDay,jdbcType=INTEGER},
            </if>
            <if test="workDay != null">
                #{workDay,jdbcType=INTEGER},
            </if>
            <if test="restDay != null">
                #{restDay,jdbcType=INTEGER},
            </if>
            <if test="plant != null">
                #{plant,jdbcType=VARCHAR},
            </if>
            <if test="workshop != null">
                #{workshop,jdbcType=VARCHAR},
            </if>
            <if test="productionLine != null">
                #{productionLine,jdbcType=VARCHAR},
            </if>
            <if test="validWorkType != null">
                #{validWorkType,jdbcType=VARCHAR},
            </if>
            <if test="notValidType != null">
                #{notValidType,jdbcType=VARCHAR},
            </if>
            <if test="leaderId != null">
                #{leaderId,jdbcType=VARCHAR},
            </if>
            <if test="createdBy != null">
                #{createdBy,jdbcType=VARCHAR},
            </if>
            <if test="createdTime != null">
                #{createdTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateBy != null">
                #{updateBy,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="org.fh.entity.common.WorkGroupEntity">
        update biz_work_group
        <set>
            <if test="name != null">
                name = #{name,jdbcType=VARCHAR},
            </if>
            <if test="cycleDay != null">
                cycle_day = #{cycleDay,jdbcType=INTEGER},
            </if>
            <if test="workDay != null">
                work_day = #{workDay,jdbcType=INTEGER},
            </if>
            <if test="restDay != null">
                rest_day = #{restDay,jdbcType=INTEGER},
            </if>
            <if test="plant != null">
                plant = #{plant,jdbcType=VARCHAR},
            </if>
            <if test="workshop != null">
                workshop = #{workshop,jdbcType=VARCHAR},
            </if>
            <if test="productionLine != null">
                production_line = #{productionLine,jdbcType=VARCHAR},
            </if>
            <if test="validWorkType != null">
                valid_work_type = #{validWorkType,jdbcType=VARCHAR},
            </if>
            <if test="notValidType != null">
                not_valid_type = #{notValidType,jdbcType=VARCHAR},
            </if>
            <if test="leaderId != null">
                leader_id = #{leaderId,jdbcType=VARCHAR},
            </if>
            <if test="createdBy != null">
                created_by = #{createdBy,jdbcType=VARCHAR},
            </if>
            <if test="createdTime != null">
                created_time = #{createdTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateBy != null">
                update_by = #{updateBy,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=VARCHAR}
    </update>
    <update id="updateByPrimaryKey" parameterType="org.fh.entity.common.WorkGroupEntity">
    update biz_work_group
    set name = #{name,jdbcType=VARCHAR},
      cycle_day = #{cycleDay,jdbcType=INTEGER},
      work_day = #{workDay,jdbcType=INTEGER},
      rest_day = #{restDay,jdbcType=INTEGER},
      plant = #{plant,jdbcType=VARCHAR},
      workshop = #{workshop,jdbcType=VARCHAR},
      production_line = #{productionLine,jdbcType=VARCHAR},
      valid_work_type = #{validWorkType,jdbcType=VARCHAR},
      not_valid_type = #{notValidType,jdbcType=VARCHAR},
      leader_id = #{leaderId,jdbcType=VARCHAR},
      created_by = #{createdBy,jdbcType=VARCHAR},
      created_time = #{createdTime,jdbcType=TIMESTAMP},
      update_by = #{updateBy,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=VARCHAR}
  </update>

    <!-- 单个添加班组信息 -->
    <insert id="addWorkGroup" parameterType="org.fh.entity.common.WorkGroupEntity">
        insert into biz_work_group
        <trim prefix="(" suffix=")" suffixOverrides=",">
                id,
            <if test="name != null and name !=''">
                name,
            </if>
            <if test="cycleDay != null">
                cycle_day,
            </if>
            <if test="workDay != null">
                work_day,
            </if>
            <if test="restDay != null">
                rest_day,
            </if>
            <if test="plant != null and plant !=''">
                plant,
            </if>
            <if test="workshop != null and workshop !=''">
                workshop,
            </if>
            <if test="productionLine != null and productionLine !=''">
                production_line,
            </if>
            <if test="validWorkType != null and validWorkType !=''">
                valid_work_type,
            </if>
            <if test="notValidType != null and notValidType !=''">
                not_valid_type,
            </if>
            <if test="leaderId != null and leaderId !=''">
                leader_id,
            </if>
            <if test="createdBy != null and createdBy !=''">
                created_by,
            </if>
                created_time
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                #{id,jdbcType=VARCHAR},
            <if test="name != null and name !=''">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="cycleDay != null">
                #{cycleDay,jdbcType=INTEGER},
            </if>
            <if test="workDay != null">
                #{workDay,jdbcType=INTEGER},
            </if>
            <if test="restDay != null">
                #{restDay,jdbcType=INTEGER},
            </if>
            <if test="plant != null and plant !=''">
                #{plant},
            </if>
            <if test="workshop != null and workshop !=''">
                #{workshop},
            </if>
            <if test="productionLine != null and productionLine !=''">
                #{productionLine},
            </if>
            <if test="validWorkType != null and validWorkType !=''">
                #{validWorkType},
            </if>
            <if test="notValidType != null and notValidType !=''">
                #{notValidType},
            </if>
            <if test="leaderId != null and leaderId !=''">
                #{leaderId},
            </if>
            <if test="createdBy != null and createdBy !=''">
                #{createdBy},
            </if>
            now()
        </trim>
    </insert>

    <!-- 修改班组信息 -->
    <update id="updateWorkGroup" parameterType="org.fh.entity.common.WorkGroupEntity">
        update biz_work_group
        <set>
            <if test="cycleDay != null">
                cycle_day = #{cycleDay},
            </if>
            <if test="workDay != null">
                work_day = #{workDay},
            </if>
            <if test="restDay != null">
                rest_day = #{restDay},
            </if>
            <if test="dayFormat != null and dayFormat !=''">
                day_format = #{dayFormat},
            </if>
            <if test="productionLine != null and dayFormat !=''">
                production_line = #{productionLine},
            </if>
            <if test="validWorkType != null and validWorkType !=''">
                valid_work_type = #{validWorkType},
            </if>
            <if test="plant != null and plant !=''">
                plant = #{plant},
            </if>
            <if test="workshop != null and workshop !=''">
                workshop = #{workshop},
            </if>
            <if test="vsm != null and vsm !=''">
                vsm = #{vsm},
            </if>
            <if test="notValidType != null and notValidType !=''">
                not_valid_type = #{notValidType},
            </if>
            <if test="leaderId != null and leaderId !=''">
                leader_id = #{leaderId},
            </if>
            <if test="updateBy != null and updateBy !=''">
                update_by = #{updateBy},
            </if>
            <if test="updateTime != null">
                update_time = now(),
            </if>
        </set>
        where id = #{id}
    </update>

    <select id="queryEmployeeWorkTeam" resultType="org.fh.entity.common.WorkGroupEntity">
        select distinct work_team as name from biz_employee e
             where e.work_team is not null
	          and e.work_team !='无'
	          and e.actived != 'Withdrawn'
    </select>

    <!-- 查询所有的班组信息 -->
    <select id="queryWorkGroupAll" resultType="org.fh.entity.common.WorkGroupEntity">
        select id,`name`,`name` as workTeam,plant,workshop,vsm,valid_work_type,valid_work_type as validWorkType from biz_work_group where deleted = '0'
    </select>

    <!-- 批量添加班组信息 -->
    <insert id="addWorkGroupList" parameterType="collection">
        insert into biz_work_group
        (id,name,plant,workshop,deleted,created_by,created_time)
        values
        <foreach collection="list" separator="," item="item">
            (
            #{item.id},#{item.name},#{item.plant},#{item.workshop},0,#{item.createdBy},now()
            )
        </foreach>
    </insert>

    <!-- 判断班组是否已经存在 -->
    <select id="checkWorkGroup" parameterType="org.fh.entity.common.WorkGroupEntity" resultType="int">
        select count(*) from biz_work_group where namd = #{name},plant = #{plant} and workshop = #{workshop}
        <if test="id !=null and id !=''">
            and id != #{id}
        </if>
    </select>

    <select id="queryWorkGroupPage" parameterType="org.fh.entity.common.WorkGroupEntity" resultMap="BaseResultMap">
        select wg.id,wg.name,wg.cycle_day,wg.work_day,wg.rest_day,wg.plant,wg.workshop,wg.vsm,wg.production_line,wg.valid_work_type,
        wg.not_valid_type,wg.leader_id,wg.created_by,wg.created_time,wg.update_by,wg.update_time,e.name as leaderName
          from biz_work_group wg
          left join biz_employee e on e.emp_id = wg.leader_id
          <where>
              wg.deleted = '0'
              <if test="name !=null and name !=''">
                  and wg.name like '%${name}%'
              </if>
              <if test="plant !=null and plant !=''">
                  and wg.plant = #{plant}
              </if>
              <if test="workshop !=null and workshop !=''">
                  and wg.workshop like '%${workshop}%'
              </if>
              <if test="vsm !=null and vsm !=''">
                  and wg.vsm like '%${vsm}%'
              </if>
              <if test="productionLine !=null and productionLine !=''">
                  and wg.production_line like '%${productionLine}%'
              </if>
          </where>
    </select>

    <!-- 查看班组详细信息 -->
    <select id="queryWorkGroupDetail" parameterType="string" resultMap="BaseResultMap">
        select id,name,cycle_day,work_day,rest_day,day_format,plant,workshop,vsm,production_line,valid_work_type,
          not_valid_type,leader_id,created_by,created_time,update_by,update_time
          from biz_work_group
          where id = #{id}
    </select>

    <select id="queryWorkGroupDetailByName" resultMap="BaseResultMap">
        select id,name,cycle_day,work_day,rest_day,day_format,plant,workshop,vsm,production_line,valid_work_type,
          not_valid_type,leader_id,created_by,created_time,update_by,update_time
          from biz_work_group wg
          where wg.deleted = '0' and `name` = #{name}
          <if test="plant !=null and plant !=''">
              and plant = #{plant}
          </if>
        <if test="workshop !=null and workshop !=''">
            and workshop = #{workshop}
        </if>
            limit 1
    </select>

    <update id="deleteWorkGroupList" parameterType="collection">
        update biz_work_group set deleted = '1',update_by = #{username},update_time = now()
        where id in
        <foreach collection="list" separator="," item="item" close=")" open="(">
            #{item}
        </foreach>
    </update>
</mapper>