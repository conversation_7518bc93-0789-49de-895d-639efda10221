<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.fh.mapper.dsno1.common.UserCountReportMapper">
    <resultMap id="BaseResultMap" type="org.fh.vo.common.UserCountReportVo">
        <id column="user_count_report" jdbcType="VARCHAR" property="userCountReport"/>
        <result column="plant" jdbcType="VARCHAR" property="plant"/>
        <result column="workshop" jdbcType="VARCHAR" property="workshop"/>
        <result column="direct" jdbcType="INTEGER" property="direct"/>
        <result column="position" jdbcType="INTEGER" property="position"/>
        <result column="indirect" jdbcType="INTEGER" property="indirect"/>
        <result column="statistics_date" jdbcType="VARCHAR" property="statisticsDate"/>
    </resultMap>
    <sql id="Base_Column_List">
    user_count_report, plant, workshop, direct, indirect, statistics_date
  </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from biz_user_count_report
        where user_count_report = #{userCountReport,jdbcType=VARCHAR}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from biz_user_count_report
    where user_count_report = #{userCountReport,jdbcType=VARCHAR}
  </delete>
    <insert id="insert" parameterType="org.fh.entity.common.UserCountReportEntity">
    insert into biz_user_count_report (user_count_report, plant, workshop, 
      direct, indirect, statistics_date
      )
    values (#{userCountReport,jdbcType=VARCHAR}, #{plant,jdbcType=VARCHAR}, #{workshop,jdbcType=VARCHAR}, 
      #{direct,jdbcType=INTEGER}, #{indirect,jdbcType=INTEGER}, #{statisticsDate,jdbcType=VARCHAR}
      )
  </insert>
    <insert id="insertSelective" parameterType="org.fh.entity.common.UserCountReportEntity">
        insert into biz_user_count_report
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userCountReport != null">
                user_count_report,
            </if>
            <if test="plant != null">
                plant,
            </if>
            <if test="workshop != null">
                workshop,
            </if>
            <if test="direct != null">
                direct,
            </if>
            <if test="indirect != null">
                indirect,
            </if>
            <if test="statisticsDate != null">
                statistics_date,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userCountReport != null">
                #{userCountReport,jdbcType=VARCHAR},
            </if>
            <if test="plant != null">
                #{plant,jdbcType=VARCHAR},
            </if>
            <if test="workshop != null">
                #{workshop,jdbcType=VARCHAR},
            </if>
            <if test="direct != null">
                #{direct,jdbcType=INTEGER},
            </if>
            <if test="indirect != null">
                #{indirect,jdbcType=INTEGER},
            </if>
            <if test="statisticsDate != null">
                #{statisticsDate,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="org.fh.entity.common.UserCountReportEntity">
        update biz_user_count_report
        <set>
            <if test="plant != null">
                plant = #{plant,jdbcType=VARCHAR},
            </if>
            <if test="workshop != null">
                workshop = #{workshop,jdbcType=VARCHAR},
            </if>
            <if test="direct != null">
                direct = #{direct,jdbcType=INTEGER},
            </if>
            <if test="indirect != null">
                indirect = #{indirect,jdbcType=INTEGER},
            </if>
            <if test="statisticsDate != null">
                statistics_date = #{statisticsDate,jdbcType=VARCHAR},
            </if>
        </set>
        where user_count_report_id = #{userCountReportId,jdbcType=VARCHAR}
    </update>
    <update id="updateByPrimaryKey" parameterType="org.fh.entity.common.UserCountReportEntity">
        update biz_user_count_report
        set plant = #{plant,jdbcType=VARCHAR},
          workshop = #{workshop,jdbcType=VARCHAR},
          direct = #{direct,jdbcType=INTEGER},
          indirect = #{indirect,jdbcType=INTEGER},
          statistics_date = #{statisticsDate,jdbcType=VARCHAR}
        where user_count_report_id = #{userCountReportId,jdbcType=VARCHAR}
      </update>

    <!-- 分页查询每月人数报告 -->
    <select id="queryUserCountReportPage" parameterType="org.fh.entity.common.UserCountReportEntity" resultMap="BaseResultMap">
        select plant,workshop,sum(direct) as direct,sum(indirect) as indirect,statistics_date
        from biz_user_count_report
        <where>
            <if test="plant !=null and plant !=''">
                and plant = #{plant}
            </if>
            <if test="workshop !=null and workshop !=''">
                and workshop = #{workshop}
            </if>
            <if test="vsm !=null and vsm !=''">
                and vsm = #{vsm}
            </if>
            <if test="year !=null and year !=''">
                and `year` = #{year}
            </if>
        </where>
        group by plant,workshop,statistics_date;
    </select>

    <select id="queryUserCountReportListByPosition" resultMap="BaseResultMap">
        select plant,workshop,direct+indirect as direct,`position`,statistics_date
        from biz_user_count_report
        <where>
            <if test="plant !=null and plant !=''">
                and plant = #{plant}
            </if>
            <if test="workshop !=null and workshop !=''">
                and workshop = #{workshop}
            </if>
            <if test="vsm !=null and vsm !=''">
                and vsm = #{vsm}
            </if>
            <if test="year !=null and year !=''">
                and `year` = #{year}
            </if>
        </where>
        GROUP BY
        plant,
        workshop,
        statistics_date,`position`;
    </select>

    <!-- 批量添加每月人数报告 -->
    <insert id="addUserCountReportList" parameterType="collection">
        insert into biz_user_count_report(user_count_report_id,plant,workshop,vsm,direct,indirect,statistics_date,`year`,`position`)
        values
        <foreach collection="list" separator="," item="item">
            (
            #{item.userCountReportId},#{item.plant},#{item.workshop},#{item.vsm},#{item.direct},#{item.indirect},#{item.statisticsDate},#{item.year},#{item.position}
            )
        </foreach>
    </insert>
</mapper>