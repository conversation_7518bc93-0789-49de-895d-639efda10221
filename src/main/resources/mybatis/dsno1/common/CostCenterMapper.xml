<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.fh.mapper.dsno1.common.CostCenterMapper">
    <resultMap id="BaseResultMap" type="org.fh.entity.common.CostCenterEntity">
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="cost_center" jdbcType="VARCHAR" property="costCenter"/>
        <result column="plant" jdbcType="VARCHAR" property="plant"/>
        <result column="workshop" jdbcType="VARCHAR" property="workshop"/>
        <result column="vsm" jdbcType="VARCHAR" property="vsm"/>
        <result column="created_by" jdbcType="VARCHAR" property="createdBy"/>
        <result column="created_time" jdbcType="TIMESTAMP" property="createdTime"/>
        <result column="updated_by" jdbcType="VARCHAR" property="updatedBy"/>
        <result column="updated_time" jdbcType="TIMESTAMP" property="updatedTime"/>
    </resultMap>
    <sql id="Base_Column_List">
    id, cost_center, plant, workshop, vsm, created_by, created_time, updated_by, updated_time
  </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from biz_cost_center
        where id = #{id,jdbcType=VARCHAR}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from biz_cost_center
    where id = #{id,jdbcType=VARCHAR}
  </delete>
    <insert id="insert" parameterType="org.fh.entity.common.CostCenterEntity">
    insert into biz_cost_center (id, cost_center, plant, 
      workshop, vsm, created_by, 
      created_time, updated_by, updated_time
      )
    values (#{id,jdbcType=VARCHAR}, #{costCenter,jdbcType=VARCHAR}, #{plant,jdbcType=VARCHAR}, 
      #{workshop,jdbcType=VARCHAR}, #{vsm,jdbcType=VARCHAR}, #{createdBy,jdbcType=VARCHAR}, 
      #{createdTime,jdbcType=TIMESTAMP}, #{updatedBy,jdbcType=VARCHAR}, #{updatedTime,jdbcType=TIMESTAMP}
      )
  </insert>
    <insert id="insertSelective" parameterType="org.fh.entity.common.CostCenterEntity">
        insert into biz_cost_center
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="costCenter != null">
                cost_center,
            </if>
            <if test="plant != null">
                plant,
            </if>
            <if test="workshop != null">
                workshop,
            </if>
            <if test="vsm != null">
                vsm,
            </if>
            <if test="createdBy != null">
                created_by,
            </if>
            <if test="createdTime != null">
                created_time,
            </if>
            <if test="updatedBy != null">
                updated_by,
            </if>
            <if test="updatedTime != null">
                updated_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=VARCHAR},
            </if>
            <if test="costCenter != null">
                #{costCenter,jdbcType=VARCHAR},
            </if>
            <if test="plant != null">
                #{plant,jdbcType=VARCHAR},
            </if>
            <if test="workshop != null">
                #{workshop,jdbcType=VARCHAR},
            </if>
            <if test="vsm != null">
                #{vsm,jdbcType=VARCHAR},
            </if>
            <if test="createdBy != null">
                #{createdBy,jdbcType=VARCHAR},
            </if>
            <if test="createdTime != null">
                #{createdTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updatedBy != null">
                #{updatedBy,jdbcType=VARCHAR},
            </if>
            <if test="updatedTime != null">
                #{updatedTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="org.fh.entity.common.CostCenterEntity">
        update biz_cost_center
        <set>
            <if test="costCenter != null">
                cost_center = #{costCenter,jdbcType=VARCHAR},
            </if>
            <if test="plant != null">
                plant = #{plant,jdbcType=VARCHAR},
            </if>
            <if test="workshop != null">
                workshop = #{workshop,jdbcType=VARCHAR},
            </if>
            <if test="vsm != null">
                vsm = #{vsm,jdbcType=VARCHAR},
            </if>
            <if test="createdBy != null">
                created_by = #{createdBy,jdbcType=VARCHAR},
            </if>
            <if test="createdTime != null">
                created_time = #{createdTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updatedBy != null">
                updated_by = #{updatedBy,jdbcType=VARCHAR},
            </if>
            <if test="updatedTime != null">
                updated_time = #{updatedTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=VARCHAR}
    </update>
    <update id="updateByPrimaryKey" parameterType="org.fh.entity.common.CostCenterEntity">
        update biz_cost_center
        set cost_center = #{costCenter,jdbcType=VARCHAR},
          plant = #{plant,jdbcType=VARCHAR},
          workshop = #{workshop,jdbcType=VARCHAR},
          vsm = #{vsm,jdbcType=VARCHAR},
          created_by = #{createdBy,jdbcType=VARCHAR},
          created_time = #{createdTime,jdbcType=TIMESTAMP},
          updated_by = #{updatedBy,jdbcType=VARCHAR},
          updated_time = #{updatedTime,jdbcType=TIMESTAMP}
        where id = #{id,jdbcType=VARCHAR}
  </update>

    <!-- 查询所有成本中心编号 -->
    <select id="queryCostCenter" resultType="string">
        select distinct cost_center from biz_cost_center
    </select>

    <!-- 批量添加成本中心数据 -->
    <insert id="addCostCenterList" parameterType="collection">
        insert into biz_cost_center
        (id,cost_center,function,plant,department,workshop,big_workshop,sub_workshop,vsm,function_head,created_by,created_time)
        values
        <foreach collection="list" separator="," item="item">
            (
            #{item.id},#{item.costCenter},#{item.function},#{item.plant},#{item.department},#{item.workshop},#{item.bigWorkshop},#{item.subWorkshop},#{item.vsm},#{item.functionHead},#{item.createdBy},now()
            )
        </foreach>
    </insert>

    <!-- 分页查询成本中心数据 -->
    <select id="queryCostCenterPage" resultType="org.fh.vo.common.CostCenterVo">
        select plant,function,workshop,cost_center as costCenter,big_workshop as bigWorkshop,sub_workshop as subWorkshop,department,vsm,function_head as functionHead from biz_cost_center
    </select>

    <!-- 查询导出成本中心数据 -->
    <select id="queryCostCenterList" resultType="org.fh.model.exportModel.CostCenterExportModel">
        select plant,workshop,function_head as functionHead, cost_center as costCenter,function,big_workshop as bigWorkshop,sub_workshop as subWorkshop,department,vsm from biz_cost_center
    </select>

    <!-- 批量修改成本中心数据 -->
    <update id="updateCostCenterList" parameterType="collection">
        <foreach collection="list" item="item" separator=";">
            update biz_cost_center set function = #{item.function},plant = #{item.plant},department = #{item.department},workshop = #{item.workshop},
            big_workshop = #{item.bigWorkshop},sub_workshop = #{item.subWorkshop},vsm = #{item.vsm},function_head = #{item.functionHead},updated_by = #{item.updatedBy},
            updated_time = now()
            where cost_center = #{item.costCenter}
        </foreach>
    </update>
</mapper>