<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.fh.mapper.dsno1.common.PositionMapper">
    <resultMap id="BaseResultMap" type="org.fh.entity.common.PositionEntity">
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="direct" jdbcType="VARCHAR" property="direct"/>
        <result column="direct_name" jdbcType="VARCHAR" property="directName"/>
        <result column="created_by" jdbcType="VARCHAR" property="createdBy"/>
        <result column="created_time" jdbcType="TIMESTAMP" property="createdTime"/>
        <result column="updated_by" jdbcType="VARCHAR" property="updatedBy"/>
        <result column="updated_time" jdbcType="TIMESTAMP" property="updatedTime"/>
    </resultMap>
    <sql id="Base_Column_List">
    id, direct, direct_name, created_by, created_time, updated_by, updated_time
  </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from biz_position
        where id = #{id,jdbcType=VARCHAR}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from biz_position
    where id = #{id,jdbcType=VARCHAR}
  </delete>
    <insert id="insert" parameterType="org.fh.entity.common.PositionEntity">
    insert into biz_position (id, direct, direct_name, 
      created_by, created_time, updated_by, 
      updated_time)
    values (#{id,jdbcType=VARCHAR}, #{direct,jdbcType=VARCHAR}, #{directName,jdbcType=VARCHAR}, 
      #{createdBy,jdbcType=VARCHAR}, #{createdTime,jdbcType=TIMESTAMP}, #{updatedBy,jdbcType=VARCHAR}, 
      #{updatedTime,jdbcType=TIMESTAMP})
  </insert>
    <insert id="insertSelective" parameterType="org.fh.entity.common.PositionEntity">
        insert into biz_position
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="direct != null">
                direct,
            </if>
            <if test="directName != null">
                direct_name,
            </if>
            <if test="createdBy != null">
                created_by,
            </if>
            <if test="createdTime != null">
                created_time,
            </if>
            <if test="updatedBy != null">
                updated_by,
            </if>
            <if test="updatedTime != null">
                updated_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=VARCHAR},
            </if>
            <if test="direct != null">
                #{direct,jdbcType=VARCHAR},
            </if>
            <if test="directName != null">
                #{directName,jdbcType=VARCHAR},
            </if>
            <if test="createdBy != null">
                #{createdBy,jdbcType=VARCHAR},
            </if>
            <if test="createdTime != null">
                #{createdTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updatedBy != null">
                #{updatedBy,jdbcType=VARCHAR},
            </if>
            <if test="updatedTime != null">
                #{updatedTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="org.fh.entity.common.PositionEntity">
        update biz_position
        <set>
            <if test="direct != null">
                direct = #{direct,jdbcType=VARCHAR},
            </if>
            <if test="directName != null">
                direct_name = #{directName,jdbcType=VARCHAR},
            </if>
            <if test="createdBy != null">
                created_by = #{createdBy,jdbcType=VARCHAR},
            </if>
            <if test="createdTime != null">
                created_time = #{createdTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updatedBy != null">
                updated_by = #{updatedBy,jdbcType=VARCHAR},
            </if>
            <if test="updatedTime != null">
                updated_time = #{updatedTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=VARCHAR}
    </update>
    <update id="updateByPrimaryKey" parameterType="org.fh.entity.common.PositionEntity">
        update biz_position
        set direct = #{direct,jdbcType=VARCHAR},
          direct_name = #{directName,jdbcType=VARCHAR},
          created_by = #{createdBy,jdbcType=VARCHAR},
          created_time = #{createdTime,jdbcType=TIMESTAMP},
          updated_by = #{updatedBy,jdbcType=VARCHAR},
          updated_time = #{updatedTime,jdbcType=TIMESTAMP}
        where id = #{id,jdbcType=VARCHAR}
  </update>

    <!-- 查询所有的职位编号 -->
    <select id="queryDirectCodeAll" resultType="string">
        select distinct direct from biz_position
    </select>

    <!-- 批量添加职位信息 -->
    <insert id="addPositionListData" parameterType="collection">
        insert into biz_position
        (id,direct,direct_name,created_by,created_time)
        values
        <foreach collection="list" item="item" separator=",">
            (
              #{item.id},#{item.direct},#{item.directName},#{item.createdBy},now()
            )
        </foreach>
    </insert>

    <!-- 职位信息分页查询 -->
    <select id="queryPositionPage" resultType="org.fh.vo.common.PositionVo">
        select direct,direct_name as directName
        from biz_position
    </select>

    <!-- 职位信息导出查询 -->
    <select id="queryPositionList" resultType="org.fh.model.exportModel.PositionExportModel">
        select direct,direct_name as directName
        from biz_position
    </select>
</mapper>