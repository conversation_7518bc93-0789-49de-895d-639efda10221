<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.fh.mapper.dsno1.common.BorrowMapper">
    <resultMap id="BaseResultMap" type="org.fh.vo.common.BorrowVo">
        <id column="borrow_id" jdbcType="VARCHAR" property="borrowId"/>
        <result column="work_team" jdbcType="VARCHAR" property="workTeam"/>
        <result column="borrow_work_team" jdbcType="VARCHAR" property="borrowWorkTeam"/>
        <result column="leader" jdbcType="VARCHAR" property="leader"/>
        <result column="leaderName" jdbcType="VARCHAR" property="leaderName"/>
        <result column="open_id" jdbcType="VARCHAR" property="openId"/>
        <result column="start_time" jdbcType="TIMESTAMP" property="startTime"/>
        <result column="end_time" jdbcType="TIMESTAMP" property="endTime"/>
        <result column="passivity_count" jdbcType="INTEGER" property="passivityCount"/>
        <result column="status" jdbcType="INTEGER" property="status"/>
        <result column="statusName" jdbcType="INTEGER" property="statusName"/>
        <result column="apply_date" jdbcType="TIMESTAMP" property="applyDate"/>
        <result column="apply_user" jdbcType="VARCHAR" property="applyUser"/>
        <result column="applyUserName" jdbcType="VARCHAR" property="applyUserName"/>
        <result column="processinstanceid" jdbcType="VARCHAR" property="processinstanceid"/>
        <result column="work_type" jdbcType="VARCHAR" property="workType"/>
    </resultMap>
    <sql id="Base_Column_List">
    borrow_id, work_team,borrow_work_team, leader, start_time, end_time, passivity_count, status,
    apply_date, apply_user, processinstanceid,work_type
  </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from biz_borrow
        where borrow_id = #{borrowId,jdbcType=VARCHAR}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from biz_borrow
    where borrow_id = #{borrowId,jdbcType=VARCHAR}
  </delete>
    <insert id="insert" parameterType="org.fh.entity.common.BorrowEntity">
    insert into biz_borrow (borrow_id, borrow_work_team, leader, 
      start_time, end_time, passivity_count, 
      status, apply_date, apply_user,
      processinstanceid,work_type)
    values (#{borrowId,jdbcType=VARCHAR}, #{borrowWorkTeam,jdbcType=VARCHAR}, #{leader,jdbcType=VARCHAR}, 
      #{startTime,jdbcType=TIMESTAMP}, #{endTime,jdbcType=TIMESTAMP}, #{passivityCount,jdbcType=INTEGER}, 
      #{status,jdbcType=INTEGER}, #{applyDate,jdbcType=TIMESTAMP}, #{applyUser,jdbcType=VARCHAR},
      #{processinstanceid,jdbcType=VARCHAR},#{workType,jdbcType=VARCHAR})
  </insert>
    <insert id="insertSelective" parameterType="org.fh.entity.common.BorrowEntity">
        insert into biz_borrow
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="borrowId != null">
                borrow_id,
            </if>
            <if test="borrowWorkTeam != null">
                borrow_work_team,
            </if>
            <if test="leader != null">
                leader,
            </if>
            <if test="startTime != null">
                start_time,
            </if>
            <if test="endTime != null">
                end_time,
            </if>
            <if test="passivityCount != null">
                passivity_count,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="applyDate != null">
                apply_date,
            </if>
            <if test="applyUser != null">
                apply_user,
            </if>
            <if test="processinstanceid != null">
                processinstanceid,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="borrowId != null">
                #{borrowId,jdbcType=VARCHAR},
            </if>
            <if test="borrowWorkTeam != null">
                #{borrowWorkTeam,jdbcType=VARCHAR},
            </if>
            <if test="leader != null">
                #{leader,jdbcType=VARCHAR},
            </if>
            <if test="startTime != null">
                #{startTime,jdbcType=TIMESTAMP},
            </if>
            <if test="endTime != null">
                #{endTime,jdbcType=TIMESTAMP},
            </if>
            <if test="passivityCount != null">
                #{passivityCount,jdbcType=INTEGER},
            </if>
            <if test="status != null">
                #{status,jdbcType=INTEGER},
            </if>
            <if test="applyDate != null">
                #{applyDate,jdbcType=TIMESTAMP},
            </if>
            <if test="applyUser != null">
                #{applyUser,jdbcType=VARCHAR},
            </if>
            <if test="processinstanceid != null">
                #{processinstanceid,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="org.fh.entity.common.BorrowEntity">
        update biz_borrow
        <set>
            <if test="workTeam != null">
                work_team = #{workTeam,jdbcType=VARCHAR},
            </if>
            <if test="borrowWorkTeam != null">
                borrow_work_team = #{borrowWorkTeam,jdbcType=VARCHAR},
            </if>
            <if test="leader != null">
                leader = #{leader,jdbcType=VARCHAR},
            </if>
            <if test="startTime != null">
                start_time = #{startTime,jdbcType=TIMESTAMP},
            </if>
            <if test="endTime != null">
                end_time = #{endTime,jdbcType=TIMESTAMP},
            </if>
            <if test="passivityCount != null">
                passivity_count = #{passivityCount,jdbcType=INTEGER},
            </if>
            <if test="status != null">
                status = #{status,jdbcType=INTEGER},
            </if>
            <if test="applyDate != null">
                apply_date = #{applyDate,jdbcType=TIMESTAMP},
            </if>
            <if test="applyUser != null">
                apply_user = #{applyUser,jdbcType=VARCHAR},
            </if>
            <if test="processinstanceid != null">
                processinstanceid = #{processinstanceid,jdbcType=VARCHAR},
            </if>
            <if test="workType != null">
                work_type = #{workType,jdbcType=VARCHAR},
            </if>
        </set>
        where borrow_id = #{borrowId,jdbcType=VARCHAR}
    </update>
    <update id="updateByPrimaryKey" parameterType="org.fh.entity.common.BorrowEntity">
        update biz_borrow
        set borrow_work_team = #{borrowWorkTeam,jdbcType=VARCHAR},
          leader = #{leader,jdbcType=VARCHAR},
          start_time = #{startTime,jdbcType=TIMESTAMP},
          end_time = #{endTime,jdbcType=TIMESTAMP},
          passivity_count = #{passivityCount,jdbcType=INTEGER},
          status = #{status,jdbcType=INTEGER},
          apply_date = #{applyDate,jdbcType=TIMESTAMP},
          apply_user = #{applyUser,jdbcType=VARCHAR},
          processinstanceid = #{processinstanceid,jdbcType=VARCHAR},
          work_type = #{workType,jdbcType=VARCHAR}
        where borrow_id = #{borrowId,jdbcType=VARCHAR}
      </update>

    <!-- 发起借调申请分页查询 -->
    <select id="queryBorrowPage" resultMap="BaseResultMap">
        select b.borrow_id,b.work_team,b.borrow_work_team,b.leader,b.start_time,b.end_time,b.passivity_count,b.status,
        b.apply_date,b.apply_user,b.processinstanceid,e.name as leaderName,b.work_type as workType,
        case
        when b.status = '1' then '待确认'
        when b.status = '2' then '已确认'
        when b.status = '3' then '被拒绝'
        end as statusName
        from biz_borrow b
        left join biz_employee e on e.emp_id = b.leader
        where apply_user = #{applyUser}
        order by apply_date desc
    </select>

    <select id="queryBorrowByProcessInstanceIdPage" parameterType="collection" resultMap="BaseResultMap">
        select b.borrow_id,b.work_team,b.borrow_work_team,b.leader,b.start_time,b.end_time,b.passivity_count,b.status,e.name as leaderName,
        b.apply_date,b.apply_user,processinstanceid,b.work_type as workType,
        case
        when b.status = '1' then '待确认'
        when b.status = '2' then '已确认'
        when b.status = '3' then '被拒绝'
        end as statusName
        from biz_borrow b
        left join biz_employee e on e.emp_id = b.leader
        where processinstanceid in (
        <foreach collection="list" separator="," item="item">
            #{item}
        </foreach>
        )
        order by apply_date desc
    </select>

    <!-- 单个添加借调申请 -->
    <insert id="addBorrow" parameterType="org.fh.entity.common.BorrowEntity">
        insert into biz_borrow
        <trim prefix="(" suffix=")" suffixOverrides=",">
                borrow_id,
            <if test="workTeam != null and workTeam !=''">
                work_team,
            </if>
            <if test="borrowWorkTeam != null and borrowWorkTeam !=''">
                borrow_work_team,
            </if>
            <if test="leader != null and leader !=''">
                leader,
            </if>
            <if test="startTime != null and startTime !=''">
                start_time,
            </if>
            <if test="endTime != null and endTime !=''">
                end_time,
            </if>
            <if test="passivityCount != null">
                passivity_count,
            </if>
            <if test="status != null">
                status,
            </if>
                apply_date,
            <if test="applyUser != null and applyUser !=''">
                apply_user,
            </if>
            <if test="processinstanceid != null and processinstanceid !=''">
                processinstanceid,
            </if>
            <if test="workType != null and workType !=''">
                work_type,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                #{borrowId,jdbcType=VARCHAR},
            <if test="workTeam != null and workTeam !=''">
                #{workTeam,jdbcType=VARCHAR},
            </if>
            <if test="borrowWorkTeam != null and borrowWorkTeam !=''">
                #{borrowWorkTeam,jdbcType=VARCHAR},
            </if>
            <if test="leader != null and leader !=''">
                #{leader,jdbcType=VARCHAR},
            </if>
            <if test="startTime != null and startTime !=''">
                #{startTime,jdbcType=TIMESTAMP},
            </if>
            <if test="endTime != null and endTime !=''">
                #{endTime,jdbcType=TIMESTAMP},
            </if>
            <if test="passivityCount != null">
                #{passivityCount,jdbcType=INTEGER},
            </if>
            <if test="status != null">
                #{status,jdbcType=INTEGER},
            </if>
            now(),
            <if test="applyUser != null and applyUser !=''">
                #{applyUser,jdbcType=VARCHAR},
            </if>
            <if test="processinstanceid != null and processinstanceid !=''">
                #{processinstanceid,jdbcType=VARCHAR},
            </if>
            <if test="workType != null and workType !=''">
                #{workType,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <!-- 根据流程实例Id查询借调申请明细 -->
    <select id="queryBorrowDetail" resultMap="BaseResultMap" parameterType="string">
        select b.work_team,b.borrow_work_team,b.leader,b.start_time,b.end_time,b.passivity_count,b.status,ee.open_id,
        b.apply_date,b.apply_user,b.processinstanceid,e.name as leaderName,ee.name as applyUserName,b.work_type,
        case
        when b.status = '1' then '待审批'
        when b.status = '2' then '已确认'
        when b.status = '3' then '被拒绝'
        end as statusName
        from biz_borrow b
        left join biz_employee e on b.leader = e.emp_id
        left join biz_employee ee on b.apply_user = ee.emp_id
        where processinstanceid = #{processinstanceid}
    </select>

    <update id="updateBorrowStatus" parameterType="org.fh.entity.common.BorrowEntity">
        update biz_borrow
        <set>
            status = #{status},
            <if test="status != null and status == 2">
                finish_time = now(),
            </if>
        </set>
        where processinstanceid = #{processinstanceid}
    </update>

    <select id="queryBorrowValid" resultMap="BaseResultMap">
        select b.work_team,b.borrow_work_team,b.leader,b.start_time,b.end_time,b.passivity_count,b.status,e.open_id,e.name as borrowUserName,
        b.apply_date,b.apply_user,b.processinstanceid,e.name as applyUserName,b.work_type as workType
        from biz_borrow b
        left join biz_borrow_user bu on bu.processinstanceid = b.processinstanceid
        left join biz_employee e on bu.emp_id = e.emp_id
        where b.end_time &gt;= #{startTime}
        and b.start_time &lt;= #{startTime}
        and bu.emp_id = #{empId}
        limit 1
    </select>


    <select id="queryBorrowValidEndTime" resultMap="BaseResultMap">
        select b.work_team,b.borrow_work_team,b.leader,b.start_time,b.end_time,b.passivity_count,b.status,e.open_id,e.name as borrowUserName,
        b.apply_date,b.apply_user,b.processinstanceid,e.name as applyUserName,b.work_type as workType
        from biz_borrow b
        left join biz_borrow_user bu on bu.processinstanceid = b.processinstanceid
        left join biz_employee e on bu.emp_id = e.emp_id
        where  b.end_time &gt;= #{endTime}
        and b.start_time &lt;= #{endTime}
        and bu.emp_id = #{empId}
        limit 1
    </select>
</mapper>