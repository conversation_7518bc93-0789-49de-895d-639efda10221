<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.fh.mapper.dsno1.common.CheckUserMapper">
    <resultMap id="BaseResultMap" type="org.fh.vo.common.CheckUserVo">
        <result column="check_id" jdbcType="VARCHAR" property="checkId"/>
        <result column="plant" jdbcType="VARCHAR" property="plant"/>
        <result column="doctor" jdbcType="VARCHAR" property="doctor"/>
        <result column="health" jdbcType="VARCHAR" property="health"/>
        <result column="welfare" jdbcType="VARCHAR" property="welfare"/>
        <result column="doctorName" jdbcType="VARCHAR" property="doctorName"/>
        <result column="healthName" jdbcType="VARCHAR" property="healthName"/>
        <result column="welfareName" jdbcType="VARCHAR" property="welfareName"/>
    </resultMap>
    <sql id="Base_Column_List">
    check_id, plant,doctor, health, welfare, create_by, create_time, update_by, update_time
  </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from biz_check_user
        where check_id = #{checkId,jdbcType=VARCHAR}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from biz_check_user
    where check_id = #{checkId,jdbcType=VARCHAR}
  </delete>
    <insert id="insert" parameterType="org.fh.entity.common.CheckUserEntity">
    insert into biz_check_user (check_id, doctor, health, 
      welfare, create_by, create_time, 
      update_by, update_time)
    values (#{checkId,jdbcType=VARCHAR}, #{doctor,jdbcType=VARCHAR}, #{health,jdbcType=VARCHAR}, 
      #{welfare,jdbcType=VARCHAR}, #{createBy,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, 
      #{updateBy,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP})
  </insert>
    <insert id="addCheckUser" parameterType="org.fh.entity.common.CheckUserEntity">
        insert into biz_check_user
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="checkId != null and checkId !=''">
                check_id,
            </if>
            <if test="doctor != null and doctor !=''">
                doctor,
            </if>
            <if test="health != null and health !=''">
                health,
            </if>
            <if test="welfare != null and welfare !=''">
                welfare,
            </if>
                create_by,create_time
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="checkId != null and checkId !=''">
                #{checkId},
            </if>
            <if test="doctor != null and doctor !=''">
                #{doctor},
            </if>
            <if test="health != null and health !=''">
                #{health},
            </if>
            <if test="welfare != null and welfare !=''">
                #{welfare},
            </if>
                #{createBy},now()
        </trim>
    </insert>
    <update id="updateCheckUser" parameterType="org.fh.entity.common.CheckUserEntity">
        update biz_check_user
        <set>
            <if test="doctor != null and checkId !=''">
                doctor = #{doctor},
            </if>
            <if test="health != null and checkId !=''">
                health = #{health},
            </if>
            <if test="welfare != null and checkId !=''">
                welfare = #{welfare},
            </if>
                update_by = #{updateBy},update_time = now()
        </set>
        where check_id = #{checkId}
    </update>
    <update id="updateByPrimaryKey" parameterType="org.fh.entity.common.CheckUserEntity">
    update biz_check_user
    set doctor = #{doctor,jdbcType=VARCHAR},
      health = #{health,jdbcType=VARCHAR},
      welfare = #{welfare,jdbcType=VARCHAR},
      create_by = #{createBy,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_by = #{updateBy,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where check_id = #{checkId,jdbcType=VARCHAR}
  </update>

    <select id="queryCheckUserList" parameterType="org.fh.entity.common.CheckUserEntity" resultMap="BaseResultMap">
        select
        check_id,plant,doctor, health, welfare,CONCAT(health,'|',e.name) as healthName,CONCAT(welfare,'|',ee.name) as welfareName
        from biz_check_user cu
        left join biz_employee e on e.emp_id = cu.health
        left join biz_employee ee on ee.emp_id = cu.welfare
        <where>
            <if test="doctor !=null and doctor !=''">
                and doctor like '%${doctor}%'
            </if>
            <if test="health !=null and health !=''">
                and health like '%${health}%'
            </if>
            <if test="welfare !=null and welfare !=''">
                and welfare like '%${welfare}%'
            </if>
        </where>
    </select>

    <!-- 查询厂区下审批人员 -->
    <select id="queryCheckUserDetail" parameterType="string" resultMap="BaseResultMap">
        select check_id,plant,doctor, health, welfare,e.name as healthName,ee.name as welfareName
        from biz_check_user cu
        left join biz_employee e on e.emp_id = cu.health
        left join biz_employee ee on ee.emp_id = cu.welfare
        where cu.plant = #{plant}
    </select>
</mapper>