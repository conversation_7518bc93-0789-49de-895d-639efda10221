<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.fh.mapper.dsno1.common.ScheduleMapper">
    <resultMap id="BaseResultMap" type="org.fh.entity.common.ScheduleEntity">
        <id column="schedule_id" jdbcType="VARCHAR" property="scheduleId"/>
        <result column="emp_id" jdbcType="VARCHAR" property="empId"/>
        <result column="schedule_date" jdbcType="VARCHAR" property="scheduleDate"/>
        <result column="work_team" jdbcType="VARCHAR" property="workTeam"/>
        <result column="plant" jdbcType="VARCHAR" property="plant"/>
        <result column="create_by" jdbcType="VARCHAR" property="createBy"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_by" jdbcType="VARCHAR" property="updateBy"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>
    <sql id="Base_Column_List">
    schedule_id, emp_id, schedule_date, work_team, plant,workshop,work_type, create_by, create_time, update_by,
    update_time
  </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from biz_schedule
        where schedule_id = #{scheduleId,jdbcType=VARCHAR}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from biz_schedule
    where schedule_id = #{scheduleId,jdbcType=VARCHAR}
  </delete>
    <insert id="insert" parameterType="org.fh.entity.common.ScheduleEntity">
    insert into biz_schedule (schedule_id, emp_id, schedule_date, 
      work_team, plant, create_by, 
      create_time, update_by, update_time
      )
    values (#{scheduleId,jdbcType=VARCHAR}, #{empId,jdbcType=VARCHAR}, #{scheduleDate,jdbcType=VARCHAR}, 
      #{workTeam,jdbcType=VARCHAR}, #{plant,jdbcType=VARCHAR}, #{createBy,jdbcType=VARCHAR}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateBy,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP}
      )
  </insert>
    <insert id="insertSelective" parameterType="org.fh.entity.common.ScheduleEntity">
        insert into biz_schedule
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="scheduleId != null">
                schedule_id,
            </if>
            <if test="empId != null">
                emp_id,
            </if>
            <if test="scheduleDate != null">
                schedule_date,
            </if>
            <if test="workTeam != null">
                work_team,
            </if>
            <if test="plant != null">
                plant,
            </if>
            <if test="createBy != null">
                create_by,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateBy != null">
                update_by,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="scheduleId != null">
                #{scheduleId,jdbcType=VARCHAR},
            </if>
            <if test="empId != null">
                #{empId,jdbcType=VARCHAR},
            </if>
            <if test="scheduleDate != null">
                #{scheduleDate,jdbcType=VARCHAR},
            </if>
            <if test="workTeam != null">
                #{workTeam,jdbcType=VARCHAR},
            </if>
            <if test="plant != null">
                #{plant,jdbcType=VARCHAR},
            </if>
            <if test="createBy != null">
                #{createBy,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateBy != null">
                #{updateBy,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="org.fh.entity.common.ScheduleEntity">
        update biz_schedule
        <set>
            <if test="empId != null">
                emp_id = #{empId,jdbcType=VARCHAR},
            </if>
            <if test="scheduleDate != null">
                schedule_date = #{scheduleDate,jdbcType=VARCHAR},
            </if>
            <if test="workTeam != null">
                work_team = #{workTeam,jdbcType=VARCHAR},
            </if>
            <if test="plant != null">
                plant = #{plant,jdbcType=VARCHAR},
            </if>
            <if test="createBy != null">
                create_by = #{createBy,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateBy != null">
                update_by = #{updateBy,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where schedule_id = #{scheduleId,jdbcType=VARCHAR}
    </update>
    <update id="updateByPrimaryKey" parameterType="org.fh.entity.common.ScheduleEntity">
        update biz_schedule
        set emp_id = #{empId,jdbcType=VARCHAR},
          schedule_date = #{scheduleDate,jdbcType=VARCHAR},
          work_team = #{workTeam,jdbcType=VARCHAR},
          plant = #{plant,jdbcType=VARCHAR},
          create_by = #{createBy,jdbcType=VARCHAR},
          create_time = #{createTime,jdbcType=TIMESTAMP},
          update_by = #{updateBy,jdbcType=VARCHAR},
          update_time = #{updateTime,jdbcType=TIMESTAMP}
        where schedule_id = #{scheduleId,jdbcType=VARCHAR}
    </update>

    <select id="queryScheduleDateList" parameterType="org.fh.entity.common.ScheduleWorkTeamEntity" resultType="map">
        select schedule_date,work_type,emp_id from biz_schedule
        <where>
            <if test="plant !=null and plant !=''">
                and plant = #{plant}
            </if>
            <if test="workTeam !=null and workTeam !=''">
                and work_team = #{workTeam}
            </if>
            <if test="workshop !=null and workshop !=''">
                and workshop = #{workshop}
            </if>
            <if test="scheduleDate !=null and scheduleDate !=''">
                and schedule_date like '%${scheduleDate}%'
            </if>
            <if test="empId !=null and empId !=''">
                and emp_id = #{empId}
            </if>
        </where>
        order by schedule_date asc
    </select>

    <select id="queryScheduleDateDetail" resultType="map" parameterType="org.fh.entity.common.ScheduleWorkTeamEntity">
        select schedule_date,work_type from biz_schedule
        where plant = #{plant}
        and work_team = #{workTeam}
        and workshop = #{workshop}
        and schedule_date = #{scheduleDate}
        and emp_id = #{empId}
        limit 1
    </select>

    <insert id="addUserSchedule" parameterType="org.fh.entity.common.ScheduleEntity">
        insert into biz_schedule
        <trim prefix="(" suffix=")" suffixOverrides=",">
                schedule_id,
            <if test="empId != null and empId !=''">
                emp_id,
            </if>
            <if test="scheduleDate != null and scheduleDate !=''">
                schedule_date,
            </if>
            <if test="workTeam != null and workTeam !=''">
                work_team,
            </if>
            <if test="workshop != null and workshop !=''">
                workshop,
            </if>
            <if test="workType != null and workType !=''">
                work_type,
            </if>
            <if test="plant != null and plant !=''">
                plant,
            </if>
            <if test="createBy != null and createBy !=''">
                create_by,
            </if>
                create_time
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                #{scheduleId},
            <if test="empId != null and empId !=''">
                #{empId},
            </if>
            <if test="scheduleDate != null and scheduleDate !=''">
                #{scheduleDate},
            </if>
            <if test="workTeam != null and workTeam !=''">
                #{workTeam},
            </if>
            <if test="workshop != null and workshop !=''">
                #{workshop},
            </if>
            <if test="workType != null and workType !=''">
                #{workType},
            </if>
            <if test="plant != null and plant !=''">
                #{plant},
            </if>
            <if test="createBy != null and createBy !=''">
                #{createBy},
            </if>
            now()
        </trim>
    </insert>

    <!-- 删除员工的对应排班，后面是添加，事务同步操作 -->
    <delete id="deleteUserSchedule" parameterType="org.fh.entity.common.ScheduleEntity">
        delete from biz_schedule
        where emp_id = #{empId} and schedule_date = #{scheduleDate}
        <if test="plant !=null and plant !=''">
            and plant = #{plant}
        </if>
        <if test="workshop !=null and workshop !=''">
            and workshop = #{workshop}
        </if>
    </delete>

    <delete id="deleteScheduleByMonth" parameterType="org.fh.entity.common.ScheduleWorkTeamEntity">
        delete from biz_schedule
        <where>
            <if test="plant !=null and plant !=''">
                and plant = #{plant}
            </if>
            <if test="workshop !=null and workshop !=''">
                and workshop = #{workshop}
            </if>
            and schedule_date &gt;= #{startDate}
            and schedule_date &lt;= #{endDate}
            <if test="empIdList.size>0">
                and emp_id in
                (
                    <foreach collection="empIdList" item="item" separator=",">
                        #{item}
                    </foreach>
                )
            </if>
        </where>
    </delete>

    <!-- 批量删除员工详细排班 -->
    <delete id="deleteScheduleList" parameterType="collection">
        <foreach collection="list" separator=";" item="item">
            delete from biz_schedule
            where schedule_date = #{item.scheduleDate} and work_team = #{item.workTeam} and plant = #{item.plant} and workshop = #{item.workshop}
        </foreach>
    </delete>

    <select id="queryWorkTypeScheduleList" resultType="string">
        select e.open_id from biz_employee e
        left join biz_cost_center cc on cc.cost_center = e.cost_center
        left join biz_schedule_work_team swt on swt.work_team = e.work_team
        where swt.schedule_date = #{scheduleDate} and swt.work_type = #{workType} and e.open_id is not null
    </select>

    <!-- 查询当前日期被指定的排班人员及排班 -->
    <select id="queryWorkTypeSchedule" resultType="map">
        select e.open_id,s.work_type from biz_schedule s
        left join biz_employee e on s.emp_id = s.emp_id
        where s.schedule_date = #{scheduleDate}
    </select>

    <select id="queryScheduleNotEmployee" resultType="string">
        select e.open_id from biz_schedule s
        left join biz_employee e on s.emp_id = e.emp_id
        left join biz_cost_center cc on cc.cost_center = e.cost_center
        left join biz_schedule_work_team swt on swt.work_team = e.work_team
        where swt.schedule_date = #{scheduleDate}
        and swt.work_type = #{workType}
        and s.work_type != #{workType}
        and s.schedule_date = #{scheduleDate}
    </select>

    <!-- 查询当前日期被指定的排班人员及排班 -->
    <select id="queryEmployeeScheduleByDate" resultType="map">
        select emp_id as empId,s.work_type as workType,plant,workshop,work_team
        from biz_schedule s
        where s.schedule_date = #{scheduleDate}
    </select>

    <select id="queryEmployeePlanScheduleList" parameterType="org.fh.entity.common.ScheduleWorkTeamEntity" resultType="java.util.Map">
        select schedule_date,work_type from biz_schedule
        <where>
            <if test="workTeam !=null and workTeam !=''">
                and work_team = #{workTeam}
            </if>
            <if test="empId !=null and empId !=''">
                and emp_id = #{empId}
            </if>
            <if test="scheduleDate !=null and scheduleDate !=''">
                and schedule_date like '%${scheduleDate}%'
            </if>
            <if test="startDate !=null and startDate !=''">
                and schedule_date &gt;= #{startDate}
            </if>
            <if test="endDate !=null and endDate !=''">
                and schedule_date &lt;= #{endDate}
            </if>
        </where>
        order by schedule_date asc
    </select>

    <select id="queryEmployeePlanScheduleListAll" parameterType="org.fh.entity.common.ScheduleWorkTeamEntity" resultType="java.util.Map">
        select schedule_date,work_type,emp_id from biz_schedule
        <where>
            <if test="workTeam !=null and workTeam !=''">
                and work_team = #{workTeam}
            </if>
            <if test="scheduleDate !=null and scheduleDate !=''">
                and schedule_date like '%${scheduleDate}%'
            </if>
            <if test="startDate !=null and startDate !=''">
                and schedule_date &gt;= #{startDate}
            </if>
            <if test="endDate !=null and endDate !=''">
                and schedule_date &lt;= #{endDate}
            </if>
        </where>
        order by schedule_date asc
    </select>

    <!-- 批量添加员工排班信息 -->
    <insert id="addScheduleList" parameterType="collection">
        insert into biz_schedule(schedule_id, emp_id, schedule_date, work_team, plant,workshop,work_type, create_by, create_time)
        values
        <foreach collection="list" item="item" separator=",">
            (
            #{item.scheduleId},#{item.empId},#{item.scheduleDate},#{item.workTeam},#{item.plant},#{item.workshop},#{item.workType},#{item.createBy},now()
            )
        </foreach>
    </insert>

</mapper>