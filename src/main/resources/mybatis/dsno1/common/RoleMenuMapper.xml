<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.fh.mapper.dsno1.common.RoleMenuMapper">
  <resultMap id="BaseResultMap" type="org.fh.vo.common.RoleMenuVo">
    <result column="role_number" jdbcType="VARCHAR" property="roleNumber" />
    <result column="menu_id" jdbcType="VARCHAR" property="menuId" />
    <result column="url" jdbcType="VARCHAR" property="url" />
    <result column="path" jdbcType="VARCHAR" property="path" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="limits" jdbcType="INTEGER" property="limits" />
    <result column="pid" jdbcType="VARCHAR" property="pid" />
    <result column="icon" jdbcType="VARCHAR" property="icon" />
  </resultMap>
  <sql id="Base_Column_List">
    role_menu_id, role_number, menu_id,limits, create_by, create_time, update_by, update_time,
    deleted
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from biz_role_menu
    where role_menu_id = #{roleMenuId,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from biz_role_menu
    where role_menu_id = #{roleMenuId,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="org.fh.entity.common.RoleMenuEntity">
    insert into biz_role_menu (role_menu_id, role_number, menu_id,limits,
      create_by, create_time, update_by, 
      update_time, deleted)
    values (#{roleMenuId,jdbcType=VARCHAR}, #{roleNumber,jdbcType=VARCHAR}, #{menuId,jdbcType=VARCHAR}, 
      #{limits},#{createBy,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{updateBy,jdbcType=VARCHAR},
      #{updateTime,jdbcType=TIMESTAMP}, #{deleted,jdbcType=BIT})
  </insert>
  <insert id="insertSelective" parameterType="org.fh.entity.common.RoleMenuEntity">
    insert into biz_role_menu
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="roleMenuId != null">
        role_menu_id,
      </if>
      <if test="roleNumber != null">
        role_number,
      </if>
      <if test="menuId != null">
        menu_id,
      </if>
      <if test="limits != null">
        limits,
      </if>
      <if test="createBy != null">
        create_by,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateBy != null">
        update_by,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="roleMenuId != null">
        #{roleMenuId,jdbcType=VARCHAR},
      </if>
      <if test="roleNumber != null">
        #{roleNumber,jdbcType=VARCHAR},
      </if>
      <if test="menuId != null">
        #{menuId,jdbcType=VARCHAR},
      </if>
      <if test="limits != null">
        #{limits,jdbcType=INTEGER},
      </if>
      <if test="createBy != null">
        #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        #{updateBy,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=BIT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="org.fh.entity.common.RoleMenuEntity">
    update biz_role_menu
    <set>
      <if test="roleNumber != null">
        role_number = #{roleNumber,jdbcType=VARCHAR},
      </if>
      <if test="menuId != null">
        menu_id = #{menuId,jdbcType=VARCHAR},
      </if>
      <if test="limits != null">
        limits = #{limits,jdbcType=INTEGER},
      </if>
      <if test="createBy != null">
        create_by = #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        update_by = #{updateBy,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=BIT},
      </if>
    </set>
    where role_menu_id = #{roleMenuId,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="org.fh.entity.common.RoleMenuEntity">
    update biz_role_menu
    set role_number = #{roleNumber,jdbcType=VARCHAR},
      menu_id = #{menuId,jdbcType=VARCHAR},
      create_by = #{createBy,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_by = #{updateBy,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      deleted = #{deleted,jdbcType=BIT}
    where role_menu_id = #{roleMenuId,jdbcType=VARCHAR}
  </update>

  <!-- 批量添加角色与菜单关系 -->
  <insert id="addRoleMenuList" parameterType="arraylist">
    insert into
    biz_role_menu(role_menu_id,role_number,menu_id,limits,create_by,create_time)
      values
      <foreach collection="list" separator="," item="item">
        (#{item.roleMenuId},#{item.roleNumber},#{item.menuId},#{item.limits},#{item.createBy},now())
      </foreach>
  </insert>

  <!-- 根据角色编号删除角色与菜单绑定关系 -->
  <delete id="deleteRoleMenuByRole" parameterType="string">
    delete from biz_role_menu where role_number = #{roleNumber}
  </delete>

  <!-- 根据角色编号查询角色与菜单绑定列表 -->
  <select id="queryRoleMenuByRoleNumber" resultMap="BaseResultMap" parameterType="string">
    select rm.role_menu_id,rm.role_number,rm.menu_id,rm.limits,m.url,m.path,m.`name`,m.pid,m.icon
    from biz_role_menu rm
    left join biz_menu m on rm.menu_id = m.menu_id
    where role_number = #{roleNumber}
    order by m.`order` asc
  </select>
</mapper>