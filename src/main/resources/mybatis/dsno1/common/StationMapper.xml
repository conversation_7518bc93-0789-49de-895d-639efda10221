<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.fh.mapper.dsno1.common.StationMapper">
    <resultMap id="BaseResultMap" type="org.fh.vo.common.StationVo">
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="lineName" jdbcType="VARCHAR" property="lineName"/>
        <result column="createName" jdbcType="VARCHAR" property="createName"/>
        <result column="plant" jdbcType="VARCHAR" property="plant"/>
        <result column="line_id" jdbcType="VARCHAR" property="lineId"/>
        <result column="created_by" jdbcType="VARCHAR" property="createdBy"/>
        <result column="created_time" jdbcType="TIMESTAMP" property="createdTime"/>
        <result column="update_by" jdbcType="VARCHAR" property="updateBy"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>
    <sql id="Base_Column_List">
    id, name, line_id, deleted, created_by, created_time, update_by, update_time
  </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from biz_station
        where id = #{id,jdbcType=VARCHAR}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from biz_station
    where id = #{id,jdbcType=VARCHAR}
  </delete>
    <insert id="insert" parameterType="org.fh.entity.common.StationEntity">
    insert into biz_station (id, name, line_id,
      deleted, created_by, created_time, 
      update_by, update_time)
    values (#{id,jdbcType=VARCHAR}, #{name,jdbcType=VARCHAR}, #{lineId,jdbcType=VARCHAR},
      #{deleted,jdbcType=BIT}, #{createdBy,jdbcType=VARCHAR}, #{createdTime,jdbcType=TIMESTAMP}, 
      #{updateBy,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP})
  </insert>
    <insert id="insertSelective" parameterType="org.fh.entity.common.StationEntity">
        insert into biz_station
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="name != null">
                name,
            </if>
            <if test="lineId != null">
                line_id,
            </if>
            <if test="deleted != null">
                deleted,
            </if>
            <if test="createdBy != null">
                created_by,
            </if>
            <if test="createdTime != null">
                created_time,
            </if>
            <if test="updateBy != null">
                update_by,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=VARCHAR},
            </if>
            <if test="name != null">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="lineId != null">
                #{lineId,jdbcType=VARCHAR},
            </if>
            <if test="deleted != null">
                #{deleted,jdbcType=BIT},
            </if>
            <if test="createdBy != null">
                #{createdBy,jdbcType=VARCHAR},
            </if>
            <if test="createdTime != null">
                #{createdTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateBy != null">
                #{updateBy,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="org.fh.entity.common.StationEntity">
        update biz_station
        <set>
            <if test="name != null">
                name = #{name,jdbcType=VARCHAR},
            </if>
            <if test="lineId != null">
              line_id = #{lineId,jdbcType=VARCHAR},
            </if>
            <if test="deleted != null">
                deleted = #{deleted,jdbcType=BIT},
            </if>
            <if test="createdBy != null">
                created_by = #{createdBy,jdbcType=VARCHAR},
            </if>
            <if test="createdTime != null">
                created_time = #{createdTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateBy != null">
                update_by = #{updateBy,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=VARCHAR}
    </update>
    <update id="updateByPrimaryKey" parameterType="org.fh.entity.common.StationEntity">
    update biz_station
    set name = #{name,jdbcType=VARCHAR},
      line_id = #{lineId,jdbcType=VARCHAR},
      deleted = #{deleted,jdbcType=BIT},
      created_by = #{createdBy,jdbcType=VARCHAR},
      created_time = #{createdTime,jdbcType=TIMESTAMP},
      update_by = #{updateBy,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=VARCHAR}
  </update>

  <!-- 添加线路管理信息 -->
  <insert id="addStation" parameterType="org.fh.entity.common.StationEntity">
    insert into biz_station
    <trim prefix="(" suffix=")" suffixOverrides=",">
      id,
      <if test="name != null and name !=''">
        name,
      </if>
      <if test="lineId != null and lineId !=''">
        line_id,
      </if>
      deleted,
      <if test="createdBy != null and createdBy !=''">
        created_by,
      </if>
      created_time
    </trim>
    <trim prefix="values(" suffix=")" suffixOverrides=",">
      #{id,jdbcType=VARCHAR},
      <if test="name != null and name !=''">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="lineId != null and lineId !=''">
        #{lineId,jdbcType=VARCHAR},
      </if>
      0,
      <if test="createdBy != null and createdBy !=''">
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null and createdTime !=''">
        #{createdTime,jdbcType=VARCHAR},
      </if>
      now(),
    </trim>
  </insert>
  <!-- 删除线路管理信息 -->
  <update id="deleteStationData" parameterType="String">
    update biz_station set deleted='1' where id = #{id}
  </update>

  <!-- 修改线路管理信息 -->
  <update id="updateStation" parameterType="org.fh.entity.common.StationEntity">
    update biz_station
    <set>
      <if test="name != null and name !=''">
        name = #{name},
      </if>
      <if test="lineId != null and lineId !=''">
        line_id = #{lineId},
      </if>
      <if test="updateBy != null and updateBy !=''">
        update_by = #{updateBy},
      </if>
      update_time = now()
    </set>
    where id = #{id}
  </update>

  <!-- 根据Id查询线路管理信息 -->
  <select id="queryStationDetail" parameterType="String" resultMap="BaseResultMap">
    select s.name,e.name as createName,l.plant,l.name as lineName,s.line_id,s.created_by,s.created_time,s.update_by,s.update_time
    from biz_station s
    left join biz_line l on s.line_id = l.id
    left join biz_employee e on s.created_by = e.emp_id
    where s.id = #{id}
  </select>

  <!-- 分页查询线路管理路线 -->
  <select id="queryStationPage" resultMap="BaseResultMap" parameterType="org.fh.entity.common.StationEntity">
    select s.NAME,s.id,e.name as createName,l.plant,l.NAME as lineName,s.line_id,s.created_by,s.created_time,s.update_by,s.update_time
    from biz_station s
    left join biz_line l on s.line_id = l.id
    left join biz_employee e on s.created_by = e.emp_id
    <where>
        s.deleted = '0'
      <if test="name != null and name !=''">
        and s.name like '%${name}%'
      </if>
      <if test="createName != null and createName !=''">
        and e.create_name like '%${createName}%'
      </if>
      <if test="plant != null and plant !=''">
        and l.plant like '%${plant}%'
      </if>
      <if test="lineName != null and lineName !=''">
        and l.line_name like '%${lineName}%'
      </if>
      <if test="lineId != null and lineId !=''">
        and s.line_id = #{lineId}
      </if>
    </where>
    order by s.created_time desc
  </select>
</mapper>