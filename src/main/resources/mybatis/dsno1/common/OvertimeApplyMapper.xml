<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.fh.mapper.dsno1.common.OvertimeApplyMapper">
    <resultMap id="BaseResultMap" type="org.fh.vo.common.OvertimeApplyVo">
        <id column="overtime_apply_id" jdbcType="VARCHAR" property="overtimeApplyId"/>
        <result column="plant" jdbcType="VARCHAR" property="plant"/>
        <result column="workshop" jdbcType="VARCHAR" property="workshop"/>
        <result column="vsm" jdbcType="VARCHAR" property="vsm"/>
        <result column="production_line" jdbcType="VARCHAR" property="productionLine"/>
        <result column="reason" jdbcType="VARCHAR" property="reason"/>
        <result column="type_id" jdbcType="VARCHAR" property="typeId"/>
        <result column="proposer" jdbcType="VARCHAR" property="proposer"/>
        <result column="proposerName" jdbcType="VARCHAR" property="proposerName"/>
        <result column="operator" jdbcType="VARCHAR" property="operator"/>
        <result column="emp_id" jdbcType="VARCHAR" property="empId"/>
        <result column="work_type" jdbcType="VARCHAR" property="workType"/>
        <result column="work_team" jdbcType="VARCHAR" property="workTeam"/>
        <result column="start_time" jdbcType="TIMESTAMP" property="startTime"/>
        <result column="end_time" jdbcType="TIMESTAMP" property="endTime"/>
        <result column="department" jdbcType="TIMESTAMP" property="department"/>
        <result column="overtime_type_name" jdbcType="TIMESTAMP" property="overtimeTypeName"/>
        <result column="multiple" jdbcType="TIMESTAMP" property="multiple"/>
        <result column="applyStatusName" jdbcType="TIMESTAMP" property="applyStatusName"/>
        <result column="processinstanceid" jdbcType="VARCHAR" property="processinstanceid"/>
        <result column="status" jdbcType="INTEGER" property="status"/>
        <result column="statusName" jdbcType="INTEGER" property="statusName"/>
        <result column="apply_date" jdbcType="TIMESTAMP" property="applyDate"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="finish_time" jdbcType="VARCHAR" property="finishTime"/>
        <result column="overtime_date" jdbcType="VARCHAR" property="overtimeDate"/>
    </resultMap>
    <sql id="Base_Column_List">
    overtime_apply_id, plant, workshop, production_line, reason, type_id, proposer, operator, 
    start_time, end_time, processinstanceid, status, apply_date, remark
  </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from biz_overtime_apply
        where overtime_apply_id = #{overtimeApplyId,jdbcType=VARCHAR}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from biz_overtime_apply
    where overtime_apply_id = #{overtimeApplyId,jdbcType=VARCHAR}
  </delete>
    <insert id="insert" parameterType="org.fh.entity.common.OvertimeApplyEntity">
    insert into biz_overtime_apply (overtime_apply_id, plant, workshop, 
      production_line, reason, type_id, 
      proposer, operator, start_time, 
      end_time, processinstanceid, status, 
      apply_date, remark)
    values (#{overtimeApplyId,jdbcType=VARCHAR}, #{plant,jdbcType=VARCHAR}, #{workshop,jdbcType=VARCHAR}, 
      #{productionLine,jdbcType=VARCHAR}, #{reason,jdbcType=VARCHAR}, #{typeId,jdbcType=VARCHAR}, 
      #{proposer,jdbcType=VARCHAR}, #{operator,jdbcType=VARCHAR}, #{startTime,jdbcType=TIMESTAMP}, 
      #{endTime,jdbcType=TIMESTAMP}, #{processinstanceid,jdbcType=VARCHAR}, #{status,jdbcType=INTEGER}, 
      #{applyDate,jdbcType=TIMESTAMP}, #{remark,jdbcType=VARCHAR})
  </insert>
    <insert id="insertSelective" parameterType="org.fh.entity.common.OvertimeApplyEntity">
        insert into biz_overtime_apply
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="overtimeApplyId != null">
                overtime_apply_id,
            </if>
            <if test="plant != null">
                plant,
            </if>
            <if test="workshop != null">
                workshop,
            </if>
            <if test="productionLine != null">
                production_line,
            </if>
            <if test="reason != null">
                reason,
            </if>
            <if test="typeId != null">
                type_id,
            </if>
            <if test="proposer != null">
                proposer,
            </if>
            <if test="operator != null">
                operator,
            </if>
            <if test="startTime != null">
                start_time,
            </if>
            <if test="endTime != null">
                end_time,
            </if>
            <if test="timeCount != null">
                time_count,
            </if>
            <if test="processinstanceid != null">
                processinstanceid,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="applyDate != null">
                apply_date,
            </if>
            <if test="remark != null">
                remark,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="overtimeApplyId != null">
                #{overtimeApplyId,jdbcType=VARCHAR},
            </if>
            <if test="plant != null">
                #{plant,jdbcType=VARCHAR},
            </if>
            <if test="workshop != null">
                #{workshop,jdbcType=VARCHAR},
            </if>
            <if test="productionLine != null">
                #{productionLine,jdbcType=VARCHAR},
            </if>
            <if test="reason != null">
                #{reason,jdbcType=VARCHAR},
            </if>
            <if test="typeId != null">
                #{typeId,jdbcType=VARCHAR},
            </if>
            <if test="proposer != null">
                #{proposer,jdbcType=VARCHAR},
            </if>
            <if test="operator != null">
                #{operator,jdbcType=VARCHAR},
            </if>
            <if test="startTime != null">
                #{startTime,jdbcType=TIMESTAMP},
            </if>
            <if test="endTime != null">
                #{endTime,jdbcType=TIMESTAMP},
            </if>
            <if test="timeCount != null">
                #{timeCount},
            </if>
            <if test="processinstanceid != null">
                #{processinstanceid,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                #{status,jdbcType=INTEGER},
            </if>
            <if test="applyDate != null">
                #{applyDate,jdbcType=TIMESTAMP},
            </if>
            <if test="remark != null">
                #{remark,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="org.fh.entity.common.OvertimeApplyEntity">
        update biz_overtime_apply
        <set>
            <if test="plant != null">
                plant = #{plant,jdbcType=VARCHAR},
            </if>
            <if test="workshop != null">
                workshop = #{workshop,jdbcType=VARCHAR},
            </if>
            <if test="productionLine != null">
                production_line = #{productionLine,jdbcType=VARCHAR},
            </if>
            <if test="reason != null">
                reason = #{reason,jdbcType=VARCHAR},
            </if>
            <if test="typeId != null">
                type_id = #{typeId,jdbcType=VARCHAR},
            </if>
            <if test="proposer != null">
                proposer = #{proposer,jdbcType=VARCHAR},
            </if>
            <if test="operator != null">
                operator = #{operator,jdbcType=VARCHAR},
            </if>
            <if test="startTime != null">
                start_time = #{startTime,jdbcType=TIMESTAMP},
            </if>
            <if test="endTime != null">
                end_time = #{endTime,jdbcType=TIMESTAMP},
            </if>
            <if test="processinstanceid != null">
                processinstanceid = #{processinstanceid,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                status = #{status,jdbcType=INTEGER},
            </if>
            <if test="applyDate != null">
                apply_date = #{applyDate,jdbcType=TIMESTAMP},
            </if>
            <if test="remark != null">
                remark = #{remark,jdbcType=VARCHAR},
            </if>
        </set>
        where overtime_apply_id = #{overtimeApplyId,jdbcType=VARCHAR}
    </update>
    <update id="updateByPrimaryKey" parameterType="org.fh.entity.common.OvertimeApplyEntity">
        update biz_overtime_apply
        set plant = #{plant,jdbcType=VARCHAR},
          workshop = #{workshop,jdbcType=VARCHAR},
          production_line = #{productionLine,jdbcType=VARCHAR},
          reason = #{reason,jdbcType=VARCHAR},
          type_id = #{typeId,jdbcType=VARCHAR},
          proposer = #{proposer,jdbcType=VARCHAR},
          operator = #{operator,jdbcType=VARCHAR},
          start_time = #{startTime,jdbcType=TIMESTAMP},
          end_time = #{endTime,jdbcType=TIMESTAMP},
          processinstanceid = #{processinstanceid,jdbcType=VARCHAR},
          status = #{status,jdbcType=INTEGER},
          apply_date = #{applyDate,jdbcType=TIMESTAMP},
          remark = #{remark,jdbcType=VARCHAR}
        where overtime_apply_id = #{overtimeApplyId,jdbcType=VARCHAR}
    </update>

    <!-- 批量添加加班申请记录 -->
    <insert id="addOvertimeApplyList" parameterType="collection">
        insert into biz_overtime_apply (overtime_apply_id, plant, workshop,vsm,department,
        production_line, reason, type_id,
        proposer, operator,overtime_date, start_time,time_count,
        end_time, processinstanceid, status,timeout_count,timeout_time,
        apply_date)
        values
        <foreach collection="list" separator="," item="item">
            (
            #{item.overtimeApplyId}, #{item.plant}, #{item.workshop},#{item.vsm},#{item.department},
            #{item.productionLine}, #{item.reason}, #{item.typeId},
            #{item.proposer}, #{item.operator}, #{item.overtimeDate}, #{item.startTime},#{item.timeCount},
            #{item.endTime}, #{item.processinstanceid}, #{item.status},#{item.timeoutCount},
            #{item.timeoutTime},
            now()
            )
        </foreach>
    </insert>

    <!-- 根据流程实例Id查询加班申请记录 -->
    <select id="queryOvertimeByProcess" parameterType="string" resultMap="BaseResultMap">
        select overtime_apply_id, plant, workshop,department, production_line, reason, type_id, proposer, operator,overtime_type_name,multiple,
        start_time, end_time, processinstanceid, status, apply_date,remark
        from biz_overtime_apply oa
        left join biz_overtime_type ot on ot.overtime_type_id = oa.type_id
        where processinstanceid = #{processInstanceId} limit 1
    </select>

    <!-- 查询加班申请次数 -->
    <select id="queryApplyOvertimeCount" parameterType="string" resultType="int">
        select count(*) from biz_overtime_apply where proposer = #{empId}
    </select>

    <!-- 查询加班申请记录 -->
    <select id="queryOvertimePage" resultMap="BaseResultMap" parameterType="org.fh.entity.common.OvertimeApplyEntity">
      select
      oa.overtime_apply_id,oa.plant,oa.workshop,oa.department,oa.production_line,oa.reason,oa.type_id,oa.proposer,oa.operator,ot.overtime_type_name,ot.multiple,
      oa.start_time,oa.end_time,oa.processinstanceid,oa.status,oa.apply_date,oa.remark,e.name as
      proposerName,
      case
      when oa.status = '1' then '待确认'
      when oa.status = '2' then '待提交'
      when oa.status = '3' then '待审批'
      when oa.status = '4' then '审批通过'
      when oa.status = '5' then '撤回'
      when oa.status = '6' then '驳回'
      when oa.status = '9' then '已撤销'
      end as statusName
      from biz_overtime_apply oa
      left join biz_overtime_type ot on ot.overtime_type_id = oa.type_id
      left join biz_employee e on oa.proposer = e.emp_id
      <where>
        <if test="processInstanceIds !=null and processInstanceIds.size > 0">
          and oa.processinstanceid in
          (
          <foreach collection="processInstanceIds" item="item" separator=",">
            #{item}
          </foreach>
          )
        </if>
        <if test="proposer !=null and proposer !=''">
          and proposer = #{proposer}
        </if>
        and oa.status != 5
      </where>
      order by oa.apply_date asc,oa.`status` asc
    </select>


    <select id="queryOvertimePageDesc" resultMap="BaseResultMap" parameterType="org.fh.entity.common.OvertimeApplyEntity">
        select
        oa.overtime_apply_id,oa.plant,oa.workshop,oa.department,oa.production_line,oa.reason,oa.type_id,oa.proposer,oa.operator,ot.overtime_type_name,ot.multiple,
        oa.start_time,oa.end_time,oa.processinstanceid,oa.status,oa.apply_date,oa.remark,e.name as proposerName,
        case
        when oa.status = '1' then '待确认'
        when oa.status = '2' then '待提交'
        when oa.status = '3' then '待审批'
        when oa.status = '4' then '审批通过'
        when oa.status = '5' then '撤回'
        when oa.status = '6' then '驳回'
        when oa.status = '9' then '已撤销'
        end as statusName
        from biz_overtime_apply oa
        left join biz_overtime_type ot on ot.overtime_type_id = oa.type_id
        left join biz_employee e on oa.proposer = e.emp_id
        <where>
            <if test="processInstanceIds !=null and processInstanceIds.size > 0">
                and oa.processinstanceid in
                (
                <foreach collection="processInstanceIds" item="item" separator=",">
                    #{item}
                </foreach>
                )
            </if>
            <if test="proposer !=null and proposer !=''">
                and proposer = #{proposer}
            </if>
        </where>
        order by oa.apply_date desc,oa.`status` asc
    </select>

    <!-- 更改加班申请状态 -->
    <update id="updateOvertimeApply" parameterType="org.fh.entity.common.OvertimeApplyEntity">
        update biz_overtime_apply
        <set>
            status = #{status},
            <if test="remark !=null and remark !=''">
                remark = #{remark},
            </if>
            <if test="updateBy !=null and updateBy !=''">
                update_by = #{updateBy},
            </if>
            <if test="status == 4">
                finish_time = now(),
            </if>
            update_time = now()
        </set>
        where processinstanceid = #{processinstanceid}
    </update>

    <select id="queryOvertimeApplyPage" parameterType="org.fh.entity.common.OvertimeApplyEntity" resultMap="BaseResultMap">
        select e.name as operator,o.emp_id,oa.plant,oa.workshop,oa.vsm,oa.production_line,oa.department,e.work_team,
        o.work_type,oa.start_time,oa.end_time,oa.finish_time,oa.processinstanceid,oa.overtime_date
        from biz_overtime o
        left join biz_overtime_apply oa on o.processinstanceid = oa.processinstanceid
        left join biz_employee e on e.emp_id = o.emp_id
        <where>
            oa.status = '4' and o.`status` = '2'
            <if test="plant !=null and plant !=''">
                and oa.plant = #{plant}
            </if>
            <if test="name !=null and name !=''">
                and e.name like '%${name}%'
            </if>
            <if test="empId !=null and empId !=''">
                and o.emp_id = #{empId}
            </if>
            <if test="workTeam !=null and workTeam !=''">
                and e.work_team like '%${workTeam}%'
            </if>
            <if test="workshop !=null and workshop !=''">
                and oa.workshop like '%${workshop}%'
            </if>
            <if test="vsm !=null and vsm !=''">
                and oa.vsm like '%${vsm}%'
            </if>
            <if test="productionLine !=null and productionLine !=''">
                and oa.production_line like '%${productionLine}%'
            </if>
            <if test="department !=null and department !=''">
                and oa.department like '%${department}%'
            </if>
            <if test="workType !=null and workType !=''">
                and o.work_type like '%${workType}%'
            </if>
            <if test="startDate !=null and startDate !=''">
                and oa.start_time &gt;= #{startDate}
            </if>
            <if test="endDate !=null and endDate !=''">
                and oa.end_time &lt;= #{endDate}
            </if>
            <if test="finishStartDate !=null and finishStartDate !=''">
                and oa.finish_time &gt;= #{finishStartDate}
            </if>
            <if test="finishEndDate !=null and finishEndDate !=''">
                and oa.finish_time &lt;= #{finishEndDate}
            </if>
        </where>
        order by oa.apply_date desc,e.name asc
    </select>

    <select id="exportOvertime" resultType="org.fh.model.exportModel.OvertimeExportModel" parameterType="org.fh.entity.common.OvertimeApplyEntity">
        select e.name,e.name as operator,o.emp_id as empId,oa.plant,oa.workshop,oa.production_line as productionLine,
        oa.time_count as timeCount,oa.finish_time as finishTime,
        e.work_team as workTeam,o.work_type as workType,oa.start_time as startTime,oa.end_time as endTime,oa.reason,o.remark
        from biz_overtime o
        left join biz_overtime_apply oa on o.processinstanceid = oa.processinstanceid
        left join biz_employee e on e.emp_id = o.emp_id
        <where>
            oa.status = '4' and o.`status` = '2'
            <if test="plant !=null and plant !=''">
                and oa.plant = #{plant}
            </if>
            <if test="name !=null and name !=''">
                and e.name like '%${name}%'
            </if>
            <if test="empId !=null and empId !=''">
                and o.emp_id = #{empId}
            </if>
            <if test="workshop !=null and workshop !=''">
                and oa.workshop like '%${workshop}%'
            </if>
            <if test="productionLine !=null and productionLine !=''">
                and oa.production_line like '%${productionLine}%'
            </if>
            <if test="department !=null and department !=''">
                and oa.department like '%${department}%'
            </if>
            <if test="startDate !=null and startDate !=''">
                and oa.start_time &gt;= #{startDate}
            </if>
            <if test="endDate !=null and endDate !=''">
                and oa.end_time &lt;= #{endDate}
            </if>
            <if test="finishStartDate !=null and finishStartDate !=''">
                and oa.finish_time &gt;= #{finishStartDate}
            </if>
            <if test="finishEndDate !=null and finishEndDate !=''">
                and oa.finish_time &lt;= #{finishEndDate}
            </if>
        </where>
        order by oa.apply_date desc,e.name asc
    </select>

    <select id="exportOvertimeForKayang" resultType="org.fh.model.exportModel.OvertimeKayangExportModel" parameterType="org.fh.entity.common.OvertimeApplyEntity">
        SELECT
        o.emp_id AS empId,
        oa.time_count AS timeCount,
        o.work_type AS workType,
        oa.start_time AS startTime,
        oa.end_time AS endTime,
        oa.overtime_date AS overtimeDate,
        oa.reason,
        o.processinstanceid AS pid,
        oa.operator,
        CASE
        oa.`status`
        WHEN '4' THEN
        0 ELSE 1
        END AS action
        FROM
        biz_overtime o
        LEFT JOIN biz_overtime_apply oa ON o.processinstanceid = oa.processinstanceid
        LEFT JOIN biz_work_type wt on o.work_type = wt.name
        <where>
            (oa.status = '4' or oa.status = '9') and o.`status` = '2'
            <if test="finishStartDate !=null and finishStartDate !=''">
                and oa.start_time &gt;= #{finishStartDate}
            </if>
            <if test="finishEndDate !=null and finishEndDate !=''">
                and oa.start_time &lt;= #{finishEndDate}
            </if>
        </where>
        order by oa.apply_date desc,o.emp_id asc
    </select>

    <select id="queryOvertimeEmployeePage" resultMap="BaseResultMap">
        select o.status,o.emp_id,o.remark,oa.plant,oa.workshop,oa.department,oa.production_line,o.update_time,o.processinstanceid,
        oa.apply_date,e.name as overtimeUser,oa.reason,oa.start_time,oa.end_time,
        case
        when oa.status = '1' then '待确认'
        when oa.status = '2' then '待提交'
        when oa.status = '3' then '待审批'
        when oa.status = '4' then '审批通过'
        when oa.status = '5' then '撤回'
        when oa.status = '6' then '驳回'
        when oa.status = '9' then '已撤销'
        end as statusName
        from biz_overtime o
        left join biz_employee e on e.emp_id = o.emp_id
        left join biz_overtime_apply oa on oa.processinstanceid = o.processinstanceid
        <where>
            <if test="processinstanceid !=null and processinstanceid !=''">
                and processinstanceid = #{processinstanceid}
            </if>
            <if test="empId !=null and empId !=''">
                and o.emp_id = #{empId}
            </if>
            <if test="status !=null">
                and o.status = #{status}
            </if>
            and oa.status != 5
        </where>
        order by oa.apply_date desc
    </select>

    <select id="queryTimeoutOvertime" resultType="string">
        select distinct oa.processinstanceid
        from biz_overtime_apply oa
        left join biz_overtime o on o.processinstanceid = oa.processinstanceid
        where timeout_time 	&lt; #{date}
        and oa.status = '1'

    </select>

    <update id="updateOvertimeStatus" parameterType="collection">
        update biz_overtime_apply
        set status = 5
        where processinstanceid in
        (
        <foreach collection="list" item="item" separator=",">
            #{item}
        </foreach>
        )
    </update>

    <!-- 查询我的加班列表 -->
    <select id="queryOvertimeValidList" resultMap="BaseResultMap">
        select o.status,o.emp_id,o.remark,oa.department,oa.plant,oa.workshop,oa.production_line,o.update_time,o.processinstanceid,
        oa.apply_date,e.name as overtimeUser,oa.reason,oa.start_time,oa.end_time,
        case
        when oa.status = '1' then '待确认'
        when oa.status = '2' then '待提交'
        when oa.status = '3' then '待审批'
        when oa.status = '4' then '审批通过'
        when oa.status = '5' then '撤回'
        when oa.status = '6' then '驳回'
        when oa.status = '9' then '已撤销'
        end as statusName
        from biz_overtime o
        left join biz_employee e on e.emp_id = o.emp_id
        left join biz_overtime_apply oa on oa.processinstanceid = o.processinstanceid
        <where>
            <if test="empId !=null and empId !=''">
                and o.emp_id = #{empId}
            </if>
            <if test="status !=null">
                and o.status != #{status}
            </if>
        </where>
        order by oa.apply_date desc
    </select>

</mapper>