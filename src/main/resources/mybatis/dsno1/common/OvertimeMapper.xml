<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.fh.mapper.dsno1.common.OvertimeMapper">
    <resultMap id="BaseResultMap" type="org.fh.vo.common.OvertimeVo">
        <id column="overtime_id" jdbcType="VARCHAR" property="overtimeId"/>
        <result column="status" jdbcType="INTEGER" property="status"/>
        <result column="statusName" jdbcType="VARCHAR" property="statusName"/>
        <result column="emp_id" jdbcType="VARCHAR" property="empId"/>
        <result column="work_type" jdbcType="VARCHAR" property="workType"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="overtimeUser" jdbcType="VARCHAR" property="overtimeUser"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>
    <sql id="Base_Column_List">
    overtime_id, status, emp_id, remark, update_time
  </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from biz_overtime
        where overtime_id = #{overtimeId,jdbcType=VARCHAR}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from biz_overtime
    where overtime_id = #{overtimeId,jdbcType=VARCHAR}
  </delete>
    <insert id="insert" parameterType="org.fh.entity.common.OvertimeEntity">
    insert into biz_overtime (overtime_id, status, emp_id, 
      remark, update_time)
    values (#{overtimeId,jdbcType=VARCHAR}, #{status,jdbcType=INTEGER}, #{empId,jdbcType=VARCHAR}, 
      #{remark,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP})
  </insert>
    <insert id="insertSelective" parameterType="org.fh.entity.common.OvertimeEntity">
        insert into biz_overtime
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="overtimeId != null">
                overtime_id,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="empId != null">
                emp_id,
            </if>
            <if test="remark != null">
                remark,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="overtimeId != null">
                #{overtimeId,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                #{status,jdbcType=INTEGER},
            </if>
            <if test="empId != null">
                #{empId,jdbcType=VARCHAR},
            </if>
            <if test="remark != null">
                #{remark,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="org.fh.entity.common.OvertimeEntity">
        update biz_overtime
        <set>
            <if test="status != null">
                status = #{status,jdbcType=INTEGER},
            </if>
            <if test="empId != null">
                emp_id = #{empId,jdbcType=VARCHAR},
            </if>
            <if test="remark != null">
                remark = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where overtime_id = #{overtimeId,jdbcType=VARCHAR}
    </update>
    <update id="updateByPrimaryKey" parameterType="org.fh.entity.common.OvertimeEntity">
        update biz_overtime
        set status = #{status,jdbcType=INTEGER},
          emp_id = #{empId,jdbcType=VARCHAR},
          remark = #{remark,jdbcType=VARCHAR},
          update_time = #{updateTime,jdbcType=TIMESTAMP}
        where overtime_id = #{overtimeId,jdbcType=VARCHAR}
      </update>

    <!-- 批量添加员工加班记录 -->
    <insert id="addOvertime" parameterType="collection">
        insert into biz_overtime (overtime_id, status,emp_id,work_type,processinstanceid,time_count)
        values
        <foreach collection="list" item="item" separator=",">
            (
            #{item.overtimeId}, #{item.status}, #{item.empId},#{item.workType},#{item.processinstanceid},#{item.timeCount}
            )
        </foreach>
    </insert>

    <!-- 更改员工加班记录状态 -->
    <update id="updateOvertimeType" parameterType="org.fh.entity.common.OvertimeEntity">
        update biz_overtime
        <set>
            status = #{status},
            <if test="remark !=null and remark !=''">
                remark = #{remark},
            </if>
            update_time = now()
        </set>
        <where>
            <if test="empId !=null and empId !=''">
                and emp_id = #{empId}
            </if>
            <if test="processinstanceid !=null and processinstanceid !=''">
                and processinstanceid = #{processinstanceid}
            </if>
        </where>
    </update>

    <!-- 查询员工加班申请详细 -->
    <select id="queryOvertimeDetail" resultMap="BaseResultMap" parameterType="org.fh.entity.common.OvertimeEntity">
        select o.status,o.emp_id,o.remark,o.update_time,e.name as overtimeUser,o.work_type,
        case
        when o.status = '1' then '未答复'
        when o.status = '2' then '已确认'
        when o.status = '3' then '已拒绝'
        when o.status = '4' then '领班确认'
        when o.status = '5' then '领班取消'
        when o.status = '6' then '经理通过'
        when o.status = '7' then '经理驳回'
        when o.status = '8' then '超时自动取消'
        end as statusName
        from biz_overtime o
        left join biz_employee e on e.emp_id = o.emp_id
        <where>
            <if test="processinstanceid !=null and processinstanceid !=''">
                and processinstanceid = #{processinstanceid}
            </if>
            <if test="empId !=null and empId !=''">
                and o.emp_id = #{empId}
            </if>
            <if test="status !=null and status !=0">
                and o.status = #{status}
            </if>
        </where>
    </select>

    <!-- 查询员工个状态数量 -->
    <select id="queryOvertimeCount" resultType="int">
        select count(*) from biz_overtime
        <where>
            <if test="empId !=null and empId !=''">
                and emp_id = #{empId}
            </if>
            <if test="list !=null">
                and status in(
                <foreach collection="list" separator="," item="item">
                    #{item}
                    )
                </foreach>
            </if>
            <if test="processInstanceId !=null and processInstanceId !=''">
                and processinstanceid = #{processInstanceId}
            </if>
        </where>
    </select>

    <select id="queryOvertimePage" resultMap="BaseResultMap">
        select o.status,o.emp_id,o.remark,o.update_time,e.name as overtimeUser,oa.reason as applyReason,oa.start_time,oa.end_time,oa.status as applyStatus,
        case
        when o.status = '1' then '未答复'
        when o.status = '2' then '已确认'
        when o.status = '3' then '拒绝'
        when o.status = '4' then '领班确认'
        when o.status = '5' then '领班取消'
        when o.status = '6' then '经理通过'
        when o.status = '7' then '经理驳回'
        when o.status = '8' then '超时自动取消'
        end as statusName,
        case
        when oa.status = '1' then '待确认'
        when oa.status = '2' then '待提交'
        when oa.status = '3' then '待审批'
        when oa.status = '4' then '审批通过'
        when oa.status = '5' then '撤回'
        when oa.status = '6' then '驳回'
        end as applyStatusName
        from biz_overtime o
        left join biz_employee e on e.emp_id = o.emp_id
        left join biz_overtime_apply oa on oa.processinstanceid = o.processinstanceid
        <where>
            <if test="processinstanceid !=null and processinstanceid !=''">
                and oa.processinstanceid = #{processinstanceid}
            </if>
            <if test="empId !=null and empId !=''">
                and o.emp_id = #{empId}
            </if>
            <if test="status !=null">
                and o.status = #{status}
            </if>
        </where>
    </select>
    
    <select id="queryOvertimeNotReply" parameterType="string" resultType="integer">
        select count(*) from biz_overtime where processinstanceid = #{processinstanceid}
    </select>

    <select id="checkOvertimeValid" resultType="integer" parameterType="string">
        select count(*)
        from biz_overtime
        where processinstanceid = #{processInstanceId} and status = '2'
    </select>

    <select id="queryEmpOvertimeSum" resultType="integer" parameterType="collection">
        select sum(oa.time_count) as timeSum from biz_overtime o
        left join biz_overtime_apply oa on o.processinstanceid = oa.processinstanceid
        where oa.`status` = '4'
        and oa.apply_date &gt;= #{startDate}
        and oa.apply_date &lt;= #{endDate}
        and o.emp_id in
        <foreach collection="empIds" separator="," item="item" open="(" close=")">
            #{item}
        </foreach>
        group by o.emp_id
        order by timeSum desc limit 1
    </select>

    <update id="updateOvertimeStatus" parameterType="string">
        update biz_overtime
        set `status` = '8'
        where processinstanceid = #{processInstanceId}
        and `status` = '1'
    </update>

</mapper>