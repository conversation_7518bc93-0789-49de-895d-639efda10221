<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.fh.mapper.dsno1.common.WithdrawnMapper">
  <resultMap id="BaseResultMap" type="org.fh.vo.common.WithdrawnVo">
    <id column="withdrawn_id" jdbcType="VARCHAR" property="withdrawnId" />
    <result column="emp_id" jdbcType="VARCHAR" property="empId" />
    <result column="execute_time" jdbcType="TIMESTAMP" property="executeTime" />
  </resultMap>
  <sql id="Base_Column_List">
    withdrawn_id, emp_id, execute_time, create_by, create_time, update_by, update_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from biz_withdrawn
    where withdrawn_id = #{withdrawnId,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from biz_withdrawn
    where withdrawn_id = #{withdrawnId,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="org.fh.entity.common.WithdrawnEntity">
    insert into biz_withdrawn (withdrawn_id, emp_id, execute_time, 
      create_by, create_time, update_by, 
      update_time)
    values (#{withdrawnId,jdbcType=VARCHAR}, #{empId,jdbcType=VARCHAR}, #{executeTime,jdbcType=TIMESTAMP}, 
      #{createBy,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{updateBy,jdbcType=VARCHAR}, 
      #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="org.fh.entity.common.WithdrawnEntity">
    insert into biz_withdrawn
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="withdrawnId != null">
        withdrawn_id,
      </if>
      <if test="empId != null">
        emp_id,
      </if>
      <if test="executeTime != null">
        execute_time,
      </if>
      <if test="createBy != null">
        create_by,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateBy != null">
        update_by,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="withdrawnId != null">
        #{withdrawnId,jdbcType=VARCHAR},
      </if>
      <if test="empId != null">
        #{empId,jdbcType=VARCHAR},
      </if>
      <if test="executeTime != null">
        #{executeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createBy != null">
        #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        #{updateBy,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="org.fh.entity.common.WithdrawnEntity">
    update biz_withdrawn
    <set>
      <if test="empId != null">
        emp_id = #{empId,jdbcType=VARCHAR},
      </if>
      <if test="executeTime != null">
        execute_time = #{executeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createBy != null">
        create_by = #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        update_by = #{updateBy,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where withdrawn_id = #{withdrawnId,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="org.fh.entity.common.WithdrawnEntity">
    update biz_withdrawn
    set emp_id = #{empId,jdbcType=VARCHAR},
      execute_time = #{executeTime,jdbcType=TIMESTAMP},
      create_by = #{createBy,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_by = #{updateBy,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where withdrawn_id = #{withdrawnId,jdbcType=VARCHAR}
  </update>

  <!-- 批量添加离职员工信息 -->
  <insert id="addWithdrawnEmployeeList" parameterType="collection">
    insert into
    biz_withdrawn(withdrawn_id,emp_id,execute_time,create_by,create_time)
    values
    <foreach collection="list" separator="," item="item">
      (#{item.withdrawnId},#{item.empId},#{item.executeTime},#{item.createBy},now())
    </foreach>
  </insert>

  <select id="queryWithdrawnEmployee" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from biz_withdrawn
    where execute_time &lt; now()
  </select>
</mapper>