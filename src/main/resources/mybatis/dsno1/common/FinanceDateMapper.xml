<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.fh.mapper.dsno1.common.FinanceDateMapper">
    <resultMap id="BaseResultMap" type="org.fh.vo.common.FinanceDateVo">
        <id column="finance_date_id" jdbcType="VARCHAR" property="financeDateId"/>
        <result column="start_date" jdbcType="DATE" property="startDate"/>
        <result column="end_date" jdbcType="DATE" property="endDate"/>
        <result column="finance_month" jdbcType="VARCHAR" property="financeMonth"/>
        <result column="finance_date" jdbcType="VARCHAR" property="financeDate"/>
        <result column="year" jdbcType="VARCHAR" property="year"/>
    </resultMap>
    <sql id="Base_Column_List">
    finance_date_id, start_date, end_date, finance_date, year, create_by, create_time, 
    update_by, update_time
  </sql>
    <select id="queryFinanceDateDetail" parameterType="org.fh.entity.common.FinanceDateEntity" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from biz_finance_date
        where finance_date_id = #{financeDateId}
    </select>

    <!-- 删除财务月时间 -->
    <delete id="deleteFinanceDate" parameterType="org.fh.entity.common.FinanceDateEntity">
        delete from biz_finance_date
        where finance_date_id = #{financeDateId}
    </delete>
    <insert id="insert" parameterType="org.fh.entity.common.FinanceDateEntity">
    insert into biz_finance_date (finance_date_id, start_date, end_date, 
      finance_date, year, create_by, 
      create_time, update_by, update_time
      )
    values (#{financeDateId,jdbcType=VARCHAR}, #{startDate,jdbcType=DATE}, #{endDate,jdbcType=DATE}, 
      #{financeDate,jdbcType=VARCHAR}, #{year,jdbcType=VARCHAR}, #{createBy,jdbcType=VARCHAR}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateBy,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP}
      )
    </insert>
    <insert id="addFinanceDate" parameterType="org.fh.entity.common.FinanceDateEntity">
        insert into biz_finance_date
        <trim prefix="(" suffix=")" suffixOverrides=",">
            finance_date_id,
            <if test="startDate != null and startDate !=''">
                start_date,
            </if>
            <if test="endDate != null and endDate !=''">
                end_date,
            </if>
            <if test="financeDate != null and financeDate !=''">
                finance_date,
            </if>
            <if test="financeMonth != null and financeMonth !=''">
                finance_month,
            </if>
            <if test="year != null and year !=''">
                year,
            </if>
            <if test="createBy != null and createBy !=''">
                create_by,
            </if>
            create_time
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            #{financeDateId},
            <if test="startDate != null and startDate !=''">
                #{startDate},
            </if>
            <if test="endDate != null and endDate !=''">
                #{endDate},
            </if>
            <if test="financeDate != null and financeDate !=''">
                #{financeDate},
            </if>
            <if test="financeMonth != null and financeMonth !=''">
                #{financeMonth},
            </if>
            <if test="year != null and year !=''">
                #{year},
            </if>
            <if test="createBy != null and createBy !=''">
                #{createBy},
            </if>
            now()
        </trim>
    </insert>
    <update id="updateFinanceDate" parameterType="org.fh.entity.common.FinanceDateEntity">
        update biz_finance_date
        <set>
            <if test="startDate != null and startDate !=''">
                start_date = #{startDate},
            </if>
            <if test="endDate != null and endDate !=''">
                end_date = #{endDate},
            </if>
            <if test="financeDate != null and financeDate !=''">
                finance_date = #{financeDate},
            </if>
            <if test="year != null and year !=''">
                year = #{year},
            </if>
            <if test="updateBy != null and updateBy !=''">
                update_by = #{updateBy},
            </if>
            update_time = now()
        </set>
        where finance_date_id = #{financeDateId,jdbcType=VARCHAR}
    </update>
    <update id="updateByPrimaryKey" parameterType="org.fh.entity.common.FinanceDateEntity">
        update biz_finance_date
        set start_date = #{startDate,jdbcType=DATE},
          end_date = #{endDate,jdbcType=DATE},
          finance_date = #{financeDate,jdbcType=VARCHAR},
          year = #{year,jdbcType=VARCHAR},
          create_by = #{createBy,jdbcType=VARCHAR},
          create_time = #{createTime,jdbcType=TIMESTAMP},
          update_by = #{updateBy,jdbcType=VARCHAR},
          update_time = #{updateTime,jdbcType=TIMESTAMP}
        where finance_date_id = #{financeDateId,jdbcType=VARCHAR}
      </update>

    <select id="queryFinanceDatePage" resultMap="BaseResultMap" parameterType="org.fh.entity.common.FinanceDateEntity">
        select finance_date_id,start_date,end_date,finance_date,year
        from biz_finance_date
        <where>
            <if test="year !=null and year !=''">
                `year` = #{year}
            </if>
        </where>
        order by finance_month desc
    </select>

    <select id="checkFinanceDate" parameterType="org.fh.entity.common.FinanceDateEntity" resultType="int">
        select count(*) from biz_finance_date
        <where>
             finance_date = #{financeDate}
            and `year` = #{year}
            <if test="financeDateId !=null and financeDateId !=''">
                and finance_date_id != #{financeDateId}
            </if>
        </where>
    </select>

    <!-- 根据财务月日期查询财务月数据 -->
    <select id="queryFinanceDateByDate" resultMap="BaseResultMap">
        select finance_date_id,start_date,end_date,finance_date,year
        from biz_finance_date where finance_month = #{year} limit 1
    </select>

    <!-- 根据结束时间查询财务月数据 -->
    <select id="queryFinanceDateByEndDate" parameterType="string" resultMap="BaseResultMap">
        select finance_date_id,start_date,end_date,finance_month,finance_date,year from biz_finance_date where end_date = #{endDate} limit 1
    </select>

    <select id="queryFinanceMonthTime" resultMap="BaseResultMap">
        select start_date,end_date,finance_date,year,finance_month
        from biz_finance_date
        where finance_month &gt;= #{startDate}
        and finance_month &lt;= #{endDate}
    </select>
</mapper>