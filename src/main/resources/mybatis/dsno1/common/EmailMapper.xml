<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.fh.mapper.dsno1.common.EmailMapper">
  <resultMap id="BaseResultMap" type="org.fh.entity.common.EmailEntity">
    <id column="email_id" jdbcType="VARCHAR" property="emailId" />
    <result column="address" jdbcType="VARCHAR" property="address" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="create_by" jdbcType="VARCHAR" property="createBy" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_by" jdbcType="VARCHAR" property="updateBy" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="deleted" jdbcType="BIT" property="deleted" />
  </resultMap>
  <sql id="Base_Column_List">
    email_id, address, status, create_by, create_time, update_by, update_time, deleted
  </sql>

  <!-- 查询邮箱明细 -->
  <select id="queryEmailDetail" parameterType="java.lang.String" resultType="org.fh.vo.common.EmailVo">
    select 
    email_id as emailId, address, status, create_by as createBy, create_time as createTime, update_by as updateBy, update_time as updateTime, deleted
    from biz_email
    where email_id = #{emailId}
  </select>

  <!-- 单个添加邮箱地址 -->
  <insert id="addEmail" parameterType="org.fh.entity.common.EmailEntity">
    insert into biz_email (email_id, address, status,
      create_by, create_time, deleted)
    values (#{emailId}, #{address}, #{status},
      #{createBy}, now(),'0')
  </insert>

  <insert id="insertSelective" parameterType="org.fh.entity.common.EmailEntity">
    insert into biz_email
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="emailId != null">
        email_id,
      </if>
      <if test="address != null">
        address,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="createBy != null">
        create_by,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateBy != null">
        update_by,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="emailId != null">
        #{emailId,jdbcType=VARCHAR},
      </if>
      <if test="address != null">
        #{address,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="createBy != null">
        #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        #{updateBy,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=BIT},
      </if>
    </trim>
  </insert>

  <!-- 达能修改邮箱地址 -->
  <update id="updateEmail" parameterType="org.fh.entity.common.EmailEntity">
    update biz_email
    <set>
      <if test="address != null and address !=''">
        address = #{address},
      </if>
      <!--<if test="status != null">
        status = #{status},
      </if>-->
      <if test="updateBy != null and updateBy !=''">
        update_by = #{updateBy},
      </if>
        update_time = now()
    </set>
    where email_id = #{emailId}
  </update>

  <!-- 单个逻辑删除删除邮箱地址 -->
  <update id="deleteEmail"  parameterType="org.fh.entity.common.EmailEntity">
    update biz_email
    set
      update_by = #{updateBy},
      update_time = now(),
      deleted = '1'
    where email_id = #{emailId}
  </update>

  <!-- 分页查询邮箱信息 -->
  <select id="queryEmailPage" resultType="org.fh.vo.common.EmailVo">
    select
    em.email_id as emailId, em.address, em.status, concat(em.create_by,'|',e.name) as createBy,
    em.create_time as createTime, concat(em.update_by,'|',ee.name) as updateBy, em.update_time as updateTime
    from biz_email em
    left join biz_employee e on e.emp_id = em.create_by
    left join biz_employee ee on ee.emp_id = em.update_by
    where em.deleted = '0'
  </select>

  <!-- 查询所有有效的邮箱信息 -->
  <select id="queryEmailList" resultType="org.fh.vo.common.EmailVo">
    select
    email_id as emailId, address, status, em.create_by as createBy,
    create_time as createTime, em.update_by as updateBy, update_time as updateTime
    from biz_email em
    where em.deleted = '0'
  </select>

</mapper>