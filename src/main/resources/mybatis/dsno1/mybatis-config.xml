<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE configuration PUBLIC "-//mybatis.org//DTD SQL Map Config 3.0//EN"  
	"http://mybatis.org/dtd/mybatis-3-config.dtd">
<configuration>

	<typeAliases>
		<typeAlias type="org.fh.entity.PageData" alias="pd"/>
		<typeAlias type="org.fh.entity.Page" alias="page"/>
		<typeAlias type="org.fh.entity.system.User" alias="user"/>
		<typeAlias type="org.fh.entity.system.Menu" alias="menu"/>
		<typeAlias type="org.fh.entity.system.Role" alias="role"/>
		<typeAlias type="org.fh.entity.system.Dictionaries" alias="dictionaries"/>
		
		<!-- 这里添加实体类 -->
		
	</typeAliases>
	
	<plugins>
		<plugin interceptor="org.fh.plugins.PagePlugin">
			<property name="dialect" value="mysql"/>
			<property name="pageSqlId" value=".*listPage.*"/>
		</plugin>
	</plugins>
	
</configuration>