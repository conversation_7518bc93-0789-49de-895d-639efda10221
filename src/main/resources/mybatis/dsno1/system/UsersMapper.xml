<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.fh.mapper.dsno1.system.UsersMapper">

    <resultMap type="User" id="userAndRoleResultMap">
        <id column="USER_ID" property="USER_ID"/>
        <result column="USERNAME" property="USERNAME"/>
        <result column="PASSWORD" property="PASSWORD"/>
        <result column="NAME" property="NAME"/>
        <result column="LAST_LOGIN" property="LAST_LOGIN"/>
        <result column="IP" property="IP"/>
        <result column="STATUS" property="STATUS"/>
        <result column="SKIN" property="SKIN"/>
        <result column="ROLE_IDS" property="ROLE_IDS"/>
        <association property="role" column="ROLE_ID" javaType="Role">
            <id column="ROLE_ID" property="ROLE_ID"/>
            <result column="ROLE_NAME" property="ROLE_NAME"/>
            <result column="ROLE_RIGHTS" property="RIGHTS"/>
            <result column="ADD_QX" property="ADD_QX"/>
            <result column="DEL_QX" property="DEL_QX"/>
            <result column="EDIT_QX" property="EDIT_QX"/>
            <result column="CHA_QX" property="CHA_QX"/>
        </association>
    </resultMap>
    <resultMap type="User" id="userResultMap">
        <id column="USER_ID" property="USER_ID"/>
        <result column="USERNAME" property="USERNAME"/>
        <result column="PASSWORD" property="PASSWORD"/>
        <result column="NAME" property="NAME"/>
        <result column="LAST_LOGIN" property="LAST_LOGIN"/>
        <result column="IP" property="IP"/>
        <result column="STATUS" property="STATUS"/>
        <result column="ROLE_ID" property="ROLE_ID"/>
        <result column="SKIN" property="SKIN"/>
        <result column="ROLE_IDS" property="ROLE_IDS"/>
    </resultMap>

    <!--用户表名 -->
    <sql id="tableName">
		SYS_USER
	</sql>

    <!--角色表名 -->
    <sql id="roleTableName">
		SYS_ROLE
	</sql>

    <!-- 字段 -->
    <sql id="Field">
		USER_ID,
		USERNAME,
		PASSWORD,
		NAME,
		ROLE_ID,
		LAST_LOGIN,
		IP,
		STATUS,
		BZ,
		SKIN,
		EMAIL,
		NUMBER,
		PHONE,
		ROLE_IDS
	</sql>

    <!-- 字段值 -->
    <sql id="FieldValue">
		#{USER_ID},
		#{USERNAME},
		#{PASSWORD},
		#{NAME},
		#{ROLE_ID},
		#{LAST_LOGIN},
		#{IP},
		#{STATUS},
		#{BZ},
		#{SKIN},
		#{EMAIL},
		#{NUMBER},
		#{PHONE},
		#{ROLE_IDS}
	</sql>

    <!-- 通过USERNAME获取数据 -->
    <select id="findByUsername" parameterType="pd" resultType="pd">
        select
        <include refid="Field"></include>
        from
        <include refid="tableName"></include>
        where
        USERNAME = #{USERNAME}
    </select>

    <!-- 通过用户ID获取数据 -->
    <select id="findById" parameterType="pd" resultType="pd">
        select
        <include refid="Field"></include>
        from
        <include refid="tableName"></include>
        where
        USER_ID = #{USER_ID}
    </select>

    <!-- 通过邮箱获取数据 -->
    <select id="findByEmail" parameterType="pd" resultType="pd">
        select
        <include refid="Field"></include>
        from
        <include refid="tableName"></include>
        where
        EMAIL = #{EMAIL}
        <if test="USERNAME != null and USERNAME != ''">
            and USERNAME != #{USERNAME}
        </if>
    </select>

    <!-- 通过编码获取数据 -->
    <select id="findByNumbe" parameterType="pd" resultType="pd">
        select
        <include refid="Field"></include>
        from
        <include refid="tableName"></include>
        where
        NUMBER = #{NUMBER}
        <if test="USERNAME != null and USERNAME != ''">
            and USERNAME != #{USERNAME}
        </if>
    </select>

    <!-- 列出某角色下的所有用户 -->
    <select id="listAllUserByRoldId" parameterType="pd" resultType="pd">
        select USER_ID
        from
        <include refid="tableName"></include>
        where
        ROLE_ID = #{ROLE_ID}
    </select>

    <!-- 新增用户 -->
    <insert id="saveUser" parameterType="pd">
        insert into <include refid="tableName"></include> (
        <include refid="Field"></include>
        ) values (
        <include refid="FieldValue"></include>
        )
    </insert>

    <!-- 修改 -->
    <update id="editUser" parameterType="pd">
        update
        <include refid="tableName"></include>
        set NAME = #{NAME},
        ROLE_ID = #{ROLE_ID},
        ROLE_IDS = #{ROLE_IDS},
        BZ = #{BZ},
        EMAIL = #{EMAIL},
        NUMBER = #{NUMBER},
        PHONE = #{PHONE}
        <if test="PASSWORD != null and PASSWORD != ''">
            ,PASSWORD = #{PASSWORD}
        </if>
        where
        USER_ID = #{USER_ID}
    </update>

    <!-- 用户列表 -->
    <select id="userlistPage" parameterType="page" resultType="pd">
        select u.USER_ID,
        u.USERNAME,
        u.PASSWORD,
        u.LAST_LOGIN,
        u.NAME,
        u.IP,
        u.EMAIL,
        u.NUMBER,
        u.PHONE,
        r.ROLE_ID,
        r.ROLE_NAME
        from <include refid="tableName"></include> u, <include refid="roleTableName"></include> r
        where u.ROLE_ID = r.ROLE_ID
        and u.USERNAME != 'admin'
        and r.PARENT_ID = '1'
        <if test="pd.KEYWORDS!= null and pd.KEYWORDS != ''"><!-- 关键词检索 -->
            and
            (
            u.USERNAME LIKE CONCAT(CONCAT('%', #{pd.KEYWORDS}),'%')
            or
            u.EMAIL LIKE CONCAT(CONCAT('%', #{pd.KEYWORDS}),'%')
            or
            u.NUMBER LIKE CONCAT(CONCAT('%', #{pd.KEYWORDS}),'%')
            or
            u.NAME LIKE CONCAT(CONCAT('%', #{pd.KEYWORDS}),'%')
            or
            u.PHONE LIKE CONCAT(CONCAT('%', #{pd.KEYWORDS}),'%')
            )
        </if>
        <if test="pd.ROLE_ID != null and pd.ROLE_ID != ''">        <!-- 角色检索 -->
            and u.ROLE_ID=#{pd.ROLE_ID}
        </if>
        <if test="pd.STRARTTIME!=null and pd.STRARTTIME!=''">    <!-- 登录时间检索 -->
            and u.LAST_LOGIN &gt;= #{pd.STRARTTIME}
        </if>
        <if test="pd.ENDTIME!=null and pd.ENDTIME!=''">            <!-- 登录时间检索 -->
            and u.LAST_LOGIN &lt;= #{pd.ENDTIME}
        </if>
        order by u.LAST_LOGIN desc
    </select>

    <!-- 用户列表(全部) -->
    <select id="listAllUser" parameterType="pd" resultType="pd">
        select u.USER_ID,
        u.USERNAME,
        u.PASSWORD,
        u.LAST_LOGIN,
        u.NAME,
        u.IP,
        u.EMAIL,
        u.NUMBER,
        u.PHONE,
        r.ROLE_ID,
        r.ROLE_NAME
        from <include refid="tableName"></include> u, <include refid="roleTableName"></include> r
        where u.ROLE_ID = r.ROLE_ID
        and u.USERNAME != 'admin'
        and r.PARENT_ID = '1'
        <if test="KEYWORDS != null and KEYWORDS != ''"><!-- 关键词检索 -->
            and
            (
            u.USERNAME LIKE CONCAT(CONCAT('%', #{KEYWORDS}),'%')
            or
            u.EMAIL LIKE CONCAT(CONCAT('%', #{KEYWORDS}),'%')
            or
            u.NUMBER LIKE CONCAT(CONCAT('%', #{KEYWORDS}),'%')
            or
            u.NAME LIKE CONCAT(CONCAT('%', #{KEYWORDS}),'%')
            or
            u.PHONE LIKE CONCAT(CONCAT('%', #{KEYWORDS}),'%')
            )
        </if>
        <if test="ROLE_ID != null and ROLE_ID != ''"><!-- 角色检索 -->
            and u.ROLE_ID=#{ROLE_ID}
        </if>
        <if test="STRARTTIME!=null and STRARTTIME!=''"><!-- 登录时间检索 -->
            and u.LAST_LOGIN &gt;= #{STRARTTIME}
        </if>
        <if test="ENDTIME!=null and ENDTIME!=''"><!-- 登录时间检索 -->
            and u.LAST_LOGIN &lt;= #{ENDTIME}
        </if>
        order by u.LAST_LOGIN desc
    </select>

    <!-- 通过用户ID获取用户信息和角色信息 -->
    <select id="getUserAndRoleById" parameterType="String" resultMap="userAndRoleResultMap">
        select u.USER_ID,
        u.USERNAME,
        u.NAME,
        u.PASSWORD,
        u.SKIN,
        u.NUMBER,
        u.ROLE_IDS,
        r.ROLE_ID,
        r.ROLE_NAME,
        r.RIGHTS as ROLE_RIGHTS,
        r.ADD_QX,
        r.DEL_QX,
        r.EDIT_QX,
        r.CHA_QX
        from
        <include refid="tableName"></include>
        u
        left join
        <include refid="roleTableName"></include>
        r
        on u.ROLE_ID=r.ROLE_ID
        where u.STATUS=0
        and u.USER_ID=#{USER_ID}
    </select>

    <!-- 存入IP -->
    <update id="saveIP" parameterType="pd">
        update
        <include refid="tableName"></include>
        set
        IP = #{IP},
        LAST_LOGIN = #{LAST_LOGIN}
        where
        USERNAME = #{USERNAME}
    </update>

    <!-- 保存用户皮肤 -->
    <update id="saveSkin" parameterType="pd">
        update
        <include refid="tableName"></include>
        set
        SKIN = #{SKIN}
        where USERNAME = #{USERNAME}
    </update>

    <!-- 删除用户 -->
    <delete id="deleteUser" parameterType="pd">
        delete from
        <include refid="tableName"></include>
        where
        USER_ID = #{USER_ID}
        and
        USER_ID != '1'
    </delete>

    <!-- 批量删除用户 -->
    <delete id="deleteAllUser" parameterType="String">
        delete from
        <include refid="tableName"></include>
        where
        USER_ID in
        <foreach item="item" index="index" collection="array" open="(" separator="," close=")">
            #{item}
        </foreach>
        and
        USER_ID != '1'
    </delete>

    <select id="queryUsernameListAll" resultType="string">
		select USERNAME from SYS_USER
	</select>

    <!-- 批量添加人员信息 -->
    <insert id="addUserList" parameterType="collection">
        insert into SYS_USER(USER_ID,USERNAME,PASSWORD,NAME)
        values
        <foreach collection="list" item="item" separator=",">
            (
            #{item.USER_ID},#{item.USERNAME},#{item.PASSWORD},#{item.NAME}
            )
        </foreach>
    </insert>

    <!-- 批量删除人员信息 -->
    <delete id="deleteUserList" parameterType="collection">
        delete from SYS_USER where USERNAME in
        <foreach collection="list" separator="," item="item" open="(" close=")">
            #{item}
        </foreach>
    </delete>

    <!-- 校验用户登录信息 -->
    <select id="userLoginCheck" resultType="org.fh.vo.system.UserVo">
        select u.USER_ID as userId,u.USERNAME,u.NAME,r.ROLE_ID as
        roleId,u.STATUS,u.EMAIL,u.NUMBER,e.rnumber,cc.plant,cc.workshop,cc.vsm,e.work_team as workTeam,e.actived
	    from SYS_USER u
	    left join biz_employee e on e.emp_id = u.username
	    left join sys_role r on e.rnumber = r.RNUMBER
	    left join biz_cost_center cc on cc.cost_center = e.cost_center
	    where u.USERNAME = #{username} and u.PASSWORD = #{password}  limit 1
    </select>

    <update id="updateUserPassword">
        update SYS_USER set PASSWORD = #{password}  where USERNAME = #{username}
    </update>

    <!-- 用户列表(弹窗选择用) -->
    <select id="userBystafflistPage" parameterType="page" resultType="pd">
        select u.USER_ID,
        u.USERNAME,
        u.PASSWORD,
        u.LAST_LOGIN,
        u.NAME,
        u.IP,
        u.EMAIL,
        u.NUMBER,
        u.PHONE,
        r.ROLE_ID,
        r.ROLE_NAME
        from <include refid="tableName"></include> u, <include refid="roleTableName"></include> r
        where u.ROLE_ID = r.ROLE_ID
        and u.USERNAME != 'admin'
        and r.PARENT_ID = '1'
        <if test="pd.KEYWORDS!= null and pd.KEYWORDS != ''"><!-- 关键词检索 -->
            and
            (
            u.USERNAME LIKE CONCAT(CONCAT('%', #{pd.KEYWORDS}),'%')
            or
            u.EMAIL LIKE CONCAT(CONCAT('%', #{pd.KEYWORDS}),'%')
            or
            u.NUMBER LIKE CONCAT(CONCAT('%', #{pd.KEYWORDS}),'%')
            or
            u.NAME LIKE CONCAT(CONCAT('%', #{pd.KEYWORDS}),'%')
            or
            u.PHONE LIKE CONCAT(CONCAT('%', #{pd.KEYWORDS}),'%')
            )
        </if>
        <if test="pd.ROLE_ID != null and pd.ROLE_ID != ''"><!-- 角色检索 -->
            and u.ROLE_ID=#{pd.ROLE_ID}
        </if>
        <if test="pd.STRARTTIME!=null and pd.STRARTTIME!=''">    <!-- 登录时间检索 -->
            and u.LAST_LOGIN &gt;= #{pd.STRARTTIME}
        </if>
        <if test="pd.ENDTIME!=null and pd.ENDTIME!=''">            <!-- 登录时间检索 -->
            and u.LAST_LOGIN &lt;= #{pd.ENDTIME}
        </if>
        order by u.LAST_LOGIN desc
    </select>

    <!-- 根据用户名查询是否存在 -->
    <select id="queryUserByUsername" resultType="int">
        select count(*) from SYS_USER where USERNAME = #{username}
    </select>

    <!-- 查询工号是否是领班 -->
    <select id="checkEmployeeDetail" resultType="int">
        select count(*) from biz_work_group where leader_id = #{empId}
    </select>

    <!-- fh 31 359 67 90qq(青 苔) -->
</mapper>