<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.fh.mapper.dsno1.bus.UserInfoMapper">

  <!--表名 -->
  <sql id="tableName">
		SYS_USER_INFO
	</sql>

  <!--数据字典表名 -->
  <sql id="dicTableName">
		SYS_DICTIONARIES
	</sql>

  <!-- 字段 -->
  <sql id="Field">
		f.EMP_ID,	
		f.NETWORK_ID,	
		f.EMP_NAME,	
		f.DEPT,	
		f.POSITION,	
		f.STATUS_ON,	
		f.FIRST_LEVEL_MGR_ID,	
		f.SECOND_LEVEL_MGR_ID,	
		f.WH_TYPE,	
		f.TEAM,	
		f.TRANSFERED,	
		f.O<PERSON>EN_ID,	
		f.FACE_UPDATE_TIME,	
		f.FACE_URL,	
		f.USER_INFO_ID
	</sql>

  <!-- 字段用于新增 -->
  <sql id="Field2">
		EMP_ID,	
		NETWORK_ID,	
		EMP_NAME,	
		DEPT,	
		POSITION,	
		STATUS_ON,	
		FIRST_LEVEL_MGR_ID,	
		SECOND_LEVEL_MGR_ID,	
		WH_TYPE,	
		TEAM,	
		TRANSFERED,	
		OPEN_ID,	
		FACE_UPDATE_TIME,	
		FACE_URL,	
		USER_INFO_ID
	</sql>

  <!-- 字段值 -->
  <sql id="FieldValue">
		#{EMP_ID},	
		#{NETWORK_ID},	
		#{EMP_NAME},	
		#{DEPT},	
		#{POSITION},	
		#{STATUS_ON},	
		#{FIRST_LEVEL_MGR_ID},	
		#{SECOND_LEVEL_MGR_ID},	
		#{WH_TYPE},	
		#{TEAM},	
		#{TRANSFERED},	
		#{OPEN_ID},	
		#{FACE_UPDATE_TIME},	
		#{FACE_URL},	
		#{USER_INFO_ID}
	</sql>

  <!-- 新增-->
  <insert id="save" parameterType="pd">
    insert into
    <include refid="tableName"></include>
    (
    <include refid="Field2"></include>
    ) values (
    <include refid="FieldValue"></include>
    )
  </insert>

  <!-- 删除-->
  <delete id="delete" parameterType="pd">
    delete from
    <include refid="tableName"></include>
    where
    USER_INFO_ID = #{USERINFO_ID}
  </delete>

  <!-- 修改 -->
  <update id="edit" parameterType="pd">
    update
    <include refid="tableName"></include>
    set
    EMP_ID = #{EMP_ID},
    NETWORK_ID = #{NETWORK_ID},
    EMP_NAME = #{EMP_NAME},
    DEPT = #{DEPT},
    POSITION = #{POSITION},
    STATUS_ON = #{STATUS_ON},
    FIRST_LEVEL_MGR_ID = #{FIRST_LEVEL_MGR_ID},
    SECOND_LEVEL_MGR_ID = #{SECOND_LEVEL_MGR_ID},
    WH_TYPE = #{WH_TYPE},
    TEAM = #{TEAM},
    TRANSFERED = #{TRANSFERED},
    OPEN_ID = #{OPEN_ID},
    FACE_UPDATE_TIME = #{FACE_UPDATE_TIME},
    FACE_URL = #{FACE_URL},
    USER_INFO_ID = USERINFO_ID
    where
    USER_INFO_ID = #{USERINFO_ID}
  </update>

  <!-- 通过ID获取数据 -->
  <select id="findById" parameterType="pd" resultType="pd">
    select
    <include refid="Field"></include>
    from
    <include refid="tableName"></include>
    f
    where
    f.USER_INFO_ID = #{USERINFO_ID}
  </select>

  <!-- 通过username获取数据 -->
  <select id="findByUsername" parameterType="pd" resultType="pd">
    select
    <include refid="Field"></include>
    from
    <include refid="tableName"></include>
    f
    where
    f.EMP_ID = #{username}
  </select>

  <!-- 列表 -->
  <select id="datalistPage" parameterType="page" resultType="pd">
    select
    <include refid="Field"></include>
    from
    <include refid="tableName"></include>
    f
    where 1=1
    <if test="pd.KEYWORDS != null and pd.KEYWORDS != ''"><!-- 关键词检索 -->
      and
      (
      <!--	根据需求自己加检索条件
        字段1 LIKE CONCAT(CONCAT('%', #{pd.KEYWORDS}),'%')
         or
        字段2 LIKE CONCAT(CONCAT('%', #{pd.KEYWORDS}),'%')
      -->
      )
    </if>
  </select>

  <!-- 列表(全部) -->
  <select id="listAll" parameterType="pd" resultType="pd">
    select
    <include refid="Field"></include>
    from
    <include refid="tableName"></include>
    f
  </select>

  <!-- 批量删除 -->
  <delete id="deleteAll" parameterType="String">
    delete from
    <include refid="tableName"></include>
    where
    USER_INFO_ID in
    <foreach item="item" index="index" collection="array" open="(" separator="," close=")">
      #{item}
    </foreach>
  </delete>

</mapper>
