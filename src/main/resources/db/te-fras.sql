/*
 Navicat Premium Data Transfer

 Source Server         : localhost_3306_new
 Source Server Type    : MySQL
 Source Server Version : 50723
 Source Host           : localhost:3306
 Source Schema         : te-fras

 Target Server Type    : MySQL
 Target Server Version : 50723
 File Encoding         : 65001

 Date: 12/07/2019 14:44:05
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for act_evt_log
-- ----------------------------
DROP TABLE IF EXISTS `act_evt_log`;
CREATE TABLE `act_evt_log`  (
  `LOG_NR_` bigint(20) NOT NULL AUTO_INCREMENT,
  `TYPE_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `PROC_DEF_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `PROC_INST_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `EXECUTION_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `TASK_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `TIME_STAMP_` timestamp(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3),
  `USER_ID_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `DATA_` longblob NULL,
  `LOCK_OWNER_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `LOCK_TIME_` timestamp(3) NULL DEFAULT NULL,
  `IS_PROCESSED_` tinyint(4) NULL DEFAULT 0,
  PRIMARY KEY (`LOG_NR_`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_bin ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for act_ge_bytearray
-- ----------------------------
DROP TABLE IF EXISTS `act_ge_bytearray`;
CREATE TABLE `act_ge_bytearray`  (
  `ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL DEFAULT '',
  `REV_` int(11) NULL DEFAULT NULL,
  `NAME_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `DEPLOYMENT_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `BYTES_` longblob NULL,
  `GENERATED_` tinyint(4) NULL DEFAULT NULL,
  PRIMARY KEY (`ID_`) USING BTREE,
  INDEX `ACT_FK_BYTEARR_DEPL`(`DEPLOYMENT_ID_`) USING BTREE,
  CONSTRAINT `act_ge_bytearray_ibfk_1` FOREIGN KEY (`DEPLOYMENT_ID_`) REFERENCES `act_re_deployment` (`ID_`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_bin ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of act_ge_bytearray
-- ----------------------------
INSERT INTO `act_ge_bytearray` VALUES ('237506', 2, 'source', NULL, 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
INSERT INTO `act_ge_bytearray` VALUES ('237507', 1, 'source-extra', NULL, 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, NULL);
INSERT INTO `act_ge_bytearray` VALUES ('242511', 1, '请假流程.bpmn20.xml', '242510', 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, 0);
INSERT INTO `act_ge_bytearray` VALUES ('242512', 1, '请假流程.KEY_leave.png', '242510', 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

-- ----------------------------
-- Table structure for act_ge_property
-- ----------------------------
DROP TABLE IF EXISTS `act_ge_property`;
CREATE TABLE `act_ge_property`  (
  `NAME_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL DEFAULT '',
  `VALUE_` varchar(300) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `REV_` int(11) NULL DEFAULT NULL,
  PRIMARY KEY (`NAME_`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_bin ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of act_ge_property
-- ----------------------------
INSERT INTO `act_ge_property` VALUES ('cfg.execution-related-entities-count', 'false', 1);
INSERT INTO `act_ge_property` VALUES ('next.dbid', '245001', 99);
INSERT INTO `act_ge_property` VALUES ('schema.history', 'create(6.0.0.4)', 1);
INSERT INTO `act_ge_property` VALUES ('schema.version', '6.0.0.4', 1);

-- ----------------------------
-- Table structure for act_hi_actinst
-- ----------------------------
DROP TABLE IF EXISTS `act_hi_actinst`;
CREATE TABLE `act_hi_actinst`  (
  `ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
  `PROC_DEF_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
  `PROC_INST_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
  `EXECUTION_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
  `ACT_ID_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
  `TASK_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `CALL_PROC_INST_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `ACT_NAME_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `ACT_TYPE_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
  `ASSIGNEE_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `START_TIME_` datetime(3) NOT NULL,
  `END_TIME_` datetime(3) NULL DEFAULT NULL,
  `DURATION_` bigint(20) NULL DEFAULT NULL,
  `DELETE_REASON_` varchar(4000) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `TENANT_ID_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT '',
  PRIMARY KEY (`ID_`) USING BTREE,
  INDEX `ACT_IDX_HI_ACT_INST_START`(`START_TIME_`) USING BTREE,
  INDEX `ACT_IDX_HI_ACT_INST_END`(`END_TIME_`) USING BTREE,
  INDEX `ACT_IDX_HI_ACT_INST_PROCINST`(`PROC_INST_ID_`, `ACT_ID_`) USING BTREE,
  INDEX `ACT_IDX_HI_ACT_INST_EXEC`(`EXECUTION_ID_`, `ACT_ID_`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_bin ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for act_hi_attachment
-- ----------------------------
DROP TABLE IF EXISTS `act_hi_attachment`;
CREATE TABLE `act_hi_attachment`  (
  `ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
  `REV_` int(11) NULL DEFAULT NULL,
  `USER_ID_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `NAME_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `DESCRIPTION_` varchar(4000) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `TYPE_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `TASK_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `PROC_INST_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `URL_` varchar(4000) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `CONTENT_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `TIME_` datetime(3) NULL DEFAULT NULL,
  PRIMARY KEY (`ID_`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_bin ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for act_hi_comment
-- ----------------------------
DROP TABLE IF EXISTS `act_hi_comment`;
CREATE TABLE `act_hi_comment`  (
  `ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
  `TYPE_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `TIME_` datetime(3) NOT NULL,
  `USER_ID_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `TASK_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `PROC_INST_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `ACTION_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `MESSAGE_` varchar(4000) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `FULL_MSG_` longblob NULL,
  PRIMARY KEY (`ID_`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_bin ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for act_hi_detail
-- ----------------------------
DROP TABLE IF EXISTS `act_hi_detail`;
CREATE TABLE `act_hi_detail`  (
  `ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
  `TYPE_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
  `PROC_INST_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `EXECUTION_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `TASK_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `ACT_INST_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `NAME_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
  `VAR_TYPE_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `REV_` int(11) NULL DEFAULT NULL,
  `TIME_` datetime(3) NOT NULL,
  `BYTEARRAY_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `DOUBLE_` double NULL DEFAULT NULL,
  `LONG_` bigint(20) NULL DEFAULT NULL,
  `TEXT_` varchar(4000) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `TEXT2_` varchar(4000) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  PRIMARY KEY (`ID_`) USING BTREE,
  INDEX `ACT_IDX_HI_DETAIL_PROC_INST`(`PROC_INST_ID_`) USING BTREE,
  INDEX `ACT_IDX_HI_DETAIL_ACT_INST`(`ACT_INST_ID_`) USING BTREE,
  INDEX `ACT_IDX_HI_DETAIL_TIME`(`TIME_`) USING BTREE,
  INDEX `ACT_IDX_HI_DETAIL_NAME`(`NAME_`) USING BTREE,
  INDEX `ACT_IDX_HI_DETAIL_TASK_ID`(`TASK_ID_`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_bin ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for act_hi_identitylink
-- ----------------------------
DROP TABLE IF EXISTS `act_hi_identitylink`;
CREATE TABLE `act_hi_identitylink`  (
  `ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL DEFAULT '',
  `GROUP_ID_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `TYPE_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `USER_ID_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `TASK_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `PROC_INST_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  PRIMARY KEY (`ID_`) USING BTREE,
  INDEX `ACT_IDX_HI_IDENT_LNK_USER`(`USER_ID_`) USING BTREE,
  INDEX `ACT_IDX_HI_IDENT_LNK_TASK`(`TASK_ID_`) USING BTREE,
  INDEX `ACT_IDX_HI_IDENT_LNK_PROCINST`(`PROC_INST_ID_`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_bin ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for act_hi_procinst
-- ----------------------------
DROP TABLE IF EXISTS `act_hi_procinst`;
CREATE TABLE `act_hi_procinst`  (
  `ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
  `PROC_INST_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
  `BUSINESS_KEY_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `PROC_DEF_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
  `START_TIME_` datetime(3) NOT NULL,
  `END_TIME_` datetime(3) NULL DEFAULT NULL,
  `DURATION_` bigint(20) NULL DEFAULT NULL,
  `START_USER_ID_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `START_ACT_ID_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `END_ACT_ID_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `SUPER_PROCESS_INSTANCE_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `DELETE_REASON_` varchar(4000) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `TENANT_ID_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT '',
  `NAME_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  PRIMARY KEY (`ID_`) USING BTREE,
  UNIQUE INDEX `PROC_INST_ID_`(`PROC_INST_ID_`) USING BTREE,
  INDEX `ACT_IDX_HI_PRO_INST_END`(`END_TIME_`) USING BTREE,
  INDEX `ACT_IDX_HI_PRO_I_BUSKEY`(`BUSINESS_KEY_`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_bin ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for act_hi_taskinst
-- ----------------------------
DROP TABLE IF EXISTS `act_hi_taskinst`;
CREATE TABLE `act_hi_taskinst`  (
  `ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
  `PROC_DEF_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `TASK_DEF_KEY_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `PROC_INST_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `EXECUTION_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `NAME_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `PARENT_TASK_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `DESCRIPTION_` varchar(4000) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `OWNER_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `ASSIGNEE_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `START_TIME_` datetime(3) NOT NULL,
  `CLAIM_TIME_` datetime(3) NULL DEFAULT NULL,
  `END_TIME_` datetime(3) NULL DEFAULT NULL,
  `DURATION_` bigint(20) NULL DEFAULT NULL,
  `DELETE_REASON_` varchar(4000) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `PRIORITY_` int(11) NULL DEFAULT NULL,
  `DUE_DATE_` datetime(3) NULL DEFAULT NULL,
  `FORM_KEY_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `CATEGORY_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `TENANT_ID_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT '',
  PRIMARY KEY (`ID_`) USING BTREE,
  INDEX `ACT_IDX_HI_TASK_INST_PROCINST`(`PROC_INST_ID_`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_bin ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for act_hi_varinst
-- ----------------------------
DROP TABLE IF EXISTS `act_hi_varinst`;
CREATE TABLE `act_hi_varinst`  (
  `ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
  `PROC_INST_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `EXECUTION_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `TASK_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `NAME_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
  `VAR_TYPE_` varchar(100) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `REV_` int(11) NULL DEFAULT NULL,
  `BYTEARRAY_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `DOUBLE_` double NULL DEFAULT NULL,
  `LONG_` bigint(20) NULL DEFAULT NULL,
  `TEXT_` varchar(4000) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `TEXT2_` varchar(4000) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `CREATE_TIME_` datetime(3) NULL DEFAULT NULL,
  `LAST_UPDATED_TIME_` datetime(3) NULL DEFAULT NULL,
  PRIMARY KEY (`ID_`) USING BTREE,
  INDEX `ACT_IDX_HI_PROCVAR_PROC_INST`(`PROC_INST_ID_`) USING BTREE,
  INDEX `ACT_IDX_HI_PROCVAR_NAME_TYPE`(`NAME_`, `VAR_TYPE_`) USING BTREE,
  INDEX `ACT_IDX_HI_PROCVAR_TASK_ID`(`TASK_ID_`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_bin ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for act_id_group
-- ----------------------------
DROP TABLE IF EXISTS `act_id_group`;
CREATE TABLE `act_id_group`  (
  `ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL DEFAULT '',
  `REV_` int(11) NULL DEFAULT NULL,
  `NAME_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `TYPE_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  PRIMARY KEY (`ID_`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_bin ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for act_id_info
-- ----------------------------
DROP TABLE IF EXISTS `act_id_info`;
CREATE TABLE `act_id_info`  (
  `ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL DEFAULT '',
  `REV_` int(11) NULL DEFAULT NULL,
  `USER_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `TYPE_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `KEY_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `VALUE_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `PASSWORD_` longblob NULL,
  `PARENT_ID_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  PRIMARY KEY (`ID_`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_bin ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for act_id_membership
-- ----------------------------
DROP TABLE IF EXISTS `act_id_membership`;
CREATE TABLE `act_id_membership`  (
  `USER_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL DEFAULT '',
  `GROUP_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL DEFAULT '',
  PRIMARY KEY (`USER_ID_`, `GROUP_ID_`) USING BTREE,
  INDEX `ACT_FK_MEMB_GROUP`(`GROUP_ID_`) USING BTREE,
  CONSTRAINT `act_id_membership_ibfk_1` FOREIGN KEY (`GROUP_ID_`) REFERENCES `act_id_group` (`ID_`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `act_id_membership_ibfk_2` FOREIGN KEY (`USER_ID_`) REFERENCES `act_id_user` (`ID_`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_bin ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for act_id_user
-- ----------------------------
DROP TABLE IF EXISTS `act_id_user`;
CREATE TABLE `act_id_user`  (
  `ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL DEFAULT '',
  `REV_` int(11) NULL DEFAULT NULL,
  `FIRST_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `LAST_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `EMAIL_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `PWD_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `PICTURE_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  PRIMARY KEY (`ID_`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_bin ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for act_procdef_info
-- ----------------------------
DROP TABLE IF EXISTS `act_procdef_info`;
CREATE TABLE `act_procdef_info`  (
  `ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
  `PROC_DEF_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
  `REV_` int(11) NULL DEFAULT NULL,
  `INFO_JSON_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  PRIMARY KEY (`ID_`) USING BTREE,
  UNIQUE INDEX `ACT_UNIQ_INFO_PROCDEF`(`PROC_DEF_ID_`) USING BTREE,
  INDEX `ACT_IDX_INFO_PROCDEF`(`PROC_DEF_ID_`) USING BTREE,
  INDEX `ACT_FK_INFO_JSON_BA`(`INFO_JSON_ID_`) USING BTREE,
  CONSTRAINT `act_procdef_info_ibfk_1` FOREIGN KEY (`INFO_JSON_ID_`) REFERENCES `act_ge_bytearray` (`ID_`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `act_procdef_info_ibfk_2` FOREIGN KEY (`PROC_DEF_ID_`) REFERENCES `act_re_procdef` (`ID_`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_bin ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for act_re_deployment
-- ----------------------------
DROP TABLE IF EXISTS `act_re_deployment`;
CREATE TABLE `act_re_deployment`  (
  `ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL DEFAULT '',
  `NAME_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `CATEGORY_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `KEY_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `TENANT_ID_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT '',
  `DEPLOY_TIME_` timestamp(3) NULL DEFAULT NULL,
  `ENGINE_VERSION_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  PRIMARY KEY (`ID_`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_bin ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of act_re_deployment
-- ----------------------------
INSERT INTO `act_re_deployment` VALUES ('242510', '请假流程', NULL, NULL, '', '2019-03-09 12:34:38.204', NULL);

-- ----------------------------
-- Table structure for act_re_model
-- ----------------------------
DROP TABLE IF EXISTS `act_re_model`;
CREATE TABLE `act_re_model`  (
  `ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
  `REV_` int(11) NULL DEFAULT NULL,
  `NAME_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `KEY_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `CATEGORY_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `CREATE_TIME_` timestamp(3) NULL DEFAULT NULL,
  `LAST_UPDATE_TIME_` timestamp(3) NULL DEFAULT NULL,
  `VERSION_` int(11) NULL DEFAULT NULL,
  `META_INFO_` varchar(4000) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `DEPLOYMENT_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `EDITOR_SOURCE_VALUE_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `EDITOR_SOURCE_EXTRA_VALUE_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `TENANT_ID_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT '',
  PRIMARY KEY (`ID_`) USING BTREE,
  INDEX `ACT_FK_MODEL_SOURCE`(`EDITOR_SOURCE_VALUE_ID_`) USING BTREE,
  INDEX `ACT_FK_MODEL_SOURCE_EXTRA`(`EDITOR_SOURCE_EXTRA_VALUE_ID_`) USING BTREE,
  INDEX `ACT_FK_MODEL_DEPLOYMENT`(`DEPLOYMENT_ID_`) USING BTREE,
  CONSTRAINT `act_re_model_ibfk_1` FOREIGN KEY (`DEPLOYMENT_ID_`) REFERENCES `act_re_deployment` (`ID_`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `act_re_model_ibfk_2` FOREIGN KEY (`EDITOR_SOURCE_VALUE_ID_`) REFERENCES `act_ge_bytearray` (`ID_`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `act_re_model_ibfk_3` FOREIGN KEY (`EDITOR_SOURCE_EXTRA_VALUE_ID_`) REFERENCES `act_ge_bytearray` (`ID_`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_bin ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of act_re_model
-- ----------------------------
INSERT INTO `act_re_model` VALUES ('237505', 5, '请假流程', 'KEY_leave', '00102', '2019-03-09 05:44:16.820', '2019-03-09 12:34:04.740', 1, '{\"name\":\"请假流程\",\"revision\":1,\"description\":\"\"}', NULL, '237506', '237507', '');

-- ----------------------------
-- Table structure for act_re_procdef
-- ----------------------------
DROP TABLE IF EXISTS `act_re_procdef`;
CREATE TABLE `act_re_procdef`  (
  `ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
  `REV_` int(11) NULL DEFAULT NULL,
  `CATEGORY_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `NAME_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `KEY_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
  `VERSION_` int(11) NOT NULL,
  `DEPLOYMENT_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `RESOURCE_NAME_` varchar(4000) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `DGRM_RESOURCE_NAME_` varchar(4000) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `DESCRIPTION_` varchar(4000) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `HAS_START_FORM_KEY_` tinyint(4) NULL DEFAULT NULL,
  `HAS_GRAPHICAL_NOTATION_` tinyint(4) NULL DEFAULT NULL,
  `SUSPENSION_STATE_` int(11) NULL DEFAULT NULL,
  `TENANT_ID_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT '',
  `ENGINE_VERSION_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  PRIMARY KEY (`ID_`) USING BTREE,
  UNIQUE INDEX `ACT_UNIQ_PROCDEF`(`KEY_`, `VERSION_`, `TENANT_ID_`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_bin ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of act_re_procdef
-- ----------------------------
INSERT INTO `act_re_procdef` VALUES ('KEY_leave:1:242513', 1, 'http://www.fhadmin.org/', '请假流程', 'KEY_leave', 1, '242510', '请假流程.bpmn20.xml', '请假流程.KEY_leave.png', NULL, 0, 1, 1, '', NULL);

-- ----------------------------
-- Table structure for act_ru_deadletter_job
-- ----------------------------
DROP TABLE IF EXISTS `act_ru_deadletter_job`;
CREATE TABLE `act_ru_deadletter_job`  (
  `ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
  `REV_` int(11) NULL DEFAULT NULL,
  `TYPE_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
  `EXCLUSIVE_` tinyint(1) NULL DEFAULT NULL,
  `EXECUTION_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `PROCESS_INSTANCE_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `PROC_DEF_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `EXCEPTION_STACK_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `EXCEPTION_MSG_` varchar(4000) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `DUEDATE_` timestamp(3) NULL DEFAULT NULL,
  `REPEAT_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `HANDLER_TYPE_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `HANDLER_CFG_` varchar(4000) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `TENANT_ID_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT '',
  PRIMARY KEY (`ID_`) USING BTREE,
  INDEX `ACT_FK_DEADLETTER_JOB_EXECUTION`(`EXECUTION_ID_`) USING BTREE,
  INDEX `ACT_FK_DEADLETTER_JOB_PROCESS_INSTANCE`(`PROCESS_INSTANCE_ID_`) USING BTREE,
  INDEX `ACT_FK_DEADLETTER_JOB_PROC_DEF`(`PROC_DEF_ID_`) USING BTREE,
  INDEX `ACT_FK_DEADLETTER_JOB_EXCEPTION`(`EXCEPTION_STACK_ID_`) USING BTREE,
  CONSTRAINT `act_ru_deadletter_job_ibfk_1` FOREIGN KEY (`EXCEPTION_STACK_ID_`) REFERENCES `act_ge_bytearray` (`ID_`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `act_ru_deadletter_job_ibfk_2` FOREIGN KEY (`EXECUTION_ID_`) REFERENCES `act_ru_execution` (`ID_`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `act_ru_deadletter_job_ibfk_3` FOREIGN KEY (`PROCESS_INSTANCE_ID_`) REFERENCES `act_ru_execution` (`ID_`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `act_ru_deadletter_job_ibfk_4` FOREIGN KEY (`PROC_DEF_ID_`) REFERENCES `act_re_procdef` (`ID_`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_bin ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for act_ru_event_subscr
-- ----------------------------
DROP TABLE IF EXISTS `act_ru_event_subscr`;
CREATE TABLE `act_ru_event_subscr`  (
  `ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
  `REV_` int(11) NULL DEFAULT NULL,
  `EVENT_TYPE_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
  `EVENT_NAME_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `EXECUTION_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `PROC_INST_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `ACTIVITY_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `CONFIGURATION_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `CREATED_` timestamp(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
  `PROC_DEF_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `TENANT_ID_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT '',
  PRIMARY KEY (`ID_`) USING BTREE,
  INDEX `ACT_IDX_EVENT_SUBSCR_CONFIG_`(`CONFIGURATION_`) USING BTREE,
  INDEX `ACT_FK_EVENT_EXEC`(`EXECUTION_ID_`) USING BTREE,
  CONSTRAINT `act_ru_event_subscr_ibfk_1` FOREIGN KEY (`EXECUTION_ID_`) REFERENCES `act_ru_execution` (`ID_`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_bin ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for act_ru_execution
-- ----------------------------
DROP TABLE IF EXISTS `act_ru_execution`;
CREATE TABLE `act_ru_execution`  (
  `ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL DEFAULT '',
  `REV_` int(11) NULL DEFAULT NULL,
  `PROC_INST_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `BUSINESS_KEY_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `PARENT_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `PROC_DEF_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `SUPER_EXEC_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `ROOT_PROC_INST_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `ACT_ID_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `IS_ACTIVE_` tinyint(4) NULL DEFAULT NULL,
  `IS_CONCURRENT_` tinyint(4) NULL DEFAULT NULL,
  `IS_SCOPE_` tinyint(4) NULL DEFAULT NULL,
  `IS_EVENT_SCOPE_` tinyint(4) NULL DEFAULT NULL,
  `IS_MI_ROOT_` tinyint(4) NULL DEFAULT NULL,
  `SUSPENSION_STATE_` int(11) NULL DEFAULT NULL,
  `CACHED_ENT_STATE_` int(11) NULL DEFAULT NULL,
  `TENANT_ID_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT '',
  `NAME_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `START_TIME_` datetime(3) NULL DEFAULT NULL,
  `START_USER_ID_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `LOCK_TIME_` timestamp(3) NULL DEFAULT NULL,
  `IS_COUNT_ENABLED_` tinyint(4) NULL DEFAULT NULL,
  `EVT_SUBSCR_COUNT_` int(11) NULL DEFAULT NULL,
  `TASK_COUNT_` int(11) NULL DEFAULT NULL,
  `JOB_COUNT_` int(11) NULL DEFAULT NULL,
  `TIMER_JOB_COUNT_` int(11) NULL DEFAULT NULL,
  `SUSP_JOB_COUNT_` int(11) NULL DEFAULT NULL,
  `DEADLETTER_JOB_COUNT_` int(11) NULL DEFAULT NULL,
  `VAR_COUNT_` int(11) NULL DEFAULT NULL,
  `ID_LINK_COUNT_` int(11) NULL DEFAULT NULL,
  PRIMARY KEY (`ID_`) USING BTREE,
  INDEX `ACT_IDX_EXEC_BUSKEY`(`BUSINESS_KEY_`) USING BTREE,
  INDEX `ACT_IDC_EXEC_ROOT`(`ROOT_PROC_INST_ID_`) USING BTREE,
  INDEX `ACT_FK_EXE_PROCINST`(`PROC_INST_ID_`) USING BTREE,
  INDEX `ACT_FK_EXE_PARENT`(`PARENT_ID_`) USING BTREE,
  INDEX `ACT_FK_EXE_SUPER`(`SUPER_EXEC_`) USING BTREE,
  INDEX `ACT_FK_EXE_PROCDEF`(`PROC_DEF_ID_`) USING BTREE,
  CONSTRAINT `act_ru_execution_ibfk_1` FOREIGN KEY (`PARENT_ID_`) REFERENCES `act_ru_execution` (`ID_`) ON DELETE CASCADE ON UPDATE RESTRICT,
  CONSTRAINT `act_ru_execution_ibfk_2` FOREIGN KEY (`PROC_DEF_ID_`) REFERENCES `act_re_procdef` (`ID_`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `act_ru_execution_ibfk_3` FOREIGN KEY (`PROC_INST_ID_`) REFERENCES `act_ru_execution` (`ID_`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `act_ru_execution_ibfk_4` FOREIGN KEY (`SUPER_EXEC_`) REFERENCES `act_ru_execution` (`ID_`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_bin ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for act_ru_identitylink
-- ----------------------------
DROP TABLE IF EXISTS `act_ru_identitylink`;
CREATE TABLE `act_ru_identitylink`  (
  `ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL DEFAULT '',
  `REV_` int(11) NULL DEFAULT NULL,
  `GROUP_ID_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `TYPE_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `USER_ID_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `TASK_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `PROC_INST_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `PROC_DEF_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  PRIMARY KEY (`ID_`) USING BTREE,
  INDEX `ACT_IDX_IDENT_LNK_USER`(`USER_ID_`) USING BTREE,
  INDEX `ACT_IDX_IDENT_LNK_GROUP`(`GROUP_ID_`) USING BTREE,
  INDEX `ACT_IDX_ATHRZ_PROCEDEF`(`PROC_DEF_ID_`) USING BTREE,
  INDEX `ACT_FK_TSKASS_TASK`(`TASK_ID_`) USING BTREE,
  INDEX `ACT_FK_IDL_PROCINST`(`PROC_INST_ID_`) USING BTREE,
  CONSTRAINT `act_ru_identitylink_ibfk_1` FOREIGN KEY (`PROC_DEF_ID_`) REFERENCES `act_re_procdef` (`ID_`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `act_ru_identitylink_ibfk_2` FOREIGN KEY (`PROC_INST_ID_`) REFERENCES `act_ru_execution` (`ID_`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `act_ru_identitylink_ibfk_3` FOREIGN KEY (`TASK_ID_`) REFERENCES `act_ru_task` (`ID_`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_bin ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for act_ru_job
-- ----------------------------
DROP TABLE IF EXISTS `act_ru_job`;
CREATE TABLE `act_ru_job`  (
  `ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
  `REV_` int(11) NULL DEFAULT NULL,
  `TYPE_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
  `LOCK_EXP_TIME_` timestamp(3) NULL DEFAULT NULL,
  `LOCK_OWNER_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `EXCLUSIVE_` tinyint(1) NULL DEFAULT NULL,
  `EXECUTION_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `PROCESS_INSTANCE_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `PROC_DEF_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `RETRIES_` int(11) NULL DEFAULT NULL,
  `EXCEPTION_STACK_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `EXCEPTION_MSG_` varchar(4000) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `DUEDATE_` timestamp(3) NULL DEFAULT NULL,
  `REPEAT_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `HANDLER_TYPE_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `HANDLER_CFG_` varchar(4000) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `TENANT_ID_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT '',
  PRIMARY KEY (`ID_`) USING BTREE,
  INDEX `ACT_FK_JOB_EXECUTION`(`EXECUTION_ID_`) USING BTREE,
  INDEX `ACT_FK_JOB_PROCESS_INSTANCE`(`PROCESS_INSTANCE_ID_`) USING BTREE,
  INDEX `ACT_FK_JOB_PROC_DEF`(`PROC_DEF_ID_`) USING BTREE,
  INDEX `ACT_FK_JOB_EXCEPTION`(`EXCEPTION_STACK_ID_`) USING BTREE,
  CONSTRAINT `act_ru_job_ibfk_1` FOREIGN KEY (`EXCEPTION_STACK_ID_`) REFERENCES `act_ge_bytearray` (`ID_`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `act_ru_job_ibfk_2` FOREIGN KEY (`EXECUTION_ID_`) REFERENCES `act_ru_execution` (`ID_`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `act_ru_job_ibfk_3` FOREIGN KEY (`PROCESS_INSTANCE_ID_`) REFERENCES `act_ru_execution` (`ID_`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `act_ru_job_ibfk_4` FOREIGN KEY (`PROC_DEF_ID_`) REFERENCES `act_re_procdef` (`ID_`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_bin ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for act_ru_suspended_job
-- ----------------------------
DROP TABLE IF EXISTS `act_ru_suspended_job`;
CREATE TABLE `act_ru_suspended_job`  (
  `ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
  `REV_` int(11) NULL DEFAULT NULL,
  `TYPE_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
  `EXCLUSIVE_` tinyint(1) NULL DEFAULT NULL,
  `EXECUTION_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `PROCESS_INSTANCE_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `PROC_DEF_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `RETRIES_` int(11) NULL DEFAULT NULL,
  `EXCEPTION_STACK_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `EXCEPTION_MSG_` varchar(4000) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `DUEDATE_` timestamp(3) NULL DEFAULT NULL,
  `REPEAT_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `HANDLER_TYPE_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `HANDLER_CFG_` varchar(4000) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `TENANT_ID_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT '',
  PRIMARY KEY (`ID_`) USING BTREE,
  INDEX `ACT_FK_SUSPENDED_JOB_EXECUTION`(`EXECUTION_ID_`) USING BTREE,
  INDEX `ACT_FK_SUSPENDED_JOB_PROCESS_INSTANCE`(`PROCESS_INSTANCE_ID_`) USING BTREE,
  INDEX `ACT_FK_SUSPENDED_JOB_PROC_DEF`(`PROC_DEF_ID_`) USING BTREE,
  INDEX `ACT_FK_SUSPENDED_JOB_EXCEPTION`(`EXCEPTION_STACK_ID_`) USING BTREE,
  CONSTRAINT `act_ru_suspended_job_ibfk_1` FOREIGN KEY (`EXCEPTION_STACK_ID_`) REFERENCES `act_ge_bytearray` (`ID_`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `act_ru_suspended_job_ibfk_2` FOREIGN KEY (`EXECUTION_ID_`) REFERENCES `act_ru_execution` (`ID_`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `act_ru_suspended_job_ibfk_3` FOREIGN KEY (`PROCESS_INSTANCE_ID_`) REFERENCES `act_ru_execution` (`ID_`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `act_ru_suspended_job_ibfk_4` FOREIGN KEY (`PROC_DEF_ID_`) REFERENCES `act_re_procdef` (`ID_`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_bin ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for act_ru_task
-- ----------------------------
DROP TABLE IF EXISTS `act_ru_task`;
CREATE TABLE `act_ru_task`  (
  `ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL DEFAULT '',
  `REV_` int(11) NULL DEFAULT NULL,
  `EXECUTION_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `PROC_INST_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `PROC_DEF_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `NAME_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `PARENT_TASK_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `DESCRIPTION_` varchar(4000) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `TASK_DEF_KEY_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `OWNER_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `ASSIGNEE_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `DELEGATION_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `PRIORITY_` int(11) NULL DEFAULT NULL,
  `CREATE_TIME_` timestamp(3) NULL DEFAULT NULL,
  `DUE_DATE_` datetime(3) NULL DEFAULT NULL,
  `CATEGORY_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `SUSPENSION_STATE_` int(11) NULL DEFAULT NULL,
  `TENANT_ID_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT '',
  `FORM_KEY_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `CLAIM_TIME_` datetime(3) NULL DEFAULT NULL,
  PRIMARY KEY (`ID_`) USING BTREE,
  INDEX `ACT_IDX_TASK_CREATE`(`CREATE_TIME_`) USING BTREE,
  INDEX `ACT_FK_TASK_EXE`(`EXECUTION_ID_`) USING BTREE,
  INDEX `ACT_FK_TASK_PROCINST`(`PROC_INST_ID_`) USING BTREE,
  INDEX `ACT_FK_TASK_PROCDEF`(`PROC_DEF_ID_`) USING BTREE,
  CONSTRAINT `act_ru_task_ibfk_1` FOREIGN KEY (`EXECUTION_ID_`) REFERENCES `act_ru_execution` (`ID_`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `act_ru_task_ibfk_2` FOREIGN KEY (`PROC_DEF_ID_`) REFERENCES `act_re_procdef` (`ID_`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `act_ru_task_ibfk_3` FOREIGN KEY (`PROC_INST_ID_`) REFERENCES `act_ru_execution` (`ID_`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_bin ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for act_ru_timer_job
-- ----------------------------
DROP TABLE IF EXISTS `act_ru_timer_job`;
CREATE TABLE `act_ru_timer_job`  (
  `ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
  `REV_` int(11) NULL DEFAULT NULL,
  `TYPE_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
  `LOCK_EXP_TIME_` timestamp(3) NULL DEFAULT NULL,
  `LOCK_OWNER_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `EXCLUSIVE_` tinyint(1) NULL DEFAULT NULL,
  `EXECUTION_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `PROCESS_INSTANCE_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `PROC_DEF_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `RETRIES_` int(11) NULL DEFAULT NULL,
  `EXCEPTION_STACK_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `EXCEPTION_MSG_` varchar(4000) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `DUEDATE_` timestamp(3) NULL DEFAULT NULL,
  `REPEAT_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `HANDLER_TYPE_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `HANDLER_CFG_` varchar(4000) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `TENANT_ID_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT '',
  PRIMARY KEY (`ID_`) USING BTREE,
  INDEX `ACT_FK_TIMER_JOB_EXECUTION`(`EXECUTION_ID_`) USING BTREE,
  INDEX `ACT_FK_TIMER_JOB_PROCESS_INSTANCE`(`PROCESS_INSTANCE_ID_`) USING BTREE,
  INDEX `ACT_FK_TIMER_JOB_PROC_DEF`(`PROC_DEF_ID_`) USING BTREE,
  INDEX `ACT_FK_TIMER_JOB_EXCEPTION`(`EXCEPTION_STACK_ID_`) USING BTREE,
  CONSTRAINT `act_ru_timer_job_ibfk_1` FOREIGN KEY (`EXCEPTION_STACK_ID_`) REFERENCES `act_ge_bytearray` (`ID_`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `act_ru_timer_job_ibfk_2` FOREIGN KEY (`EXECUTION_ID_`) REFERENCES `act_ru_execution` (`ID_`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `act_ru_timer_job_ibfk_3` FOREIGN KEY (`PROCESS_INSTANCE_ID_`) REFERENCES `act_ru_execution` (`ID_`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `act_ru_timer_job_ibfk_4` FOREIGN KEY (`PROC_DEF_ID_`) REFERENCES `act_re_procdef` (`ID_`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_bin ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for act_ru_variable
-- ----------------------------
DROP TABLE IF EXISTS `act_ru_variable`;
CREATE TABLE `act_ru_variable`  (
  `ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
  `REV_` int(11) NULL DEFAULT NULL,
  `TYPE_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
  `NAME_` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
  `EXECUTION_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `PROC_INST_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `TASK_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `BYTEARRAY_ID_` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `DOUBLE_` double NULL DEFAULT NULL,
  `LONG_` bigint(20) NULL DEFAULT NULL,
  `TEXT_` varchar(4000) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  `TEXT2_` varchar(4000) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL,
  PRIMARY KEY (`ID_`) USING BTREE,
  INDEX `ACT_IDX_VARIABLE_TASK_ID`(`TASK_ID_`) USING BTREE,
  INDEX `ACT_FK_VAR_EXE`(`EXECUTION_ID_`) USING BTREE,
  INDEX `ACT_FK_VAR_PROCINST`(`PROC_INST_ID_`) USING BTREE,
  INDEX `ACT_FK_VAR_BYTEARRAY`(`BYTEARRAY_ID_`) USING BTREE,
  CONSTRAINT `act_ru_variable_ibfk_1` FOREIGN KEY (`BYTEARRAY_ID_`) REFERENCES `act_ge_bytearray` (`ID_`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `act_ru_variable_ibfk_2` FOREIGN KEY (`EXECUTION_ID_`) REFERENCES `act_ru_execution` (`ID_`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `act_ru_variable_ibfk_3` FOREIGN KEY (`PROC_INST_ID_`) REFERENCES `act_ru_execution` (`ID_`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_bin ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for db_fhdb
-- ----------------------------
DROP TABLE IF EXISTS `db_fhdb`;
CREATE TABLE `db_fhdb`  (
  `FHDB_ID` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `USERNAME` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '操作用户',
  `BACKUP_TIME` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '备份时间',
  `TABLENAME` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '表名',
  `SQLPATH` varchar(300) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '存储位置',
  `TYPE` int(1) NOT NULL COMMENT '类型',
  `DBSIZE` varchar(10) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '文件大小',
  `BZ` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`FHDB_ID`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for db_timingbackup
-- ----------------------------
DROP TABLE IF EXISTS `db_timingbackup`;
CREATE TABLE `db_timingbackup`  (
  `TIMINGBACKUP_ID` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `JOBNAME` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '任务名称',
  `CREATE_TIME` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '创建时间',
  `TABLENAME` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '表名',
  `STATUS` int(1) NOT NULL COMMENT '类型',
  `FHTIME` varchar(30) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '时间规则',
  `TIMEEXPLAIN` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '规则说明',
  `BZ` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`TIMINGBACKUP_ID`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for im_fgroup
-- ----------------------------
DROP TABLE IF EXISTS `im_fgroup`;
CREATE TABLE `im_fgroup`  (
  `FGROUP_ID` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `NAME` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '组名',
  `USERNAME` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '用户名',
  PRIMARY KEY (`FGROUP_ID`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of im_fgroup
-- ----------------------------
INSERT INTO `im_fgroup` VALUES ('0b2944ca09a74c358267db61014ec49b', '同事', 'admin');
INSERT INTO `im_fgroup` VALUES ('6857275e88c4453188d23d13a70c49cf', '朋友', 'zhangsan');
INSERT INTO `im_fgroup` VALUES ('76c1b136283040888763ce3e663588ca', '同事', 'zhangsan');
INSERT INTO `im_fgroup` VALUES ('a56e46152024454991ab5ba552153624', '朋友', 'admin');

-- ----------------------------
-- Table structure for im_friends
-- ----------------------------
DROP TABLE IF EXISTS `im_friends`;
CREATE TABLE `im_friends`  (
  `FRIENDS_ID` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `USERNAME` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '用户名',
  `FUSERNAME` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '好友用户名',
  `CTIME` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '添加时间',
  `ALLOW` varchar(10) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '是否允许',
  `FGROUP_ID` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `DTIME` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `BZ` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  PRIMARY KEY (`FRIENDS_ID`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for im_hismsg
-- ----------------------------
DROP TABLE IF EXISTS `im_hismsg`;
CREATE TABLE `im_hismsg`  (
  `HISMSG_ID` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `USERNAME` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '发送者',
  `TOID` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '目标(好友或者群)',
  `TYPE` varchar(30) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '类型',
  `NAME` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '发送者姓名',
  `PHOTO` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '发送者头像',
  `CTIME` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '发送时间',
  `CONTENT` longtext CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '发送消息内容',
  `DREAD` varchar(10) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '是否已读',
  `OWNER` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  PRIMARY KEY (`HISMSG_ID`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for im_imstate
-- ----------------------------
DROP TABLE IF EXISTS `im_imstate`;
CREATE TABLE `im_imstate`  (
  `IMSTATE_ID` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `USERNAME` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '用户名',
  `ONLINE` varchar(10) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '在线',
  `AUTOGRAPH` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '个性签名',
  `SIGN` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  PRIMARY KEY (`IMSTATE_ID`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of im_imstate
-- ----------------------------
INSERT INTO `im_imstate` VALUES ('49548c35352d4337b4978a198bdce889', 'admin', 'online', 'I LOVE FH Admin', '4.jpg');
INSERT INTO `im_imstate` VALUES ('69ffe5039fbb4735aa5e163efcd4070b', 'zhangsan', 'online', 'I LOVE FH Admin', '5.jpg');

-- ----------------------------
-- Table structure for im_iqgroup
-- ----------------------------
DROP TABLE IF EXISTS `im_iqgroup`;
CREATE TABLE `im_iqgroup`  (
  `IQGROUP_ID` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `USERNAME` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '用户名',
  `QGROUPS` longtext CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '群的ID组合',
  PRIMARY KEY (`IQGROUP_ID`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of im_iqgroup
-- ----------------------------
INSERT INTO `im_iqgroup` VALUES ('88eda2d5922f44c5b05f5ad473a6c9ae', 'wangwu', '(');
INSERT INTO `im_iqgroup` VALUES ('9d3e104b17ad45f8997607101794cf06', 'admin', '(\'8ad1d56bb7d346eeb5cc9aef018edbd4\',\'909f5e6013394ad69eb4f194020d923e\',');
INSERT INTO `im_iqgroup` VALUES ('a6be4906ed724936ae20de35c27fa83e', 'zhangsan', '(');

-- ----------------------------
-- Table structure for im_qgroup
-- ----------------------------
DROP TABLE IF EXISTS `im_qgroup`;
CREATE TABLE `im_qgroup`  (
  `QGROUP_ID` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `NAME` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '群名称',
  `USERNAME` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '群主',
  `PHOTO` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '群头像',
  `CTIME` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`QGROUP_ID`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of im_qgroup
-- ----------------------------
INSERT INTO `im_qgroup` VALUES ('8ad1d56bb7d346eeb5cc9aef018edbd4', '朋友群', 'admin', 'uploadFiles/imgs/20190306/409e20d8034c4426969637f363843416.jpg', '2019-03-06 22:16:18');
INSERT INTO `im_qgroup` VALUES ('909f5e6013394ad69eb4f194020d923e', '同事群', 'admin', 'uploadFiles/imgs/20190307/5eb8133467044e9bbf3d060f88c0c096.jpg', '2019-03-07 03:22:18');

-- ----------------------------
-- Table structure for im_sysmsg
-- ----------------------------
DROP TABLE IF EXISTS `im_sysmsg`;
CREATE TABLE `im_sysmsg`  (
  `SYSMSG_ID` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `USERNAME` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '接收者用户名',
  `FROMUSERNAME` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '发送者用户名',
  `CTIME` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '操作时间',
  `REMARK` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '留言',
  `TYPE` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '类型',
  `CONTENT` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '事件内容',
  `ISDONE` varchar(30) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '是否完成',
  `DTIME` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '完成时间',
  `QGROUP_ID` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `DREAD` varchar(10) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  PRIMARY KEY (`SYSMSG_ID`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of im_sysmsg
-- ----------------------------
INSERT INTO `im_sysmsg` VALUES ('4ae0c2360e31460f933a4331df3e69f8', 'zhangsan', 'admin', '2019-02-24 21:37:42', '', 'friend', '您同意了系统管理员申请好友并加对方好友', 'yes', '2019-02-24 21:38:12', '', '1');
INSERT INTO `im_sysmsg` VALUES ('4d5c3663f81c40a1ae277bbee95ca6c6', 'kangxi', 'admin', '2018-11-20 01:59:01', '申请加好友', 'friend', '申请添加你为好友', 'no', '', '', '0');
INSERT INTO `im_sysmsg` VALUES ('529feabb0b994d0094f34c2e426085aa', 'zhangsan', 'admin', '2019-03-07 03:33:15', '申请加好友', 'friend', '您同意了系统管理员申请好友并加对方好友', 'yes', '2019-03-07 03:34:39', '', '1');
INSERT INTO `im_sysmsg` VALUES ('850ce8ae13d3489e9655e76dc98bf875', 'zhangsan', 'admin', '2019-03-07 04:02:55', '', 'group', '系统管理员 已经同意你申请加入群:同事群1', 'yes', '2019-03-07 04:02:55', '909f5e6013394ad69eb4f194020d923e', '0');
INSERT INTO `im_sysmsg` VALUES ('c398f3370336441b99bf77f8061215a1', 'qianlong', 'admin', '2019-02-23 05:29:23', '', 'friend', '申请添加你为好友', 'no', '', '', '0');

-- ----------------------------
-- Table structure for oa_myleave
-- ----------------------------
DROP TABLE IF EXISTS `oa_myleave`;
CREATE TABLE `oa_myleave`  (
  `MYLEAVE_ID` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `USERNAME` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '用户名',
  `TYPE` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '类型',
  `STARTTIME` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '开始时间',
  `ENDTIME` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '结束时间',
  `WHENLONG` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '时长',
  `REASON` varchar(1000) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '事由',
  PRIMARY KEY (`MYLEAVE_ID`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of oa_myleave
-- ----------------------------
INSERT INTO `oa_myleave` VALUES ('0d000bf0fcc94e8da6e7f9324770117a', 'admin', '事假', '2019 三月 09 周六 - 13:56', '2019 三月 09 周六 - 13:56', 'asdsad', 'asdasd');

-- ----------------------------
-- Table structure for sys_codeeditor
-- ----------------------------
DROP TABLE IF EXISTS `sys_codeeditor`;
CREATE TABLE `sys_codeeditor`  (
  `CODEEDITOR_ID` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `TYPE` varchar(30) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '类型',
  `FTLNMAME` varchar(30) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '文件名',
  `CTIME` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '创建时间',
  `CODECONTENT` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '代码',
  PRIMARY KEY (`CODEEDITOR_ID`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sys_createcode
-- ----------------------------
DROP TABLE IF EXISTS `sys_createcode`;
CREATE TABLE `sys_createcode`  (
  `CREATECODE_ID` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `PACKAGENAME` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '包名',
  `OBJECTNAME` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '类名',
  `TABLENAME` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '表名',
  `FIELDLIST` longtext CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '属性集',
  `CREATETIME` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '创建时间',
  `TITLE` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '描述',
  `FHTYPE` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  PRIMARY KEY (`CREATECODE_ID`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_createcode
-- ----------------------------
INSERT INTO `sys_createcode` VALUES ('f1181ac16cab4f73b62aec30af8d2851', 'stu', 'Student', 'TB_,fh,STUDENT', 'NAME,fh,String,fh,姓名,fh,是,fh,无,fh,255,fh,0,fh,nullQ313596790SEX,fh,String,fh,性别,fh,是,fh,无,fh,255,fh,0,fh,nullQ313596790', '2019-02-24 21:21:26', '学生信息', 'single');

-- ----------------------------
-- Table structure for sys_dictionaries
-- ----------------------------
DROP TABLE IF EXISTS `sys_dictionaries`;
CREATE TABLE `sys_dictionaries`  (
  `DICTIONARIES_ID` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `NAME` varchar(30) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '名称',
  `NAME_EN` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '英文',
  `BIANMA` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '编码',
  `ORDER_BY` int(11) NOT NULL COMMENT '排序',
  `PARENT_ID` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '上级ID',
  `BZ` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '备注',
  `TBSNAME` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '排查表',
  `TBFIELD` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `YNDEL` varchar(10) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  PRIMARY KEY (`DICTIONARIES_ID`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_dictionaries
-- ----------------------------
INSERT INTO `sys_dictionaries` VALUES ('00ef925d227444859eef2057693722ae', '达州', 'dazhou', '0032504', 4, 'd3538add7125404aba4b0007ef9fde50', '达州市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('010692054ac24eeebf5b8067f0f0521a', '安庆', 'anqing', '0030401', 1, '249999f296d14f95b8138a30bbb2c374', '安庆市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('023473e9e6204583a110531036357514', '山西', 'shanxi', '00323', 23, '1', '山西省', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('02e5eb8f50bd4824ad97427e2b372d14', '绥化', 'suihua', '0031312', 12, 'b2d4133b5dbf4599ada940620d2ab250', '绥化市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('035fe989f54742ac8b64b80b24213442', '来宾', 'laibin', '0030809', 9, 'c5f3d426c582410281f89f1451e1d854', '来宾市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('055273fe79f94e09a64698dab8d30ea8', '揭阳', 'jieyang', '0030708', 8, '0dd1f40bcb9d46aeba015dc19645a5b9', '揭阳市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('05ab2921b64d4f5c935c35228cc49ecb', '大同', 'datong', '0032302', 2, '023473e9e6204583a110531036357514', '大同市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('05ed855479d94b139c77ae82452bb39c', '涪陵区', 'fulingqu', '0033102', 2, '1c85fbd06cf840d093f3640aca1b6b2d', '涪陵区', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('076995f7d0034b32a94e0130d406d137', '湖州', 'huzhou', '0033002', 2, '6d846178376549ed878d11d109819f25', '湖州市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('076a163af6814f93954a543bd3b2fa4d', '广州', 'guangzhou', '0030704', 4, '0dd1f40bcb9d46aeba015dc19645a5b9', '广州市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('085ebd2776384eff842de8b61b781a7e', '潼南区', 'tongnanqu', '0033122', 22, '1c85fbd06cf840d093f3640aca1b6b2d', '潼南区', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('0953fe05e34642169c4cf24492b163b9', '湘西', 'xiangxi', '0031514', 14, 'c59f91630bef4289b71fcb2a48994582', '湘西市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('098bf5e3603e44889a2c4bb25e350400', '阿坝', 'a\'ba', '0032501', 1, 'd3538add7125404aba4b0007ef9fde50', '阿坝市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('0a2561ec256b4f46b4fa76c621256595', '鹤岗', 'hegang', '0031304', 4, 'b2d4133b5dbf4599ada940620d2ab250', '鹤岗市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('0a65f2ef68d54b7c8772e1d916684c4a', '岳阳', 'yueyang', '0031506', 6, 'c59f91630bef4289b71fcb2a48994582', '岳阳市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('0a754e878c304b99bf5d34a82ca3705c', '吉林', 'jilin', '0031604', 4, '857be71b0d6d4a40a2c83476824206d1', '吉林市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('0b08e52f2b264d0da66d37e718e32aba', '常德', 'changde', '0031507', 7, 'c59f91630bef4289b71fcb2a48994582', '常德市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('0c908137935946ac885cb56e55ff4f5d', '北碚区', 'beibeiqu', '0033109', 9, '1c85fbd06cf840d093f3640aca1b6b2d', '北碚区', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('0dba32de24014bcab807fd0fc51953aa', '北海', 'beihai', '0030802', 2, 'c5f3d426c582410281f89f1451e1d854', '北海市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('0dd1f40bcb9d46aeba015dc19645a5b9', '广东', 'guangdong', '00307', 7, '1', '广东省', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('0e18ab3edf5e43ee8737c156b0b50692', '吉安', 'ji\'an', '0031803', 3, 'cb3008cbd6ae4b5f8cebd2254ccb8603', '吉安市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('0e4f0359b4104a4e84723fda8f86cc37', '请假流程标识', 'Leave process identification', 'KEY_leave', 1, 'act002', '请假流程标识', '', '', 'yes');
INSERT INTO `sys_dictionaries` VALUES ('0eb279a28a0d43c7a075d58c6cfc3e02', '长寿区', 'changshouqu', '0033115', 15, '1c85fbd06cf840d093f3640aca1b6b2d', '长寿区', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('0efda23d751b42cb9472ca4f80cdf6c5', '德州', 'dezhou', '0030303', 3, '10f46a521ea0471f8d71ee75ac3b5f3a', '德州市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('0f975f2f51e245439b7d759f822a4a43', '嘉定区', 'jiadingqu', '0030210', 10, 'f1ea30ddef1340609c35c88fb2919bee', '嘉定区', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('1', '地区', 'area', '003', 3, '0', '地区', '', '', NULL);
INSERT INTO `sys_dictionaries` VALUES ('10a2b2b54bce432baf603c7fa4b45de0', '淮北', 'huaibei', '0030409', 9, '249999f296d14f95b8138a30bbb2c374', '淮北市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('10c14cd82df9496bb86c5681ddfb92fb', '本溪', 'benxi', '0031902', 2, 'b3366626c66c4b61881f09e1722e8495', '本溪市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('10f46a521ea0471f8d71ee75ac3b5f3a', '山东', 'shandong', '00303', 3, '1', '山东省', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('10f5278b19824877988e8baa5a1b58f4', '邯郸', 'handan', '0031104', 4, '75362368f22f4d60a810c2a45cced487', '邯郸市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('11ab8df614c14451bb08a91fbe05162e', '防城港', 'fangchenggang', '0030804', 4, 'c5f3d426c582410281f89f1451e1d854', '防城港市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('11fa0741880a45f09c81f0681acbc6dd', '办公审批', 'bangong', '00101', 1, 'act001', '办公审批', '', '', 'yes');
INSERT INTO `sys_dictionaries` VALUES ('12a62a3e5bed44bba0412b7e6b733c93', '北京', 'beijing', '00301', 1, '1', '北京', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('13b4d440cdd043378c2bbd0b797bc7b7', '黄石', 'huangshi', '0031404', 4, '312b80775e104ba08c8244a042a658df', '黄石市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('13e9e380abed4def837bea1671b92633', '大兴安岭', 'daxinganling', '0031302', 2, 'b2d4133b5dbf4599ada940620d2ab250', '大兴安岭市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('14452abafbef4cadbb05a5a74a61eb6f', '广安', 'guang\'an', '0032507', 7, 'd3538add7125404aba4b0007ef9fde50', '广安市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('158588bf48464dcca0e656968b8e09c0', '密云区', 'miyunqu', '0030115', 15, '12a62a3e5bed44bba0412b7e6b733c93', '密云区', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('159d49075827476490aee58956fb159c', '潜江', 'qianjiang', '0031406', 6, '312b80775e104ba08c8244a042a658df', '潜江市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('15da226f465b4dac95c8333fd3d81747', '淮安', 'huaian', '0031702', 2, '577405ff648240959b3765c950598ab0', '淮安市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('16535e38a2534f4781353e7570831ea1', '丽江', 'lijiang', '0032909', 9, '510607a1836e4079b3103e14ec5864ed', '丽江', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('165fd1c02d98439d8d7cc2e81def88d6', '陇南', 'longnan', '0030609', 9, '3283f1a77180495f9a0b192d0f9cdd35', '陇南市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('16a1eb63489e4d28827fc16a90e2ed61', '贵港', 'guigang', '0030805', 5, 'c5f3d426c582410281f89f1451e1d854', '贵港市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('1895a514cda74329817bce6a5fe918f4', '济源', 'jiyuan', '0031203', 3, '7336944efb4b40fcae9118fc9a970d2d', '济源市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('1929f99821f2484fa33991233c1555e9', '大理', 'dali', '0032904', 4, '510607a1836e4079b3103e14ec5864ed', '大理', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('192a36eb3d234a909e339c06b9cf723a', '许昌', 'xuchang', '0031215', 15, '7336944efb4b40fcae9118fc9a970d2d', '许昌市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('19cf8222eac9457280ebb40f14052590', '南岸区', 'nan\'anqu', '0033108', 8, '1c85fbd06cf840d093f3640aca1b6b2d', '南岸区', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('1a99e7e302ce4f24b4b5d5d4b20a75fe', '清远', 'qingyuan', '0030711', 11, '0dd1f40bcb9d46aeba015dc19645a5b9', '清远市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('1ac809034f3d471592a5c74e19c7f1bc', '烟台', 'yantai', '0030315', 15, '10f46a521ea0471f8d71ee75ac3b5f3a', '烟台市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('1b65dd2a2057489c9598c789b4114d24', '孝感', 'xiaogan', '0031415', 15, '312b80775e104ba08c8244a042a658df', '孝感市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('1b8b44f0c4384a09987ab0c5d9ceedd2', '神农架林区', 'shennongjialinqu', '0031407', 7, '312b80775e104ba08c8244a042a658df', '神农架林区', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('1c4313525bdc4d4b9f7849dfb614cfb3', '柳州', 'liuzhou', '0030810', 10, 'c5f3d426c582410281f89f1451e1d854', '柳州市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('1c85fbd06cf840d093f3640aca1b6b2d', '重庆', 'chongqing', '00331', 31, '1', '重庆', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('1cf92384c7ee46faa91acface462b32f', '沧州', 'cangzhou', '0031102', 2, '75362368f22f4d60a810c2a45cced487', '沧州市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('1d0deff7da2745cc960cfa9ae07bdd13', '抚顺', 'fushun', '0031906', 6, 'b3366626c66c4b61881f09e1722e8495', '抚顺市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('1d6d2d9af05849da9807d4cba0144695', '南通', 'nantong', '0031705', 5, '577405ff648240959b3765c950598ab0', '南通市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('1e228d052ec24bb7ba64524f51689cef', '鄂尔多斯', 'eerduosi', '0032005', 5, 'c072c248c7ab47dda7bf24f5e577925c', '鄂尔多斯市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('1e429ce404794a30aad09bc592d0f5ce', '荆门', 'jingmen', '0031405', 5, '312b80775e104ba08c8244a042a658df', '荆门市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('1e89ca839dbf46a3bc8c02b7d55802c5', '长沙', 'changsha', '0031501', 1, 'c59f91630bef4289b71fcb2a48994582', '长沙市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('2087851693514e3c9c98fd843fb5b32c', '河西区', 'hexiqu', '0032603', 3, '2c254799d3454f2cbc338ef5712548e9', '河西区', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('20a08ca32856488dad122529f901fb9b', '固原', 'guyuan', '0032101', 1, '5690b0534fe745e5ba0f504f0c260559', '固原市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('20f6d6c3b3234c21b52755ab6b690ffe', '杭州', 'hangzhou', '0033001', 1, '6d846178376549ed878d11d109819f25', '杭州市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('212dbe5474304ad8b5f6e6049a72da46', '包头', 'baotou', '0032003', 3, 'c072c248c7ab47dda7bf24f5e577925c', '包头市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('217c993dce9544c89279e88bdd60e7a8', '黄冈', 'huanggang', '0031403', 3, '312b80775e104ba08c8244a042a658df', '黄冈市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('22ef24142b0a4d6e9f05582e3c8790a9', '济南', 'jinan', '0030301', 1, '10f46a521ea0471f8d71ee75ac3b5f3a', '济南市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('2303cab27d704a259d7b0c42a687f3db', '马鞍山', 'maanshan', '0030413', 13, '249999f296d14f95b8138a30bbb2c374', '马鞍山市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('23aff8da2e6c4513be3155f372c45046', '石嘴山', 'shizuishan', '0032103', 3, '5690b0534fe745e5ba0f504f0c260559', '石嘴山市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('23caa037fe8c4283b7a7fc42da4a45a9', '武威', 'wuwei', '0030613', 13, '3283f1a77180495f9a0b192d0f9cdd35', '武威市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('23f71d660bb94d239dde6738b73d3905', '延边', 'yanbian', '0031609', 9, '857be71b0d6d4a40a2c83476824206d1', '延边市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('24007511f8ec42da8c6555305afe56ce', '青岛', 'qindao', '0030310', 10, '10f46a521ea0471f8d71ee75ac3b5f3a', '青岛市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('2431e2f471624fd9bf0f76b7049b1296', '驻马店', 'zhumadian', '0031218', 18, '7336944efb4b40fcae9118fc9a970d2d', '驻马店市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('244ffa16c2cd4594af2dfed2f7257d24', '德阳', 'deyang', '0032505', 5, 'd3538add7125404aba4b0007ef9fde50', '德阳市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('2477f3e5e2c94c73844b060d9dc82316', '深圳', 'shenzhen', '0030715', 15, '0dd1f40bcb9d46aeba015dc19645a5b9', '深圳市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('249999f296d14f95b8138a30bbb2c374', '安徽', 'anhui', '00304', 4, '1', '安徽省', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('25892ce9cba1429fb1b45d4aaeaf3bca', '石家庄', 'shijiazhuang', '0031108', 8, '75362368f22f4d60a810c2a45cced487', '石家庄市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('2627d3e9f98a4cdfbe0f59a4c5d3772a', '白银', 'baiyin', '0030601', 1, '3283f1a77180495f9a0b192d0f9cdd35', '白银市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('26a79d023ad7483194241cddf97f3689', '莱芜', 'laiwu', '0030307', 7, '10f46a521ea0471f8d71ee75ac3b5f3a', '莱芜市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('26b093ae7635474d8da8162efe7e4035', '合川区', 'hechuanqu', '0033117', 17, '1c85fbd06cf840d093f3640aca1b6b2d', '合川区', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('26d4e79797d34b11b58eb12e5c0c55ae', '抚州', 'fuzhou', '0031801', 1, 'cb3008cbd6ae4b5f8cebd2254ccb8603', '抚州市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('273f2c545056473abaf320327073b48b', '无锡', 'wuxi', '0031709', 9, '577405ff648240959b3765c950598ab0', '无锡市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('27927fbc83154894b096221da15b326a', '保定', 'baoding', '0031101', 1, '75362368f22f4d60a810c2a45cced487', '保定市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('287baf1c903444359971b0ce8d58dce2', '普洱', 'pu\'er', '0032912', 12, '510607a1836e4079b3103e14ec5864ed', '普洱', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('29a4dbca082b49078af67caf5fd28f4f', '漳州', 'zhangzhou', '0030509', 9, 'd4066f6f425a4894a77f49f539f2a34f', '漳州市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('2a4c3b9f024743d19907b36ab4a43499', '宣城', 'xuancheng', '0030417', 17, '249999f296d14f95b8138a30bbb2c374', '宣城市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('2a4f0cb0748645bab53b94b62412df04', '黔西南', 'qianxinan', '0030907', 7, '592f6fcf45a74524aa8ea853fc9761d5', '黔西南市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('2ae081dd5d3c47b584fdaf24769e49e1', '玉溪', 'yuxi', '0032915', 15, '510607a1836e4079b3103e14ec5864ed', '玉溪', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('2ba8e6d0fd944983aa19b781c6b53477', '海南', 'hainan', '00310', 10, '1', '海南省', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('2bd0431e3566451297ecd194287a878a', '甘孜', 'ganzi', '0032506', 6, 'd3538add7125404aba4b0007ef9fde50', '甘孜', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('2c0da3154cc74d7990c597bed6ebf2d6', '大兴区', 'daxingqu', '0030112', 12, '12a62a3e5bed44bba0412b7e6b733c93', '大兴区', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('2c254799d3454f2cbc338ef5712548e9', '天津', 'tianjin', '00326', 26, '1', '天津', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('2d0e4687904b48738ac6bd6a42e7f32d', '忻州', 'xinzhou', '0032309', 9, '023473e9e6204583a110531036357514', '忻州市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('2d0ff92556b544c19dbfc8b8b055e19a', '常州', 'changzhou', '0031701', 1, '577405ff648240959b3765c950598ab0', '常州市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('2e3c279cf0a44115869049e4a6d9ed08', '西宁', 'xining', '0032207', 7, '5a80e3435c0e4dc09bafceeadb38e5f0', '西宁', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('2f097a8914de4b01a04bf61852435672', '阿勒泰', 'a\'letai', '0032802', 2, '2fabed91c6d94e698ed449165cd250ca', '阿勒泰', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('2f5e433682f24e20b600532062ff0bcb', '白山', 'baishan', '0031602', 2, '857be71b0d6d4a40a2c83476824206d1', '白山市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('2f7f68eb9be845be90e74a0763de2c7f', '平谷区', 'pingguqu', '0030114', 14, '12a62a3e5bed44bba0412b7e6b733c93', '平谷区', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('2f8e7a55eaab4649b9abe43ade744e58', '大庆', 'daqing', '0031301', 1, 'b2d4133b5dbf4599ada940620d2ab250', '大庆市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('2fabed91c6d94e698ed449165cd250ca', '新疆', 'xinjiang', '00328', 28, '1', '新疆', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('30d424f63bf44e8391683f371ed3552f', '秦皇岛', 'qinhuangdao', '0031107', 7, '75362368f22f4d60a810c2a45cced487', '秦皇岛市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('312b80775e104ba08c8244a042a658df', '湖北', 'hubei', '00314', 14, '1', '湖北省', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('3186f859efa246f793401c475d3d0090', '阿里', 'a\'li', '0032701', 1, '3e846b08dbbe495e93bc93f8f202de79', '阿里', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('325e45e49c1849efb7fea2296f686210', '赣州', 'ganzhou', '0031802', 2, 'cb3008cbd6ae4b5f8cebd2254ccb8603', '赣州市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('3283f1a77180495f9a0b192d0f9cdd35', '甘肃', 'gansu', '00306', 6, '1', '甘肃省', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('329838f633f340779483910f33387ccd', '通州区', 'tongzhouqu', '0030109', 9, '12a62a3e5bed44bba0412b7e6b733c93', '通州区', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('3326f321dfe54e5292e94a9f2a518723', '乌鲁木齐', 'wulumuqi', '0032812', 12, '2fabed91c6d94e698ed449165cd250ca', '乌鲁木齐', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('33fe30734ed84994bdd46ebe69aac088', '朝阳区', 'chaoyangqu', '0030103', 3, '12a62a3e5bed44bba0412b7e6b733c93', '朝阳区', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('34bc05269e304e2e99c9ded314a12321', '石景山区', 'shijingshanqu', '0030105', 5, '12a62a3e5bed44bba0412b7e6b733c93', '石景山区', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('34d6634b8baa47a3b1c0d0346d93873b', '和平区', 'hepingqu', '0032601', 1, '2c254799d3454f2cbc338ef5712548e9', '和平区', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('3521e41344aa42aaa1cd212482992055', '漯河', 'luohe', '0031207', 7, '7336944efb4b40fcae9118fc9a970d2d', '漯河市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('366bc947374d4826b09882d387b880ff', '病假', 'sick leave', '00404', 4, '6d30b170d4e348e585f113d14a4dd96d', '病假', '', '', 'no');
INSERT INTO `sys_dictionaries` VALUES ('38bc876c1cab4434af9d14be194463c8', '南充', 'nanchong', '0032514', 14, 'd3538add7125404aba4b0007ef9fde50', '南充市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('39595ea4b765445dae9c26ae870b3a0f', '克州', 'kezhou', '0032809', 9, '2fabed91c6d94e698ed449165cd250ca', '克州', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('3a3b4ea7445a4aec80083e5957028990', '汕头', 'shantou', '0030712', 12, '0dd1f40bcb9d46aeba015dc19645a5b9', '汕头市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('3a5d1b6e800541c683724672cae3e0f6', '泰安', 'taian', '0030312', 12, '10f46a521ea0471f8d71ee75ac3b5f3a', '泰安市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('3ad7c52e9a7044a1a9ab00f29f8cef7c', '阿克苏', 'a\'kesu', '0032801', 1, '2fabed91c6d94e698ed449165cd250ca', '阿克苏', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('3ae7c64c40c147eeb3898883e20a7fe0', '山南', 'shannan', '0032707', 7, '3e846b08dbbe495e93bc93f8f202de79', '山南', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('3d2a8f11e6d345b5af2f8e5d8bb6bb7a', '鹰潭', 'yingtan', '0031811', 11, 'cb3008cbd6ae4b5f8cebd2254ccb8603', '鹰潭市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('3dbed4444dfc4884ab57d769ceac9507', '松江区', 'songjiangqu', '0030213', 13, 'f1ea30ddef1340609c35c88fb2919bee', '松江区', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('3e846b08dbbe495e93bc93f8f202de79', '西藏', 'xizang', '00327', 27, '1', '西藏', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('3eed1c5fb2c9420dbe6e76fdb0f9c4cb', '眉山', 'meishan', '0032511', 11, 'd3538add7125404aba4b0007ef9fde50', '眉山市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('3f7c5e8ba51849549f5e5dfee72566cc', '呼伦贝尔', 'hulunbeier', '0032007', 7, 'c072c248c7ab47dda7bf24f5e577925c', '呼伦贝尔市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('3fb6c72b11124211a22d9f8f40715737', '辽阳', 'liaoyang', '0031910', 10, 'b3366626c66c4b61881f09e1722e8495', '辽阳市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('3fffacbb502d4647bd358ff00412f536', '长治', 'changzhi', '0032301', 1, '023473e9e6204583a110531036357514', '长治市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('407fa7f152f4461582cfd6904b2c454a', '晋城', 'jincheng', '0032303', 3, '023473e9e6204583a110531036357514', '晋城市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('415fe5fbf3054a3ea2ebdbe24ce4c49f', '永川区', 'yongchuanqu', '0033118', 18, '1c85fbd06cf840d093f3640aca1b6b2d', '永川区', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('417d25314a9e43c6b7b725db160db360', '延安', 'yan\'an', '0032409', 9, '534850c72ceb4a57b7dc269da63c330a', '延安市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('4266f08d4bc24321bba7ea3a83a8ba95', '永州', 'yongzhou', '0031511', 11, 'c59f91630bef4289b71fcb2a48994582', '永州市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('429dc62e0f6641b8b2ddced272d5d087', '遵义', 'zunyi', '0030909', 9, '592f6fcf45a74524aa8ea853fc9761d5', '遵义市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('432c0a1be10143beba6de15ad8811b64', '安顺', 'anshun', '0030901', 1, '592f6fcf45a74524aa8ea853fc9761d5', '安顺市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('44fee1b9a9e141f9bdf90053f4972d2e', '北辰区', 'beichenqu', '0032610', 10, '2c254799d3454f2cbc338ef5712548e9', '北辰区', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('458411aef2d34ccd99ab5976f0f1f030', '金华', 'jinhua', '0033004', 4, '6d846178376549ed878d11d109819f25', '金华市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('45a5be4b22ec494c99b112a7c96bca47', '钦州', 'qinzhou', '0030812', 12, 'c5f3d426c582410281f89f1451e1d854', '钦州市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('4795a00ae89441ce82bcabdf086e8316', '宝鸡', 'baoji', '0032402', 2, '534850c72ceb4a57b7dc269da63c330a', '宝鸡市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('480875fd43a947119e24c2302eeead53', '伊春', 'yichun', '0031313', 13, 'b2d4133b5dbf4599ada940620d2ab250', '伊春市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('4921e0e6f9d445cdb6a4b3da98ab3555', '林芝', 'linzhi', '0032704', 4, '3e846b08dbbe495e93bc93f8f202de79', '林芝', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('4972af008f074efd91ea8312587afb42', '牡丹江', 'mudanjiang', '0031308', 8, 'b2d4133b5dbf4599ada940620d2ab250', '牡丹江市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('49b4639e83e441c581bfdefda3a9ac27', '宜春', 'yichun', '0031810', 10, 'cb3008cbd6ae4b5f8cebd2254ccb8603', '宜春市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('4b41fbe4cdae414a91af371e3105ebe5', '景德镇', 'jingdezhen', '0031804', 4, 'cb3008cbd6ae4b5f8cebd2254ccb8603', '景德镇市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('4b72f022312e4664ae7863b343239ff0', '淄博', 'zibo', '0030317', 17, '10f46a521ea0471f8d71ee75ac3b5f3a', '淄博市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('4be3e0a560a2486eae928b44110e971e', '白城', 'baicheng', '0031601', 1, '857be71b0d6d4a40a2c83476824206d1', '白城市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('4c5dbcb293bf40f8837c0acec1ad67eb', '佳木斯', 'jiamusi', '0031307', 7, 'b2d4133b5dbf4599ada940620d2ab250', '佳木斯市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('4cee922697a64ec78de69210e8e40af1', '昌都', 'changdu', '0032702', 2, '3e846b08dbbe495e93bc93f8f202de79', '昌都', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('50b42656af3f4068984fa042e81d7d22', '威海', 'weihai', '0030313', 13, '10f46a521ea0471f8d71ee75ac3b5f3a', '威海市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('510607a1836e4079b3103e14ec5864ed', '云南', 'yunnan', '00329', 29, '1', '云南省', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('512a316326984ed8aa32d7f610b7604f', '玉树', 'yushu', '0032208', 8, '5a80e3435c0e4dc09bafceeadb38e5f0', '玉树', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('51f23688604848809184ec828f89cfca', '嘉兴', 'jiaxing', '0033003', 3, '6d846178376549ed878d11d109819f25', '嘉兴市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('5259c4093aa84f7c88a367685581fbc6', '门头沟区', 'mentougouqu', '0030107', 7, '12a62a3e5bed44bba0412b7e6b733c93', '门头沟区', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('52630830669149edba48a7bb9b06e297', '中卫', 'zhongwei', '0032106', 6, '5690b0534fe745e5ba0f504f0c260559', '中卫市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('534850c72ceb4a57b7dc269da63c330a', '陕西', 'shanxi', '00324', 24, '1', '陕西省', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('537974fdf5f54b8f99452bb8a03cf37b', '厦门', 'xiamen', '0030508', 8, 'd4066f6f425a4894a77f49f539f2a34f', '厦门市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('53ca3f5aede8420b835b38bbc542da81', '松源', 'songyuan', '0031607', 7, '857be71b0d6d4a40a2c83476824206d1', '松源市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('558d920174014b4cb5a0c8f518b5819b', '东城区', 'dongchengqu', '0030101', 1, '12a62a3e5bed44bba0412b7e6b733c93', '东城区', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('56103b0e83a44d07a6025061fab4cebc', '运城', 'yuncheng', '0032311', 11, '023473e9e6204583a110531036357514', '运城市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('5690b0534fe745e5ba0f504f0c260559', '宁夏', 'ningxia', '00321', 21, '1', '宁夏', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('576fa3bd7d294f61af1315e95f70d44c', '九龙坡区', 'jiulongpoqu', '0033107', 7, '1c85fbd06cf840d093f3640aca1b6b2d', '九龙坡区', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('577405ff648240959b3765c950598ab0', '江苏', 'jiangsu', '00317', 17, '1', '江苏省', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('58ed29aefce044339ecf067f514c43cb', '宁德', 'ningde', '0030504', 4, 'd4066f6f425a4894a77f49f539f2a34f', '宁德市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('5908fbf750a347b8a6b82af7778bb866', '商洛', 'shangluo', '0032404', 4, '534850c72ceb4a57b7dc269da63c330a', '商洛市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('592f6fcf45a74524aa8ea853fc9761d5', '贵州', 'guizhou', '00309', 9, '1', '贵州省', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('59dbb93b007d44a6ae97744ac14e642c', '龙岩', 'longyan', '0030502', 2, 'd4066f6f425a4894a77f49f539f2a34f', '龙岩市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('5a80e3435c0e4dc09bafceeadb38e5f0', '青海', 'qinghai', '00322', 22, '1', '青海', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('5b5747166f714882b5cdfbeb7856f965', '周口', 'zhoukou', '0031217', 17, '7336944efb4b40fcae9118fc9a970d2d', '周口市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('5c89402bd4a34028b9c61d4f7ebb7876', '其它', 'Other', '00407', 7, '6d30b170d4e348e585f113d14a4dd96d', '其它', '', '', 'no');
INSERT INTO `sys_dictionaries` VALUES ('5c91965168eb4deaab99266bbb4b64e1', '朝阳', 'chaoyang', '0031903', 3, 'b3366626c66c4b61881f09e1722e8495', '朝阳市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('5d2d367b0aee49449e65d0dd7601ee29', '巴中', 'bazhong', '0032502', 2, 'd3538add7125404aba4b0007ef9fde50', '巴中市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('5ddde49610d4433eb157897d01ba6838', '东莞', 'dongguan', '0030702', 2, '0dd1f40bcb9d46aeba015dc19645a5b9', '东莞市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('5e85d09db1054472bcca22d82da6ec5d', '乐山', 'leshan', '0032509', 9, 'd3538add7125404aba4b0007ef9fde50', '乐山市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('603fe04b11704a0eb7e20b97c008c5f5', '行政审批', 'xingzheng', '00102', 2, 'act001', '行政审批', '', '', 'yes');
INSERT INTO `sys_dictionaries` VALUES ('60c96ec0debf4cc4bf5974c93d4f638c', '阳泉', 'yangquan', '0032310', 10, '023473e9e6204583a110531036357514', '阳泉市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('61c3711334fe44b0806e717e6fb238b0', '滨海新区', 'binhaixinqu', '0032613', 13, '2c254799d3454f2cbc338ef5712548e9', '滨海新区', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('6214f40ad2704fb6af0ded59420ca958', '安康', 'ankang', '0032401', 1, '534850c72ceb4a57b7dc269da63c330a', '安康市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('62ed6854726c4674ae2f5b676ddec7fb', '新乡', 'xinxiang', '0031213', 13, '7336944efb4b40fcae9118fc9a970d2d', '新乡市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('6303e3eeffb441018044b039063e3f96', '丹东', 'dandong', '0031905', 5, 'b3366626c66c4b61881f09e1722e8495', '丹东市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('644316de71f942f9a90eb1f810eca872', '怀柔区', 'huairouqu', '0030113', 13, '12a62a3e5bed44bba0412b7e6b733c93', '怀柔区', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('64a03236dd084d09ada9a1ca22b3815f', '长春', 'changchun', '0031603', 3, '857be71b0d6d4a40a2c83476824206d1', '长春市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('65629a05de764a19b66b752726f5cdbd', '海淀区', 'haidianqu', '0030106', 6, '12a62a3e5bed44bba0412b7e6b733c93', '海淀区', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('6567a081f5d142779e17edbda3da9a04', '宁波', 'ningbo', '0033006', 6, '6d846178376549ed878d11d109819f25', '宁波市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('6624ad3b318149f3a8ee5beef1b8b38f', '肇庆', 'zhaoqing', '0030719', 19, '0dd1f40bcb9d46aeba015dc19645a5b9', '肇庆市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('674ec37e9641450dadc9798df10c58bc', '静安区', 'jinganqu', '0030204', 4, 'f1ea30ddef1340609c35c88fb2919bee', '静安区', '', '', NULL);
INSERT INTO `sys_dictionaries` VALUES ('67cba9a4ca4c4c38ac3ba2c21dd191e6', '南昌', 'nanchang', '0031806', 6, 'cb3008cbd6ae4b5f8cebd2254ccb8603', '南昌市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('68a8f7a8337141d3a092fadfd2457970', '鸡西', 'jixi', '0031306', 6, 'b2d4133b5dbf4599ada940620d2ab250', '鸡西市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('6932b6b7b5124bef8385fb8e5b5c2568', '通化', 'tonghua', '0031608', 8, '857be71b0d6d4a40a2c83476824206d1', '通化市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('694348f8e1c0444e8e0b2c2caf4de1a6', '榆林', 'yulin', '0032410', 10, '534850c72ceb4a57b7dc269da63c330a', '榆林市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('697141b58ada46518bc8ec0cc3d64b31', '博州', 'bozhou', '032804', 4, '2fabed91c6d94e698ed449165cd250ca', '博州', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('69ba7746763049d4a97de39a8d9db697', '年假', 'annual leave', '00406', 6, '6d30b170d4e348e585f113d14a4dd96d', '年假', '', '', 'no');
INSERT INTO `sys_dictionaries` VALUES ('6a2226c73bc745faa6973dd3af3e274a', '吐鲁番', 'tulufan', '0032811', 11, '2fabed91c6d94e698ed449165cd250ca', '吐鲁番', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('6b2b1d55b06b44cd8a487d327397b69b', '徐汇区', 'xuhuiqu', '0030202', 2, 'f1ea30ddef1340609c35c88fb2919bee', '徐汇区', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('6d1e9b9c9c334448878312d589eeaeac', '荣昌区', 'rongchangqu', '0033123', 23, '1c85fbd06cf840d093f3640aca1b6b2d', '荣昌区', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('6d30b170d4e348e585f113d14a4dd96d', '请假类型', 'Type of ask for leave', '004', 4, '0', '请假类型', '', '', 'yes');
INSERT INTO `sys_dictionaries` VALUES ('6d846178376549ed878d11d109819f25', '浙江', 'zhejiang', '00330', 30, '1', '浙江省', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('6daab50a4a1048f993f348a66dcfa83d', '喀什', 'kashi', '0032807', 7, '2fabed91c6d94e698ed449165cd250ca', '喀什', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('6dc38f66c86a4f32ac9d585e668c110e', '怒江', 'nujiang', '0032911', 11, '510607a1836e4079b3103e14ec5864ed', '怒江', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('6e1f1c6d82704e5cadcd0bc8ef2ab79f', '昆明', 'kunming', '0032908', 8, '510607a1836e4079b3103e14ec5864ed', '昆明', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('6e639147d90943c38490cafe223985ce', '梅州', 'meizhou', '0030710', 10, '0dd1f40bcb9d46aeba015dc19645a5b9', '梅州市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('6e67518f1da44dbaa8cf95920779f188', '汕尾', 'shanwei', '0030713', 13, '0dd1f40bcb9d46aeba015dc19645a5b9', '汕尾市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('6eaa823330da42b6b5783e389707853c', '四平', 'siping', '0031606', 6, '857be71b0d6d4a40a2c83476824206d1', '四平市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('6ecc40e527404bba89207cc158ef3994', '河源', 'heyuan', '0030705', 5, '0dd1f40bcb9d46aeba015dc19645a5b9', '河源市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('6ee20f02066e43a29f10ca6dbd0b7c71', '普陀区', 'putuoqu', '0030205', 5, 'f1ea30ddef1340609c35c88fb2919bee', '普陀区', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('6f5749ab2b5d4cbea1655e9a5197096d', '湛江', 'zhanjiang', '0030718', 18, '0dd1f40bcb9d46aeba015dc19645a5b9', '湛江市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('6f8c18c8b3a54bc287c1dfc5642be577', '三明', 'sanming', '0030507', 7, 'd4066f6f425a4894a77f49f539f2a34f', '三明市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('6f9601270aca46519e7f8836e0d2446c', '广元', 'guangyuan', '0032508', 8, 'd3538add7125404aba4b0007ef9fde50', '广元市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('6fd083505ab24086b234c032dab3c2a7', '海口', 'haikou', '0031001', 1, '2ba8e6d0fd944983aa19b781c6b53477', '海口市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('70733399b60d4b058c255fa9fff2eee0', '莆田', 'putian', '0030505', 5, 'd4066f6f425a4894a77f49f539f2a34f', '莆田市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('70c1525174a04767865d0e6b7ed01f5a', '七台河', 'qitaihe', '0031309', 9, 'b2d4133b5dbf4599ada940620d2ab250', '七台河市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('70c41ec5cb9e4aec98bd2357702c4082', '江门', 'jiangmen', '0030707', 7, '0dd1f40bcb9d46aeba015dc19645a5b9', '江门市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('715a834c60734d498ec6043eaa1c0f12', '产假', 'maternity leave', '00403', 3, '6d30b170d4e348e585f113d14a4dd96d', '产假', '', '', 'no');
INSERT INTO `sys_dictionaries` VALUES ('71aba068cd5b4588a03be75e2e49f496', '鄂州', 'ezhou', '0031401', 1, '312b80775e104ba08c8244a042a658df', '鄂州市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('7336944efb4b40fcae9118fc9a970d2d', '河南', 'henan', '00312', 12, '1', '河南省', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('748623f3282b4ca7ace0e73303327310', '巴南区', 'bananqu', '0033113', 13, '1c85fbd06cf840d093f3640aca1b6b2d', '巴南区', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('74d2aaddaf294355b01970d52e303a1a', '资阳', 'ziyang', '0032519', 19, 'd3538add7125404aba4b0007ef9fde50', '资阳市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('75362368f22f4d60a810c2a45cced487', '河北', 'hebei', '00311', 11, '1', '河北省', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('755e2d15540c49dbad6be564f694a4af', '曲靖', 'qujing', '0032913', 13, '510607a1836e4079b3103e14ec5864ed', '曲靖', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('75b889c8e86c4d518a1fb74b089ceae8', '攀枝花', 'panzhihua', '0032515', 15, 'd3538add7125404aba4b0007ef9fde50', '攀枝花市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('75e0334ad60b41a1b42ae6724b06c874', '镇江', 'zhenjiang', '0031713', 13, '577405ff648240959b3765c950598ab0', '镇江市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('762bc3b1443e4ea98ab051b4007c0238', '邢台', 'xingtai', '0031110', 10, '75362368f22f4d60a810c2a45cced487', '邢台市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('76d023f160e448c8bcb78598bf246a44', '黔南', 'qiannan', '0030906', 6, '592f6fcf45a74524aa8ea853fc9761d5', '黔南市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('773cb4f25b9d4ebcba2953570da776c9', '吴忠', 'wuzhong', '0032104', 4, '5690b0534fe745e5ba0f504f0c260559', '吴忠市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('776b55acb6804296a00c9f97723633ba', '舟山', 'zhoushan', '0033011', 11, '6d846178376549ed878d11d109819f25', '舟山市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('79b98f7f0c054fa0ab2a3a2cb75d1b87', '邵阳', 'shaoyang', '0031505', 5, 'c59f91630bef4289b71fcb2a48994582', '邵阳市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('7a8097646dc8419284201db66dd6eda1', '芜湖', 'wuhu', '0030416', 16, '249999f296d14f95b8138a30bbb2c374', '芜湖市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('7ab1618b70354ee2ab49e8fd5cbca27f', '哈密', 'hami', '0032806', 6, '2fabed91c6d94e698ed449165cd250ca', '哈密', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('7bed9f7f137e4048bbfd0d564283312d', '日照', 'rizhao', '0030311', 11, '10f46a521ea0471f8d71ee75ac3b5f3a', '日照市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('7c6addc8becd4e759479228f6dd38bb2', '通辽', 'tongliao', '0032008', 8, 'c072c248c7ab47dda7bf24f5e577925c', '通辽市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('7f69cfd9e1ae4c92b4ddf13b9f78cb6c', '保山', 'baoshan', '0032902', 2, '510607a1836e4079b3103e14ec5864ed', '保山', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('802ef5c62bbb47e3a026e3c92989f53e', '临夏', 'linxia', '0030608', 8, '3283f1a77180495f9a0b192d0f9cdd35', '临夏市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('8114568fa8a34c6e878ff13d5ba59006', '齐齐哈尔', 'qiqihaer', '0031310', 10, 'b2d4133b5dbf4599ada940620d2ab250', '齐齐哈尔市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('8127fbeb13a44c3284dfa8e2326ce19a', '茂名', 'maoming', '0030709', 9, '0dd1f40bcb9d46aeba015dc19645a5b9', '茂名市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('813ff1376c0445c6a64b9f00452c2427', '成都', 'chengdu', '0032503', 3, 'd3538add7125404aba4b0007ef9fde50', '成都市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('81443e6b687349c6ae3231aff5e038ba', '铜陵', 'tongling', '0030415', 15, '249999f296d14f95b8138a30bbb2c374', '铜陵市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('8170c3271bc840d78e92ed355851aa5e', '西城区', 'xichengqu', '0030102', 2, '12a62a3e5bed44bba0412b7e6b733c93', '西城区', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('8346ebddc2464a9bbb99f7b0794da39c', '韶关', 'shaoguan', '0030714', 14, '0dd1f40bcb9d46aeba015dc19645a5b9', '韶关市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('8362c89358c748a5907b44de500a1333', '泰州', 'taizhou', '0031708', 8, '577405ff648240959b3765c950598ab0', '泰州市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('83e9fbd9e7fa4d878575088df7798b5e', '泸州', 'luzhou', '0032510', 10, 'd3538add7125404aba4b0007ef9fde50', '泸州市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('857be71b0d6d4a40a2c83476824206d1', '吉林', 'jilin', '00316', 16, '1', '吉林省', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('85b8accc31b34d70bce548a9d42767dd', '汉中', 'hanzhong', '0032403', 3, '534850c72ceb4a57b7dc269da63c330a', '汉中市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('85c0cb3849bc4d79b9c2fa2b63b2c858', '福州', 'fuzhou', '0030501', 1, 'd4066f6f425a4894a77f49f539f2a34f', '福州市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('85da31513d984f3e8a179b764efb3a22', '太原', 'taiyuan', '0032308', 8, '023473e9e6204583a110531036357514', '太原市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('863205a0ac1d4c50b19bb79f602dbea7', '张家口', 'zhangjiakou', '0031111', 11, '75362368f22f4d60a810c2a45cced487', '张家口市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('8636d6e9bdb34510bcb528159ca4f29d', '辽源', 'liaoyuan', '0031605', 5, '857be71b0d6d4a40a2c83476824206d1', '辽源市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('866bf0b4a8cc41dfb5071f8edb271934', '佛山', 'foshan', '0030703', 3, '0dd1f40bcb9d46aeba015dc19645a5b9', '佛山市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('86b85d3d8ddc4632946bdc4cdf642504', '虹口区', 'hongkouqu', '0030206', 6, 'f1ea30ddef1340609c35c88fb2919bee', '虹口区', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('871c4e3b4a044b2e8235d731835db559', '酒泉', 'jiuquan', '0030606', 6, '3283f1a77180495f9a0b192d0f9cdd35', '酒泉市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('87563959aa914187a0b5af82f862a0f0', '金昌', 'jinchang', '0030605', 5, '3283f1a77180495f9a0b192d0f9cdd35', '金昌市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('87ca58986beb4c58a877953e56486538', '婚假', 'Marriage holiday', '00402', 2, '6d30b170d4e348e585f113d14a4dd96d', '婚假', '', '', 'no');
INSERT INTO `sys_dictionaries` VALUES ('886a63c7def64cdfad1cfc0a2de8a1e0', '崇左', 'chongzuo', '0030803', 3, 'c5f3d426c582410281f89f1451e1d854', '崇左市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('89341f9a48444d258609e87cf40604fa', '伊犁州直', 'yilizhouzhi', '0032813', 13, '2fabed91c6d94e698ed449165cd250ca', '伊犁州直', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('89461222215e40f7b8067c7b791a9c2c', '南平', 'nanping', '0030503', 3, 'd4066f6f425a4894a77f49f539f2a34f', '南平市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('895262136b904f4888aa5af2f89dc967', '洛阳', 'luoyang', '0031206', 6, '7336944efb4b40fcae9118fc9a970d2d', '洛阳市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('896bce499bd740ffb9f745a4782a7886', '青浦区', 'qingpuqu', '0030214', 14, 'f1ea30ddef1340609c35c88fb2919bee', '青浦区', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('8a7265e11f0141ba808c0410b76d415b', '临沂', 'linyi', '0030309', 9, '10f46a521ea0471f8d71ee75ac3b5f3a', '临沂市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('8a79e7d2af7244b0b543492374ec6549', '兴安盟', 'xinganmeng', '0032012', 12, 'c072c248c7ab47dda7bf24f5e577925c', '兴安盟', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('8abbfbb071f34df4b77e2828f53ef99b', '怀化', 'huaihua', '0031512', 12, 'c59f91630bef4289b71fcb2a48994582', '怀化市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('8af40c23c6fe4ed8819dbe99f1f125f0', '奉贤区', 'fengxianqu', '0030215', 15, 'f1ea30ddef1340609c35c88fb2919bee', '奉贤区', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('8b336fb55c2346a2b5ec13f578c627ef', '苏州', 'suzhou', '0031706', 6, '577405ff648240959b3765c950598ab0', '苏州市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('8b5512281c364e09a67d8e81a5bd7ce9', '南川区', 'nanchuanqu', '0033119', 19, '1c85fbd06cf840d093f3640aca1b6b2d', '南川区', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('8b95528d20c44f86adf1e64009ce317b', '贵阳', 'guiyang', '0030903', 3, '592f6fcf45a74524aa8ea853fc9761d5', '贵阳市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('8bfd173e34704a7f978c539f87a511a8', '宝山区', 'baoshanqu', '0030209', 9, 'f1ea30ddef1340609c35c88fb2919bee', '宝山区', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('8c64bc307e894223a429a4f50a0cd387', '唐山', 'tangshan', '0031109', 9, '75362368f22f4d60a810c2a45cced487', '唐山市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('8d10532fba444c66bead45a6d9e13b6a', '延庆区', 'yanqingqu', '0030116', 16, '12a62a3e5bed44bba0412b7e6b733c93', '延庆区', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('8e35c978f8b248cb93863818be6be56b', '大足区', 'dazuqu', '0033111', 11, '1c85fbd06cf840d093f3640aca1b6b2d', '大足区', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('8f617ff8e61c49689cb26540a618a80c', '宁河区', 'ninghequ', '0032614', 14, '2c254799d3454f2cbc338ef5712548e9', '宁河区', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('8fa97a231db54e879ece49d566f0561d', '天门', 'tianmen', '0031410', 10, '312b80775e104ba08c8244a042a658df', '天门市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('9068c4ec7d1a4de69339fb61654cb3d9', '温州', 'wenzhou', '0033010', 10, '6d846178376549ed878d11d109819f25', '温州市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('921a9e3d2c434cca943aca4f3e5087b5', '渝中区', 'yuzhongqu', '0033103', 3, '1c85fbd06cf840d093f3640aca1b6b2d', '渝中区', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('923ca61a8fdb4357a5220763fdbd7c37', '台州', 'taizhou', '0033009', 9, '6d846178376549ed878d11d109819f25', '台州市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('92800c5b33df4f15a689ceda6bd23f2b', '信阳', 'xinyang', '0031214', 14, '7336944efb4b40fcae9118fc9a970d2d', '信阳市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('9381ab9da4b64001b289252ee21d1157', '河东区', 'hedongqu', '0032602', 2, '2c254799d3454f2cbc338ef5712548e9', '河东区', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('95a7fd77484f4ef39e9ed4596556a93c', '海西', 'haixi', '0032205', 5, '5a80e3435c0e4dc09bafceeadb38e5f0', '海西', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('9795de38301642539aefda61adf595a4', '江津区', 'jiangjinqu', '0033116', 16, '1c85fbd06cf840d093f3640aca1b6b2d', '江津区', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('97c543ee46e84586998554f5f745fcc6', '杨浦区', 'yangpuqu', '0030207', 7, 'f1ea30ddef1340609c35c88fb2919bee', '杨浦区', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('983eaae633244ecea99d11a804b1c736', '万州区', 'wanzhouqu', '0033101', 1, '1c85fbd06cf840d093f3640aca1b6b2d', '万州区', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('9a7465597dda46c8bb4c5c98aabfb4b9', '阳江', 'yangjiang', '0030716', 16, '0dd1f40bcb9d46aeba015dc19645a5b9', '阳江市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('9b483ad27bc14af2a47d8facdf8fafca', '嘉峪关', 'jiayuguan', '0030604', 4, '3283f1a77180495f9a0b192d0f9cdd35', '嘉峪关市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('9bb5220b50dd4def87ffbf6444a28c58', '黄浦区', 'huangpuqu', '0030201', 1, 'f1ea30ddef1340609c35c88fb2919bee', '黄浦区', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('9c8a7d5f3423458eb9e6ef68f6185fca', '黄山', 'huangshan', '0030411', 11, '249999f296d14f95b8138a30bbb2c374', '黄山市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('9d9700b28ae347bca4db9f592c78eb02', '百色', 'baise', '0030801', 1, 'c5f3d426c582410281f89f1451e1d854', '百色市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('9e4d7c5d9d86458d8c8d8a644e7eec9a', '阿拉善盟', 'alashanmeng', '0032001', 1, 'c072c248c7ab47dda7bf24f5e577925c', '阿拉善盟', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('9e833df814a74d1690f8039782ddf914', '昭通', 'zhaotong', '0032916', 16, '510607a1836e4079b3103e14ec5864ed', '昭通', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('9e9d0ad23c9e45df9dd9c269c0e4fdfa', '昌平区', 'changpingqu', '0030111', 11, '12a62a3e5bed44bba0412b7e6b733c93', '昌平区', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('9fc24347a7ca4a34bdea408dad223348', '闵行区', 'minhangqu', '0030208', 8, 'f1ea30ddef1340609c35c88fb2919bee', '闵行区', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('a013e3db1b384beb843959c33f361203', '静海区', 'jinghaiqu', '0032615', 15, '2c254799d3454f2cbc338ef5712548e9', '静海区', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('a0d5b55e48c945faad1d7bb624de7de8', '九江', 'jiujiang', '0031805', 5, 'cb3008cbd6ae4b5f8cebd2254ccb8603', '九江市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('a165da9b81b940fe9764fc7f5d41232d', '银川', 'yinchuan', '0032105', 5, '5690b0534fe745e5ba0f504f0c260559', '银川市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('a268c4b698274f12a64ba48db568d057', '潮州', 'chaozhou', '0030701', 1, '0dd1f40bcb9d46aeba015dc19645a5b9', '潮州市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('a34815cb348d4598a6fac4ece3baa0cd', '衡阳', 'hengyang', '0031504', 4, 'c59f91630bef4289b71fcb2a48994582', '衡阳市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('a39af97e7ad04ebfb530f49b05c7b146', '阜阳', 'fuyang', '0030407', 7, '249999f296d14f95b8138a30bbb2c374', '阜阳市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('a405680276e645188a122e8933f77a38', '乌兰察布', 'wulanchabu', '0032010', 10, 'c072c248c7ab47dda7bf24f5e577925c', '乌兰察布市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('a46bb0749dac4627b9a7d465dc75aca5', '武汉', 'wuhan', '0031411', 11, '312b80775e104ba08c8244a042a658df', '武汉市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('a4d4d8f678dd4dba958f466bbc581c5f', '菏泽', 'heze', '0030305', 5, '10f46a521ea0471f8d71ee75ac3b5f3a', '菏泽市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('a6898561d8664f6dad7a32f63ab64e19', '铁岭', 'tieling', '0031913', 13, 'b3366626c66c4b61881f09e1722e8495', '铁岭市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('a76ed518fb584442aaf1769fd2583677', '兰州', 'lanzhou', '0030607', 7, '3283f1a77180495f9a0b192d0f9cdd35', '兰州市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('a7d500ab05844c45b839f4a30c1d7643', '宜昌', 'yichang', '0031416', 16, '312b80775e104ba08c8244a042a658df', '宜昌市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('a9093795a013403e869a4308f17c7588', '海东', 'haidong', '0032203', 3, '5a80e3435c0e4dc09bafceeadb38e5f0', '海东', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('aa4b703a13dc4116bea578295efd9ea0', '赤峰', 'chifeng', '0032004', 4, 'c072c248c7ab47dda7bf24f5e577925c', '赤峰市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('aab8d56a03de4bbc84d433bc24748730', '贺州', 'hezhou', '0030808', 8, 'c5f3d426c582410281f89f1451e1d854', '贺州市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('ac1aa3c1b928467ebfa261cfaeb77be9', '葫芦岛', 'huludao', '0031908', 8, 'b3366626c66c4b61881f09e1722e8495', '葫芦岛市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('ac23d537ccd64827ab44007c5503bd58', '安阳', 'anyang', '0031201', 1, '7336944efb4b40fcae9118fc9a970d2d', '安阳市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('acd9f9b8fb8a4e47bd1e5d4eea45809c', '滁州', 'chuzhou', '0030406', 6, '249999f296d14f95b8138a30bbb2c374', '滁州市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('acf5c250d6614fb9920e442a3b178b86', '东营', 'dongying', '0030304', 4, '10f46a521ea0471f8d71ee75ac3b5f3a', '东营市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('act001', '工作流分类', 'CATEGORY', '001', 1, '0', '工作流分类', '', '', 'yes');
INSERT INTO `sys_dictionaries` VALUES ('act002', '工作流标识', 'KEY_', '002', 2, '0', '工作流标识', '', '', 'yes');
INSERT INTO `sys_dictionaries` VALUES ('adfac2a66ce04767bdbabbd8c115cd5d', '黄南', 'huangnan', '0032206', 6, '5a80e3435c0e4dc09bafceeadb38e5f0', '黄南', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('ae2c4a00360442f29ce8b0c284525ded', '崇明县', 'chongmingxian', '0030216', 16, 'f1ea30ddef1340609c35c88fb2919bee', '崇明县', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('ae384d9313e448949c8ed9c565e5cade', '扬州', 'yangzhou', '0031712', 12, '577405ff648240959b3765c950598ab0', '扬州市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('affc6a796b524efdb006bd1730003df7', '西青区', 'xiqingqu', '0032608', 8, '2c254799d3454f2cbc338ef5712548e9', '西青区', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('b0251d85679b40dca30ee83af80838bb', '拉萨', 'lasa', '0032703', 3, '3e846b08dbbe495e93bc93f8f202de79', '拉萨市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('b041a523ae214f03969454aa8c180ced', '房山区', 'fangshanqu', '0030108', 8, '12a62a3e5bed44bba0412b7e6b733c93', '房山区', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('b08ccea3cf89458e97b346546221e4ae', '凉山', 'liangshan', '0032102', 2, '5690b0534fe745e5ba0f504f0c260559', '凉山市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('b1d50a7eb21f44389733e17831fd121d', '盘锦', 'panjin', '0031911', 11, 'b3366626c66c4b61881f09e1722e8495', '盘锦市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('b20bb3345c6f459ba5f565e749636716', '事假', 'Compassionate leave', '00401', 1, '6d30b170d4e348e585f113d14a4dd96d', '事假', '', '', 'no');
INSERT INTO `sys_dictionaries` VALUES ('b21a81793ca6459f97c246ccbd543c67', '张掖', 'zhangye', '0030614', 14, '3283f1a77180495f9a0b192d0f9cdd35', '张掖市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('b2356bf7a1d546709ac296de1bf2a9eb', '连云港', 'lianyungang', '0031703', 3, '577405ff648240959b3765c950598ab0', '连云港市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('b2409f9c928f4dd1bd224809f54a1225', '宝坻区', 'baodiqu', '0032612', 12, '2c254799d3454f2cbc338ef5712548e9', '宝坻区', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('b2d4133b5dbf4599ada940620d2ab250', '黑龙江', 'heilongjiang', '00313', 13, '1', '黑龙江省', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('b3221428d30249d8acbb40f0f38d7a5c', '南宁', 'nanning', '0030811', 11, 'c5f3d426c582410281f89f1451e1d854', '南宁市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('b3366626c66c4b61881f09e1722e8495', '辽宁', 'liaoning', '00319', 19, '1', '辽宁省', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('b38f0725533a47cea5d0f5f520ad72c7', '沙坪坝区', 'shapingbaqu', '0033106', 6, '1c85fbd06cf840d093f3640aca1b6b2d', '沙坪坝区', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('b4736703fc064dbe8b8440c79991a1ed', '大连', 'dalian', '0031904', 4, 'b3366626c66c4b61881f09e1722e8495', '大连市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('b5329030086d470fa8cf6b38aaafb320', '随州', 'suizhou', '0031409', 9, '312b80775e104ba08c8244a042a658df', '随州市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('b68e669c229945ae86e053d15c277a6a', '惠州', 'huizhou', '0030706', 6, '0dd1f40bcb9d46aeba015dc19645a5b9', '惠州市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('b7785f96730e4a35820e08da1c200c4d', '璧山区', 'bishanqu', '0033120', 20, '1c85fbd06cf840d093f3640aca1b6b2d', '璧山区', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('b84acd830f3b4c65bd82c97cc925badf', '自贡', 'zigong', '0032520', 20, 'd3538add7125404aba4b0007ef9fde50', '自贡市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('b8e203af46924284a9a8be9851a557a2', '南开区', 'nankaiqu', '0032604', 4, '2c254799d3454f2cbc338ef5712548e9', '南开区', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('b908e46ac1544cb6a26f1e1fb22f2a94', '宜宾', 'yibin', '0032518', 18, 'd3538add7125404aba4b0007ef9fde50', '宜宾市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('b9c02b885a4a49719b1000110ed47df4', '毕节', 'bijie', '0030902', 2, '592f6fcf45a74524aa8ea853fc9761d5', '毕节市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('ba821dcfd50d4f64af1cd3eecc5f54e0', '海北', 'haibei', '0032202', 2, '5a80e3435c0e4dc09bafceeadb38e5f0', '海北', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('bbf9ff3b0fa444f18d70f2a4a9e45609', '绵阳', 'mianyang', '0032512', 12, 'd3538add7125404aba4b0007ef9fde50', '绵阳市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('bc2ec49f78204ea29cd666e2dc6583a2', '临沧', 'lincang', '0032910', 10, '510607a1836e4079b3103e14ec5864ed', '临沧', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('bc97087ea25547a794cec553d03c1abc', '那曲', 'naqu', '0032705', 5, '3e846b08dbbe495e93bc93f8f202de79', '那曲', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('bd1efd2194724213b72efa91fe3d5ddc', '浦东新区', 'pudongxinqu', '0030211', 11, 'f1ea30ddef1340609c35c88fb2919bee', '浦东新区', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('bdb65b22a7c447dcadbc6328292e5aef', '中山', 'zhongshan', '0030720', 20, '0dd1f40bcb9d46aeba015dc19645a5b9', '中山市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('bdce443b39ba4cef8c0c0b75bdc8e253', '沈阳', 'shenyang', '0031912', 12, 'b3366626c66c4b61881f09e1722e8495', '沈阳市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('be33f1cad2954520bbf033e4198890f7', '亳州', 'bozhou', '0030403', 3, '249999f296d14f95b8138a30bbb2c374', '亳州市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('be359cbd02944e1da5997ae560831db1', '定西', 'dingxi', '0030602', 2, '3283f1a77180495f9a0b192d0f9cdd35', '定西市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('beaf0090ebf94ad9af5dd78e372611fe', '咸阳', 'xianyang', '0032408', 8, '534850c72ceb4a57b7dc269da63c330a', '咸阳市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('bff8ba692e4c4d78a23309ec0ad745c7', '桂林', 'guilin', '0030806', 6, 'c5f3d426c582410281f89f1451e1d854', '桂林市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('c0170db89f2e48fe99bfdcd857fe2016', '承德', 'chengde', '0031103', 3, '75362368f22f4d60a810c2a45cced487', '承德市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('c072c248c7ab47dda7bf24f5e577925c', '内蒙古', 'neimenggu', '00320', 20, '1', '内蒙古', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('c0e55c6a60564359859d87d25c249ac4', '锦州', 'jinzhou', '0031909', 9, 'b3366626c66c4b61881f09e1722e8495', '锦州市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('c1875ba50f6d4e61870be000be8ee78e', '东丽区', 'dongliqu', '0032607', 7, '2c254799d3454f2cbc338ef5712548e9', '东丽区', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('c22083b403ba4ea698ba4dfc7245a317', '平凉', 'pingliang', '0030610', 10, '3283f1a77180495f9a0b192d0f9cdd35', '平凉市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('c256624d6caa49979ebc4dce35006945', '武清区', 'wuqingqu', '0032611', 11, '2c254799d3454f2cbc338ef5712548e9', '武清区', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('c2e0fca8943d41ba8ec5d013e9bc3f41', '日喀则', 'rikaze', '0032706', 6, '3e846b08dbbe495e93bc93f8f202de79', '日喀则', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('c3717fb4891148a0bef623dbd746e7e6', '开封', 'kaifeng', '0031205', 5, '7336944efb4b40fcae9118fc9a970d2d', '开封市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('c4817fc49be444e491920112aa9a3e32', '淮南', 'huainan', '0030410', 10, '249999f296d14f95b8138a30bbb2c374', '淮南市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('c59f91630bef4289b71fcb2a48994582', '湖南', 'hunan', '00315', 15, '1', '湖南省', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('c5f3d426c582410281f89f1451e1d854', '广西', 'guangxi', '00308', 8, '1', '广西省', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('c7852784049a473c917863c5bc84dd95', '甘南', 'gannan', '0030603', 3, '3283f1a77180495f9a0b192d0f9cdd35', '甘南市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('c7a5ba87961742f3b242ee4d30a55921', '营口', 'yingkou', '0031914', 14, 'b3366626c66c4b61881f09e1722e8495', '营口市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('c8342c1dcf584cbf92f20d90a62a34bf', '平顶山', 'pingdingshan', '0031209', 9, '7336944efb4b40fcae9118fc9a970d2d', '平顶山市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('c8d4119e57f84e71815769f03935e471', '黔东南', 'qiandongnan', '0030905', 5, '592f6fcf45a74524aa8ea853fc9761d5', '黔东南市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('c928e9192e2f4f5ca06c6599371ff83c', '六安', 'lu\'an', '0030412', 12, '249999f296d14f95b8138a30bbb2c374', '六安市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('c9811aef284b4ae8b8bf7698e90d8b3b', '泉州', 'quanzhou', '0030506', 6, 'd4066f6f425a4894a77f49f539f2a34f', '泉州市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('c9df1fd73d0642eea8b050738f6ed9fa', '聊城', 'liaocheng', '0030308', 8, '10f46a521ea0471f8d71ee75ac3b5f3a', '聊城市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('ca2e3717bb734c4b9142f29e36a31989', '株洲', 'zhuzhou', '0031502', 2, 'c59f91630bef4289b71fcb2a48994582', '株洲市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('cabe896dba3a4a21ba194f8839a3eb98', '大渡口区', 'dadukouqu', '0033104', 4, '1c85fbd06cf840d093f3640aca1b6b2d', '大渡口区', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('caeba09845bf4a29960a840d4f436f09', '上饶', 'shangrao', '0031808', 8, 'cb3008cbd6ae4b5f8cebd2254ccb8603', '上饶市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('cb3008cbd6ae4b5f8cebd2254ccb8603', '江西', 'jiangxi', '00318', 18, '1', '江西省', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('cc2aaa3ed3904d66a2f79676b14a1b49', '黔江区', 'qianjiangqu', '0033114', 14, '1c85fbd06cf840d093f3640aca1b6b2d', '黔江区', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('cd66a360619847d2b17989643f03dc8f', '济宁', 'jining', '0030306', 6, '10f46a521ea0471f8d71ee75ac3b5f3a', '济宁市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('cd87ffcb742744d18bbce6928922a5be', '衡州', 'hengzhou', '0033007', 7, '6d846178376549ed878d11d109819f25', '衡州市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('cdc1df5961994a9d94751003edd0fc58', '宿州', 'suzhou', '0030414', 14, '249999f296d14f95b8138a30bbb2c374', '宿州市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('cddd155314404710bd1ab3fa51d80cf2', '天水', 'tianshui', '0030612', 12, '3283f1a77180495f9a0b192d0f9cdd35', '天水市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('ce0dcc5a66dd44b3b9a42aef4aa8b4ba', '蚌埠', 'bengbu', '0030402', 2, '249999f296d14f95b8138a30bbb2c374', '蚌埠市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('cf632a4f4de54b619ad5cb6835f35434', '玉林', 'yulin', '0030814', 14, 'c5f3d426c582410281f89f1451e1d854', '玉林市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('cf7d29feb2c34cbfaf4e28896ca577e4', '巴彦淖尔', 'bayannaoer', '0032002', 2, 'c072c248c7ab47dda7bf24f5e577925c', '巴彦淖尔', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('cfb2df83812d4fabb10ba91e98be9467', '黑河', 'heihe', '0031305', 5, 'b2d4133b5dbf4599ada940620d2ab250', '黑河市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('cfee5c9833664e0aba76267389e4adbd', '丰台区', 'fengtaiqu', '0030104', 4, '12a62a3e5bed44bba0412b7e6b733c93', '丰台区', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('d13b01a607e34fdfa16deb7292a0f299', '鹤壁', 'hebi', '0031202', 2, '7336944efb4b40fcae9118fc9a970d2d', '鹤壁市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('d1646344b9cc45018c3a347f6dc6c77b', '红桥区', 'hongqiaoqu', '0032606', 6, '2c254799d3454f2cbc338ef5712548e9', '红桥区', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('d26fc248d49c4a71b2a003033adc88de', '娄底', 'loudi', '0031513', 13, 'c59f91630bef4289b71fcb2a48994582', '娄底市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('d3538add7125404aba4b0007ef9fde50', '四川', 'sichuan', '00325', 25, '1', '四川省', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('d3c2d3b9184b4e3185ca6bdbe73c5cff', '襄樊', 'xiangfan', '0031414', 14, '312b80775e104ba08c8244a042a658df', '襄樊市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('d3c76818757942fba8ebf8246fa257a9', '咸宁', 'xianning', '0031413', 13, '312b80775e104ba08c8244a042a658df', '咸宁市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('d4066f6f425a4894a77f49f539f2a34f', '福建', 'fujian', '00305', 5, '1', '福建省', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('d4131ecb91d3435db1dbd770ac39221f', '鞍山', 'anshan', '0031901', 1, 'b3366626c66c4b61881f09e1722e8495', '鞍山市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('d42e09432d10454caecf1d4335aca1da', '合肥', 'hefei', '0030408', 8, '249999f296d14f95b8138a30bbb2c374', '合肥市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('d4f8ba23c3ef4fbaa00c8f8a7c047bf1', '商丘', 'shangqiu', '0031212', 12, '7336944efb4b40fcae9118fc9a970d2d', '商丘市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('d5f5462779bc4976a3fbcbdeba45ed00', '丽水', 'lishui', '0033005', 5, '6d846178376549ed878d11d109819f25', '丽水市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('d5f621c6fab44d1eab1bdafd9d08e042', '绍兴', 'shaoxing', '0033008', 8, '6d846178376549ed878d11d109819f25', '绍兴市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('d7443dcd45704a27981810fc32b93390', '潍坊', 'weifang', '0030314', 14, '10f46a521ea0471f8d71ee75ac3b5f3a', '潍坊市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('d7c388a2ed58414a9bd4dfbedf6858b3', '顺义区', 'shunyiqu', '0030110', 10, '12a62a3e5bed44bba0412b7e6b733c93', '顺义区', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('d7e006b55b96491282e9c2e672d35a34', '克拉玛依', 'kelamayi', '0032808', 8, '2fabed91c6d94e698ed449165cd250ca', '克拉玛依', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('d7e25a4040694008b4cb8aa322f77ad9', '张家界', 'zhangjiajie', '0031508', 8, 'c59f91630bef4289b71fcb2a48994582', '张家界市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('d80455402bc44d2ca15e905913301fb2', '焦作', 'jiaozuo', '0031204', 4, '7336944efb4b40fcae9118fc9a970d2d', '焦作市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('d90a14bfbfe44a3e8d60bda8f8f362a6', '铜仁', 'tongren', '0030908', 8, '592f6fcf45a74524aa8ea853fc9761d5', '铜仁市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('da583c36f6754d498176755c93db8d7c', '遂宁', 'suining', '0032516', 16, 'd3538add7125404aba4b0007ef9fde50', '遂宁市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('db3b9c7116bc49e3a65fa641dd82155d', '红河', 'honghe', '0032907', 7, '510607a1836e4079b3103e14ec5864ed', '红河', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('db6336fcf27f4c00b37513ff0e368ae6', '长宁区', 'changningqu', '0030203', 3, 'f1ea30ddef1340609c35c88fb2919bee', '长宁区', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('db77fd88654c4014a71d541171d2795b', '西安', 'xi\'an', '0032407', 7, '534850c72ceb4a57b7dc269da63c330a', '西安市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('dc022922169446549dfac0de221d2a4d', '雅安', 'ya\'an', '0032517', 17, 'd3538add7125404aba4b0007ef9fde50', '雅安市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('dc9b2098188f4b5c93aec5c9bbfb895d', '吕梁', 'lvliang', '0032306', 6, '023473e9e6204583a110531036357514', '吕梁市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('dcbcde16e75643f9b8dd4b6293c87dd5', '滨州', 'binzhou', '0030302', 2, '10f46a521ea0471f8d71ee75ac3b5f3a', '滨州市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('dcd0ca1cde8f420dbfecbac4cf1506ee', '巴州', 'bazhou', '0032803', 3, '2fabed91c6d94e698ed449165cd250ca', '巴州', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('dcd445ef42c9484bbd14bacd02bebf37', '郑州', 'zhengzhou', '0031216', 16, '7336944efb4b40fcae9118fc9a970d2d', '郑州市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('dcf99d941ca44b30915e16a4048d5004', '益阳', 'yiyang', '0031509', 9, 'c59f91630bef4289b71fcb2a48994582', '益阳市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('dee1fa3295ec42219815769d00fabe70', '迪庆', 'diqing', '0032906', 6, '510607a1836e4079b3103e14ec5864ed', '迪庆', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('e06c4a42478b4853827911b8adac6def', '庆阳', 'qingyang', '0030611', 11, '3283f1a77180495f9a0b192d0f9cdd35', '庆阳市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('e1204d4286834046886f26ae6af0722a', '湘潭', 'xiangtan', '0031503', 3, 'c59f91630bef4289b71fcb2a48994582', '湘潭市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('e15cb9bb072248628b7ec481b6337229', '六盘水', 'liupanshui', '0030904', 4, '592f6fcf45a74524aa8ea853fc9761d5', '六盘水市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('e1bbd9b635e140ee8fcf0dc06743519b', '廊坊', 'langfang', '0031106', 6, '75362368f22f4d60a810c2a45cced487', '廊坊市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('e384a07d11bf413eb83cd490939ca7a2', '綦江区', 'qijiangqu', '0033110', 10, '1c85fbd06cf840d093f3640aca1b6b2d', '綦江区', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('e3ca4d54f3354ba5b17e1f93415ceb1a', '云浮', 'yunfu', '0030717', 17, '0dd1f40bcb9d46aeba015dc19645a5b9', '云浮市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('e4adf72e96ee4b7fa3528ee5ba4eb5cf', '梧州', 'wuzhou', '0030813', 13, 'c5f3d426c582410281f89f1451e1d854', '梧州市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('e556d8e5e148406883e1a83d6595e406', '铜梁区', 'tongliangqu', '0033121', 21, '1c85fbd06cf840d093f3640aca1b6b2d', '铜梁区', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('e7200f8c6dce4ea193bf33f55d9fd192', '池州', 'chizhou', '0030405', 5, '249999f296d14f95b8138a30bbb2c374', '池州市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('e7de6c7b752040b2bd3175641d83d128', '哈尔滨', 'haerbin', '0031303', 3, 'b2d4133b5dbf4599ada940620d2ab250', '哈尔滨市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('e8311a6f0ab4495484fdf24902ee97cc', '新余', 'xinyu', '0031809', 9, 'cb3008cbd6ae4b5f8cebd2254ccb8603', '新余市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('e91a5fc37b774b598648dbd0ee5e7566', '财务流程标识', 'Financial process identification', 'KEY_finance', 2, 'act002', '财务流程标识', '', '', 'yes');
INSERT INTO `sys_dictionaries` VALUES ('e9a653c9850c46bc9e2e1916de643a52', '楚雄', 'chuxiong', '0032903', 3, '510607a1836e4079b3103e14ec5864ed', '楚雄', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('ec0eed6293294d58aa56f6c381263288', '郴州', 'chenzhou', '0031510', 10, 'c59f91630bef4289b71fcb2a48994582', '郴州市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('ec107e60189346adb2b5749d6f6fe074', '德宏', 'dehong', '0032905', 5, '510607a1836e4079b3103e14ec5864ed', '德宏', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('ec892838cf4944cc8b330216f02de1e6', '津南区', 'jinnanqu', '0032609', 9, '2c254799d3454f2cbc338ef5712548e9', '津南区', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('ec96c3771161459c99eb01124db7aa8a', '三门峡', 'sanmenxia', '0031211', 11, '7336944efb4b40fcae9118fc9a970d2d', '三门峡市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('eca8a2f4e2534f77b7bccf263139d8c7', '内江', 'neijiang', '0032513', 13, 'd3538add7125404aba4b0007ef9fde50', '内江市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('ecb8f08c1408495bb31842c221d3edb4', '渝北区', 'yubeiqu', '0033112', 12, '1c85fbd06cf840d093f3640aca1b6b2d', '渝北区', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('ed5391a7608b4a61a24d95f2384f2131', '阜新', 'fuxin', '0031907', 7, 'b3366626c66c4b61881f09e1722e8495', '阜新市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('ed5b3d39695f496d88c37f56508d6447', '仙桃', 'xiantao', '0031412', 12, '312b80775e104ba08c8244a042a658df', '仙桃市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('ed97335f8b3d42fabfd89993bc68475d', '海南', 'hainan', '0032204', 4, '5a80e3435c0e4dc09bafceeadb38e5f0', '海南', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('ede65c49ae624ef8900414298f79a438', '乌海', 'wuhai', '0032009', 9, 'c072c248c7ab47dda7bf24f5e577925c', '乌海市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('f0570e2fe4644e32af5c5401e8c371ba', '盐城', 'yancheng', '0031711', 11, '577405ff648240959b3765c950598ab0', '盐城市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('f0eb076930844fe18fdd8dcf5fc1f56e', '塔城', 'tacheng', '0032810', 10, '2fabed91c6d94e698ed449165cd250ca', '塔城', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('f16ddc177870477685297a6abb157637', '朔州', 'shuozhou', '0032307', 7, '023473e9e6204583a110531036357514', '朔州市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('f1e2cdd9518c4ac2b5e1ea52985b9771', '果洛', 'guoluo', '0032201', 1, '5a80e3435c0e4dc09bafceeadb38e5f0', '果洛', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('f1ea30ddef1340609c35c88fb2919bee', '上海', 'shanghai', '00302', 2, '1', '上海', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('f260eee573dc48fca1572b228d280849', '版纳', 'banna', '0032901', 1, '510607a1836e4079b3103e14ec5864ed', '版纳', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('f389ad0eb7884c4d91d4f31312bc8771', '萍乡', 'pingxiang', '0031807', 7, 'cb3008cbd6ae4b5f8cebd2254ccb8603', '萍乡市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('f416737f56924f7cb642a75f57b1530a', '渭南', 'weinan', '0032406', 6, '534850c72ceb4a57b7dc269da63c330a', '渭南市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('f498d09200ba48df9d6e281776eba4f8', '徐州', 'xuzhou', '0031710', 10, '577405ff648240959b3765c950598ab0', '徐州市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('f4bdd6b8f0704479a6d051f52d62d693', '河池', 'hechi', '0030807', 7, 'c5f3d426c582410281f89f1451e1d854', '河池市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('f4f2434769b842afbbf1791018b69800', '河北区', 'hebeiqu', '0032605', 5, '2c254799d3454f2cbc338ef5712548e9', '河北区', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('f57d2b8d983f43d5a035a596b690445b', '金山区', 'jinshanqu', '0030212', 12, 'f1ea30ddef1340609c35c88fb2919bee', '金山区', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('f6337bdeefa44b0db9f35fe2fe7d6d6f', '十堰', 'shiyan', '0031408', 8, '312b80775e104ba08c8244a042a658df', '十堰市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('f661c388a73d478699a2c1c5909b45f1', '三亚', 'sanya', '0031002', 2, '2ba8e6d0fd944983aa19b781c6b53477', '三亚市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('f6ff36eb35414a5dacf7ccc0c479d2ea', '宿迁', 'suqian', '0031707', 7, '577405ff648240959b3765c950598ab0', '宿迁市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('f775a440cb004c63b0b3d3429b58a1e6', '衡水', 'hengshui', '0031105', 5, '75362368f22f4d60a810c2a45cced487', '衡水市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('f845a1c0a62b45bfbf358688eec3680d', '巢湖', 'chaohu', '0030404', 4, '249999f296d14f95b8138a30bbb2c374', '巢湖市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('f9565fe7c0a348ba949131645d20e8fa', '恩施', 'enshi', '0031402', 2, '312b80775e104ba08c8244a042a658df', '恩施市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('f970bd1da8f94bfa92206fa94d595cbb', '锡林郭勒盟', 'xilinguolemeng', '0032011', 11, 'c072c248c7ab47dda7bf24f5e577925c', '锡林郭勒盟', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('f9a9156f0e9e41438e823f93070248bd', '濮阳', 'puyang', '0031210', 10, '7336944efb4b40fcae9118fc9a970d2d', '濮阳市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('f9ceff59e02c4be3a4b20aa806c1ec0d', '呼和浩特', 'huhehaote', '0032006', 6, 'c072c248c7ab47dda7bf24f5e577925c', '呼和浩特市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('fa2ff170919e406d9d5547ff09d14d0d', '双鸭山', 'shuangyashan', '0031311', 11, 'b2d4133b5dbf4599ada940620d2ab250', '双鸭山市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('fa3446ef035546c09c1f27268b43b937', '南京', 'nanjing', '0031704', 4, '577405ff648240959b3765c950598ab0', '南京市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('fbdbbe70e361402e886dfd3d2c7bd434', '公假', 'Public holidays', '00405', 5, '6d30b170d4e348e585f113d14a4dd96d', '公假', '', '', 'no');
INSERT INTO `sys_dictionaries` VALUES ('fc70429d9b8146e0ac31ce38410e2cb7', '南阳', 'nanyang', '0031208', 8, '7336944efb4b40fcae9118fc9a970d2d', '南阳市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('fd06b72a41654fcfbfe2c3327ca4e9fc', '珠海', 'zhuhai', '0030721', 21, '0dd1f40bcb9d46aeba015dc19645a5b9', '珠海市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('fd1d83119c414e56b3a35052c9d6dd0f', '文山', 'wenshan', '0032914', 14, '510607a1836e4079b3103e14ec5864ed', '文山', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('fd2a0cad70c643528587d1ccde4c5530', '铜川', 'tongchuan', '0032405', 5, '534850c72ceb4a57b7dc269da63c330a', '铜川市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('fd79801a69ad4ec5857df82358c26368', '江北区', 'jiangbeiqu', '0033105', 5, '1c85fbd06cf840d093f3640aca1b6b2d', '江北区', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('ff880943e156482ea50d1ece4ff233a6', '昌吉州', 'changjizhou', '0032805', 5, '2fabed91c6d94e698ed449165cd250ca', '昌吉州', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('ffb2cc1e96fe485b94335589315ab38c', '临汾', 'linfen', '0032305', 5, '023473e9e6204583a110531036357514', '临汾市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('ffd838f71da648319bfe4f176e0e209f', '晋中', 'jinzhong', '0032304', 4, '023473e9e6204583a110531036357514', '晋中市', '', NULL, NULL);
INSERT INTO `sys_dictionaries` VALUES ('ffeaa196501d4f35a486e42be17f2986', '枣庄', 'zaozhuang', '0030316', 16, '10f46a521ea0471f8d71ee75ac3b5f3a', '枣庄市', '', NULL, NULL);

-- ----------------------------
-- Table structure for sys_fhbutton
-- ----------------------------
DROP TABLE IF EXISTS `sys_fhbutton`;
CREATE TABLE `sys_fhbutton`  (
  `FHBUTTON_ID` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `NAME` varchar(30) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '名称',
  `SHIRO_KEY` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '权限标识',
  `BZ` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`FHBUTTON_ID`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_fhbutton
-- ----------------------------
INSERT INTO `sys_fhbutton` VALUES ('117825711d484e719a2a3bf1d0d45c2b', '发站内信', 'fhSms', '发站内信按钮');
INSERT INTO `sys_fhbutton` VALUES ('1af9cef37b1f4cc79abacb52c25a3dfa', '驳回', 'Reject', '驳回');
INSERT INTO `sys_fhbutton` VALUES ('3542adfbda73410c976e185ffe50ad06', '从系统导出到EXCEL', 'toExcel', '从系统导出到EXCEL');
INSERT INTO `sys_fhbutton` VALUES ('688f6db8b226468e82e0f2c40d377fd9', '作废', 'Abolish', '作废');
INSERT INTO `sys_fhbutton` VALUES ('7b9756f1455d418396d14bf5a1f8ed09', '指定下一办理对象', 'NextASSIGNEE_', '指定下一办理对象');
INSERT INTO `sys_fhbutton` VALUES ('c3d94fcfd3ee4a2a8f08bb1cd110bc5c', '发邮件', 'email', '发邮件按钮');
INSERT INTO `sys_fhbutton` VALUES ('e2a0d76a25bd467aa29aaadf3a8def18', '委派', 'Delegate', '委派');
INSERT INTO `sys_fhbutton` VALUES ('ed570ab55e4f4151a6654a4578aec787', '从EXCEL导入系统', 'fromExcel', '从EXCEL导入系统');

-- ----------------------------
-- Table structure for sys_fhlog
-- ----------------------------
DROP TABLE IF EXISTS `sys_fhlog`;
CREATE TABLE `sys_fhlog`  (
  `FHLOG_ID` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `USERNAME` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '用户名',
  `CZTIME` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '操作时间',
  `CONTENT` longtext CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '事件',
  PRIMARY KEY (`FHLOG_ID`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_fhlog
-- ----------------------------
INSERT INTO `sys_fhlog` VALUES ('059018bbbfa0425784d9262576d662a5', 'admin', '2019-03-28 17:37:59', '成功登录系统');
INSERT INTO `sys_fhlog` VALUES ('477750202e5746bbac4eadd11afcfa72', 'admin', '2019-06-18 13:26:17', '成功登录系统');
INSERT INTO `sys_fhlog` VALUES ('4fe686acaab044b18fb44f281e634d17', 'admin', '2019-07-11 18:15:28', '成功登录系统');
INSERT INTO `sys_fhlog` VALUES ('899d1723bb3a4170937ce2ecae422990', 'admin', '2019-03-28 17:07:52', '成功登录系统');
INSERT INTO `sys_fhlog` VALUES ('8f9bd243d3b24ee591b3660679600f5b', 'student', '2019-06-18 13:22:02', '尝试登录系统失败,用户名密码错误,无权限');
INSERT INTO `sys_fhlog` VALUES ('9f2bd2ca0aa242bab7634a6739f628f8', 'admin', '2019-07-11 18:07:35', '成功登录系统');
INSERT INTO `sys_fhlog` VALUES ('a601f800b0334a36bdbfc9a1b8103bb1', 'admin', '2019-06-18 13:22:07', '成功登录系统');
INSERT INTO `sys_fhlog` VALUES ('ab9c4150bffc4c76bfbfe1bbb038d0e1', 'admin', '2019-06-18 13:25:52', '退出系统');
INSERT INTO `sys_fhlog` VALUES ('ca0d6f8ed07c4487b99aa222a2cf59ad', 'admin', '2019-06-18 13:26:19', '成功登录系统');
INSERT INTO `sys_fhlog` VALUES ('d0b9de62dc9446da950ec53e2f935760', 'admin', '2019-03-09 21:37:14', '成功登录系统');
INSERT INTO `sys_fhlog` VALUES ('d7f065df7a7f43f794d9bb27b26d2945', 'admin', '2019-07-11 18:15:15', '退出系统');

-- ----------------------------
-- Table structure for sys_fhsms
-- ----------------------------
DROP TABLE IF EXISTS `sys_fhsms`;
CREATE TABLE `sys_fhsms`  (
  `FHSMS_ID` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `CONTENT` varchar(1000) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '内容',
  `TYPE` varchar(5) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '类型',
  `TO_USERNAME` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '收信人',
  `FROM_USERNAME` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '发信人',
  `SEND_TIME` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '发信时间',
  `STATUS` varchar(5) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '状态',
  `SANME_ID` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  PRIMARY KEY (`FHSMS_ID`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_fhsms
-- ----------------------------
INSERT INTO `sys_fhsms` VALUES ('42bc0a11d77248f09df5229c1ebcd9a9', '您申请的任务已经被作废,请到已办任务列表查看', '1', '系统消息', 'admin', '2019-03-09 20:33:57', '2', '636b8f58b5a54caead2541a2ba598b51');

-- ----------------------------
-- Table structure for sys_menu
-- ----------------------------
DROP TABLE IF EXISTS `sys_menu`;
CREATE TABLE `sys_menu`  (
  `MENU_ID` int(11) NOT NULL,
  `MENU_NAME` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `MENU_URL` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `PARENT_ID` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `MENU_ORDER` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `MENU_ICON` varchar(60) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `MENU_TYPE` varchar(10) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `MENU_STATE` int(1) NULL DEFAULT NULL,
  `SHIRO_KEY` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  PRIMARY KEY (`MENU_ID`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_menu
-- ----------------------------
INSERT INTO `sys_menu` VALUES (1, '系统管理', '#', '0', '1', '<i class=\"feather icon-monitor\"></i>', '2', 1, NULL);
INSERT INTO `sys_menu` VALUES (2, '权限管理', '#', '1', '1', '<i class=\"zmdi zmdi-key zmdi-hc-fw\"></i>', '1', 1, NULL);
INSERT INTO `sys_menu` VALUES (3, '日志管理', 'fhlog/list', '1', '6', '<i class=\"zmdi zmdi-keyboard-hide zmdi-hc-fw\"></i>', '1', 1, 'fhlog:list');
INSERT INTO `sys_menu` VALUES (4, '性能监控', 'druid/index.html', '5', '2', '<i class=\"zmdi zmdi-devices zmdi-hc-fw\"></i>', '1', 1, '(无)');
INSERT INTO `sys_menu` VALUES (5, '系统工具', '#', '0', '4', '<i class=\"feather icon-grid\"></i>', '1', 1, '(无)');
INSERT INTO `sys_menu` VALUES (6, '角色(基础权限)', 'role/list', '2', '1', '<i class=\"zmdi zmdi-account-calendar zmdi-hc-fw\"></i>', '1', 1, 'role:list');
INSERT INTO `sys_menu` VALUES (7, '按钮权限', 'buttonrights/list', '2', '2', '<i class=\"zmdi zmdi-shield-security zmdi-hc-fw\"></i>', '1', 1, 'buttonrights:list');
INSERT INTO `sys_menu` VALUES (8, '菜单管理', 'menu/listAllMenu', '1', '3', '<i class=\"zmdi zmdi-collection-text zmdi-hc-fw\"></i>', '1', 1, 'menu:list');
INSERT INTO `sys_menu` VALUES (9, '按钮管理', 'fhbutton/list', '1', '2', '<i class=\"zmdi zmdi-picture-in-picture zmdi-hc-fw\"></i>', '1', 1, 'fhbutton:list');
INSERT INTO `sys_menu` VALUES (10, '用户管理', '#', '0', '2', '<i class=\"feather icon-users\"></i>', '2', 1, NULL);
INSERT INTO `sys_menu` VALUES (11, '系统用户', 'user/listUsers', '10', '1', '<i class=\"zmdi zmdi-accounts zmdi-hc-fw\"></i>', '1', 1, 'user:list');
INSERT INTO `sys_menu` VALUES (12, '数据字典', 'dictionaries/listAllDict?DICTIONARIES_ID=0', '1', '4', '<i class=\"zmdi zmdi-assignment zmdi-hc-fw\"></i>', '1', 1, 'dictionaries:list');
INSERT INTO `sys_menu` VALUES (13, '代码生成器', '#', '5', '1', '<i class=\"zmdi zmdi-keyboard zmdi-hc-fw\"></i>', '1', 1, '(无)');
INSERT INTO `sys_menu` VALUES (14, '正向生成', 'createCode/list', '13', '1', '<i class=\"zmdi zmdi-square-right zmdi-hc-fw\"></i>', '1', 1, 'createCode:list');
INSERT INTO `sys_menu` VALUES (15, '反向生成', 'recreateCode/list', '13', '2', '<i class=\"zmdi zmdi-rotate-left zmdi-hc-fw\"></i>', '1', 1, 'recreateCode:list');
INSERT INTO `sys_menu` VALUES (16, '模版管理', 'codeeditor/goEdit?type=createOneCode&ftl=controllerTemplate', '13', '3', '<i class=\"zmdi zmdi-assignment zmdi-hc-fw\"></i>', '1', 1, '(无)');
INSERT INTO `sys_menu` VALUES (17, '单表模版', 'codeeditor/goEdit?type=createOneCode&ftl=controllerTemplate', '16', '1', '<i class=\"zmdi zmdi-folder-outline zmdi-hc-fw\"></i>', '1', 1, 'codeeditor:list');
INSERT INTO `sys_menu` VALUES (18, '主表模版', 'codeeditor/goEdit?type=createFaCode&ftl=controllerTemplate', '16', '2', '<i class=\"zmdi zmdi-folder-outline zmdi-hc-fw\"></i>', '1', 1, 'codeeditor:list');
INSERT INTO `sys_menu` VALUES (19, '明细表模版', 'codeeditor/goEdit?type=createSoCode&ftl=controllerTemplate', '16', '3', '<i class=\"zmdi zmdi-folder-outline zmdi-hc-fw\"></i>', '1', 1, 'codeeditor:list');
INSERT INTO `sys_menu` VALUES (20, '树形表模版', 'codeeditor/goEdit?type=createTreeCode&ftl=controllerTemplate', '16', '4', '<i class=\"zmdi zmdi-folder-outline zmdi-hc-fw\"></i>', '1', 1, '(无)');
INSERT INTO `sys_menu` VALUES (21, '处理类', 'codeeditor/goEdit?type=createOneCode&ftl=controllerTemplate', '17', '1', '', '1', 1, '(无)');
INSERT INTO `sys_menu` VALUES (22, 'mapper层', 'codeeditor/goEdit?type=createOneCode&ftl=mapperTemplate', '17', '2', '', '1', 1, '(无)');
INSERT INTO `sys_menu` VALUES (23, 'service层', 'codeeditor/goEdit?type=createOneCode&ftl=serviceTemplate', '17', '3', '', '1', 1, '(无)');
INSERT INTO `sys_menu` VALUES (24, 'serviceI实现类', 'codeeditor/goEdit?type=createOneCode&ftl=serviceImplTemplate', '17', '4', '', '1', 1, '(无)');
INSERT INTO `sys_menu` VALUES (25, 'mybatis_xml_mysql', 'codeeditor/goEdit2?type=createOneCode&ftl=xml_MysqlTemplate', '17', '5', '', '1', 1, '(无)');
INSERT INTO `sys_menu` VALUES (26, 'mybatis_xml_oracle', 'codeeditor/goEdit2?type=createOneCode&ftl=xml_OracleTemplate', '17', '6', '', '1', 1, '(无)');
INSERT INTO `sys_menu` VALUES (27, 'mybatis_xml_sqlserver', 'codeeditor/goEdit2?type=createOneCode&ftl=xml_SqlserverTemplate', '17', '7', '', '1', 1, '(无)');
INSERT INTO `sys_menu` VALUES (28, 'mysql_sql_脚本', 'codeeditor/goEdit?type=createOneCode&ftl=mysql_SQL_Template', '17', '8', '', '1', 1, '(无)');
INSERT INTO `sys_menu` VALUES (29, 'oracle_sql_脚本', 'codeeditor/goEdit?type=createOneCode&ftl=oracle_SQL_Template', '17', '9', '', '1', 1, '(无)');
INSERT INTO `sys_menu` VALUES (30, 'sqlserver_sql_脚本', 'codeeditor/goEdit?type=createOneCode&ftl=sqlserver_SQL_Template', '17', '10', '', '1', 1, '(无)');
INSERT INTO `sys_menu` VALUES (31, 'jsp_列表页面', 'codeeditor/goEdit2?type=createOneCode&ftl=jsp_list_Template', '17', '11', '', '1', 1, '(无)');
INSERT INTO `sys_menu` VALUES (32, 'jsp_编辑页面', 'codeeditor/goEdit2?type=createOneCode&ftl=jsp_edit_Template', '17', '12', '', '1', 1, '(无)');
INSERT INTO `sys_menu` VALUES (33, '处理类', 'codeeditor/goEdit?type=createFaCode&ftl=controllerTemplate', '18', '1', '', '1', 1, '(无)');
INSERT INTO `sys_menu` VALUES (34, 'mapper层', 'codeeditor/goEdit?type=createFaCode&ftl=mapperTemplate', '18', '2', '', '1', 1, '(无)');
INSERT INTO `sys_menu` VALUES (35, 'service层 ', 'codeeditor/goEdit?type=createFaCode&ftl=serviceTemplate', '18', '3', '', '1', 1, '(无)');
INSERT INTO `sys_menu` VALUES (36, 'serviceI实现类', 'codeeditor/goEdit?type=createFaCode&ftl=serviceImplTemplate', '18', '4', '', '1', 1, '(无)');
INSERT INTO `sys_menu` VALUES (37, 'mybatis_xml_mysql', 'codeeditor/goEdit2?type=createFaCode&ftl=xml_MysqlTemplate', '18', '5', '', '1', 1, '(无)');
INSERT INTO `sys_menu` VALUES (38, 'mybatis_xml_oracle', 'codeeditor/goEdit2?type=createFaCode&ftl=xml_OracleTemplate', '18', '6', '', '1', 1, '(无)');
INSERT INTO `sys_menu` VALUES (39, 'mybatis_xml_sqlserver', 'codeeditor/goEdit2?type=createFaCode&ftl=xml_SqlserverTemplate', '18', '7', '', '1', 1, '(无)');
INSERT INTO `sys_menu` VALUES (40, 'mysql_sql_脚本', 'codeeditor/goEdit?type=createFaCode&ftl=mysql_SQL_Template', '18', '8', '', '1', 1, '(无)');
INSERT INTO `sys_menu` VALUES (41, 'oracle_sql_脚本', 'codeeditor/goEdit?type=createFaCode&ftl=oracle_SQL_Template', '18', '9', '', '1', 1, '(无)');
INSERT INTO `sys_menu` VALUES (42, 'sqlserver_sql_脚本', 'codeeditor/goEdit?type=createFaCode&ftl=sqlserver_SQL_Template', '18', '10', '', '1', 1, '(无)');
INSERT INTO `sys_menu` VALUES (43, 'jsp_列表页面', 'codeeditor/goEdit2?type=createFaCode&ftl=jsp_list_Template', '18', '11', '', '1', 1, '(无)');
INSERT INTO `sys_menu` VALUES (44, 'jsp_编辑页面', 'codeeditor/goEdit2?type=createFaCode&ftl=jsp_edit_Template', '18', '12', '', '1', 1, '(无)');
INSERT INTO `sys_menu` VALUES (45, '处理类', 'codeeditor/goEdit?type=createSoCode&ftl=controllerTemplate', '19', '1', '', '1', 1, '(无)');
INSERT INTO `sys_menu` VALUES (46, 'mapper层', 'codeeditor/goEdit?type=createSoCode&ftl=mapperTemplate', '19', '2', '', '1', 1, '(无)');
INSERT INTO `sys_menu` VALUES (47, 'service层', 'codeeditor/goEdit?type=createSoCode&ftl=serviceTemplate', '19', '3', '', '1', 1, '(无)');
INSERT INTO `sys_menu` VALUES (48, 'serviceI实现类', 'codeeditor/goEdit?type=createSoCode&ftl=serviceImplTemplate', '19', '4', '', '1', 1, '(无)');
INSERT INTO `sys_menu` VALUES (49, 'mybatis_xml_mysql', 'codeeditor/goEdit2?type=createSoCode&ftl=xml_MysqlTemplate', '19', '5', '', '1', 1, '(无)');
INSERT INTO `sys_menu` VALUES (50, 'mybatis_xml_oracle', 'codeeditor/goEdit2?type=createSoCode&ftl=xml_OracleTemplate', '19', '6', '', '1', 1, '(无)');
INSERT INTO `sys_menu` VALUES (51, 'mybatis_xml_sqlserver', 'codeeditor/goEdit2?type=createSoCode&ftl=xml_SqlserverTemplate', '19', '7', '', '1', 1, '(无)');
INSERT INTO `sys_menu` VALUES (52, 'mysql_sql_脚本', 'codeeditor/goEdit?type=createSoCode&ftl=mysql_SQL_Template', '19', '8', '', '1', 1, '(无)');
INSERT INTO `sys_menu` VALUES (53, 'oracle_sql_脚本', 'codeeditor/goEdit?type=createSoCode&ftl=oracle_SQL_Template', '19', '9', '', '1', 1, '(无)');
INSERT INTO `sys_menu` VALUES (54, 'sqlserver_sql_脚本', 'codeeditor/goEdit?type=createSoCode&ftl=sqlserver_SQL_Template', '19', '10', '', '1', 1, '(无)');
INSERT INTO `sys_menu` VALUES (55, 'jsp_列表页面', 'codeeditor/goEdit2?type=createSoCode&ftl=jsp_list_Template', '19', '11', '', '1', 1, '(无)');
INSERT INTO `sys_menu` VALUES (56, 'jsp_编辑页面', 'codeeditor/goEdit2?type=createSoCode&ftl=jsp_edit_Template', '19', '12', '', '1', 1, '(无)');
INSERT INTO `sys_menu` VALUES (57, '处理类', 'codeeditor/goEdit?type=createTreeCode&ftl=controllerTemplate', '20', '1', '', '1', 1, '(无)');
INSERT INTO `sys_menu` VALUES (58, '实体类', 'codeeditor/goEdit?type=createTreeCode&ftl=entityTemplate', '20', '2', '', '1', 1, '(无)');
INSERT INTO `sys_menu` VALUES (59, 'mapper层', 'codeeditor/goEdit?type=createTreeCode&ftl=mapperTemplate', '20', '3', '', '1', 1, '(无)');
INSERT INTO `sys_menu` VALUES (60, 'service层', 'codeeditor/goEdit?type=createTreeCode&ftl=serviceTemplate', '20', '4', '', '1', 1, '(无)');
INSERT INTO `sys_menu` VALUES (61, 'serviceI实现类', 'codeeditor/goEdit?type=createTreeCode&ftl=serviceImplTemplate', '20', '5', '', '1', 1, '(无)');
INSERT INTO `sys_menu` VALUES (62, 'mybatis_xml_mysql', 'codeeditor/goEdit2?type=createTreeCode&ftl=xml_MysqlTemplate', '20', '6', '', '1', 1, '(无)');
INSERT INTO `sys_menu` VALUES (63, 'mybatis_xml_oracle', 'codeeditor/goEdit2?type=createTreeCode&ftl=xml_OracleTemplate', '20', '7', '', '1', 1, '(无)');
INSERT INTO `sys_menu` VALUES (64, 'mybatis_xml_sqlserver', 'codeeditor/goEdit2?type=createTreeCode&ftl=xml_SqlserverTemplate', '20', '8', '', '1', 1, '(无)');
INSERT INTO `sys_menu` VALUES (65, 'mysql_sql_脚本', 'codeeditor/goEdit?type=createTreeCode&ftl=mysql_SQL_Template', '20', '9', '', '1', 1, '(无)');
INSERT INTO `sys_menu` VALUES (66, 'oracle_sql_脚本', 'codeeditor/goEdit?type=createTreeCode&ftl=oracle_SQL_Template', '20', '10', '', '1', 1, '(无)');
INSERT INTO `sys_menu` VALUES (67, 'sqlserver_sql_脚本', 'codeeditor/goEdit?type=createTreeCode&ftl=sqlserver_SQL_Template', '20', '11', '', '1', 1, '(无)');
INSERT INTO `sys_menu` VALUES (68, 'tree_树形页面', 'codeeditor/goEdit2?type=createTreeCode&ftl=jsp_tree_Template', '20', '12', '', '1', 1, '(无)');
INSERT INTO `sys_menu` VALUES (69, 'jsp_列表页面', 'codeeditor/goEdit2?type=createTreeCode&ftl=jsp_list_Template', '20', '13', '', '1', 1, '(无)');
INSERT INTO `sys_menu` VALUES (70, 'jsp_编辑页面', 'codeeditor/goEdit2?type=createTreeCode&ftl=jsp_edit_Template', '20', '14', '', '1', 1, '(无)');
INSERT INTO `sys_menu` VALUES (71, '在线管理', 'online/list', '1', '6', '<i class=\"zmdi zmdi-globe zmdi-hc-fw\"></i>', '1', 1, 'online:list');
INSERT INTO `sys_menu` VALUES (72, '我的通讯', '#', '0', '3', '<i class=\"feather icon-phone-call\"></i>', '1', 1, '(无)');
INSERT INTO `sys_menu` VALUES (73, '好友管理', 'friends/list', '72', '1', '<i class=\"zmdi zmdi-account zmdi-hc-fw\"></i>', '1', 1, 'friends:list');
INSERT INTO `sys_menu` VALUES (74, '好友分组', 'fgroup/list', '72', '2', '<i class=\"zmdi zmdi-accounts-alt zmdi-hc-fw\"></i>', '1', 1, 'fgroup:list');
INSERT INTO `sys_menu` VALUES (75, '我的群组', 'qgroup/list', '72', '3', '<i class=\"zmdi zmdi-male-female zmdi-hc-fw\"></i>', '1', 1, 'qgroup:list');
INSERT INTO `sys_menu` VALUES (76, '数据库管理', '#', '0', '6', '<i class=\"mdi mdi-database\"></i>', '1', 1, '(无)');
INSERT INTO `sys_menu` VALUES (77, '数据库备份', 'brdb/listAllTable', '76', '1', '<i class=\"zmdi zmdi-assignment-returned zmdi-hc-fw\"></i>', '1', 1, 'brdb:listAllTable');
INSERT INTO `sys_menu` VALUES (78, '备份定时器 ', 'timingbackup/list', '76', '2', '<i class=\"zmdi zmdi-alarm zmdi-hc-fw\"></i>', '1', 1, 'timingbackup:list');
INSERT INTO `sys_menu` VALUES (79, '数据库还原', 'brdb/list', '76', '3', '<i class=\"zmdi zmdi-assignment-return zmdi-hc-fw\"></i>', '1', 1, 'brdb:list');
INSERT INTO `sys_menu` VALUES (80, 'SQL编辑器', 'sqledit/view', '76', '4', '<i class=\"zmdi zmdi-receipt zmdi-hc-fw\"></i>', '1', 1, 'sqledit:view');
INSERT INTO `sys_menu` VALUES (81, '办公管理', '#', '0', '5', '<i class=\"feather icon-briefcase\"></i>', '1', 1, '(无)');
INSERT INTO `sys_menu` VALUES (82, '工作流程', '#', '81', '1', '', '1', 1, '(无)');
INSERT INTO `sys_menu` VALUES (83, '模型管理', 'fhmodel/list', '82', '1', '', '1', 1, 'fhmodel:list');
INSERT INTO `sys_menu` VALUES (84, '流程管理', 'procdef/list', '82', '2', '', '1', 1, 'procdef:list');
INSERT INTO `sys_menu` VALUES (85, '运行中流程', 'ruprocdef/list', '82', '3', '', '1', 1, 'ruprocdef:list');
INSERT INTO `sys_menu` VALUES (86, '历史的流程', 'hiprocdef/list', '82', '4', '', '1', 1, 'hiprocdef:list');
INSERT INTO `sys_menu` VALUES (87, '申请审批', '#', '81', '3', '', '1', 1, '(无)');
INSERT INTO `sys_menu` VALUES (88, '请假申请', 'myleave/list', '87', '1', '', '1', 1, 'myleave:list');
INSERT INTO `sys_menu` VALUES (89, '任务管理', '#', '81', '2', '', '1', 1, '(无)');
INSERT INTO `sys_menu` VALUES (90, '待办任务', 'rutask/list', '89', '1', '', '1', 1, 'rutask:list');
INSERT INTO `sys_menu` VALUES (91, '已办任务', 'hitask/list', '89', '2', '', '1', 1, 'hitask:list');

-- ----------------------------
-- Table structure for sys_role
-- ----------------------------
DROP TABLE IF EXISTS `sys_role`;
CREATE TABLE `sys_role`  (
  `ROLE_ID` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `ROLE_NAME` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `RIGHTS` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `PARENT_ID` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `ADD_QX` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `DEL_QX` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `EDIT_QX` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `CHA_QX` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `RNUMBER` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  PRIMARY KEY (`ROLE_ID`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_role
-- ----------------------------
INSERT INTO `sys_role` VALUES ('1', '系统管理组', '4951760157141521099596496894', '0', '1', '1', '1', '1', 'R20170000000001');
INSERT INTO `sys_role` VALUES ('147d873ec4154673a15ac7c093b68657', '备用组', '', '0', '0', '0', '0', '0', 'R20190219170377');
INSERT INTO `sys_role` VALUES ('e295424edf854dac8329dee5a6b7f016', '部门经理', '4799506339367320868261724160', '1', '4799506339367320868261724160', '4799506339367320868261724160', '4799506339367320868261724160', '4799506339367320868261724160', 'R20171231556774');
INSERT INTO `sys_role` VALUES ('ebdb884104f24671ad8d30440242adad', '员工', '4799506339367320868261724160', '1', '4799506339367320868261724160', '4799506339367320868261724160', '4799506339367320868261724160', '4799506339367320868261724160', 'R20171231102049');
INSERT INTO `sys_role` VALUES ('f1cb1a689f2a4b57bcf8761b350c0ffc', '总经理', '4799506339367320868261724160', '1', '4799506339367320868261724160', '4799506339367320868261724160', '4799506339367320868261724160', '4799506339367320868261724160', 'R20171231726481');
INSERT INTO `sys_role` VALUES ('fhadminzhuche', '注册用户', '0', '1', '0', '0', '0', '0', 'R20171231000000');

-- ----------------------------
-- Table structure for sys_role_fhbutton
-- ----------------------------
DROP TABLE IF EXISTS `sys_role_fhbutton`;
CREATE TABLE `sys_role_fhbutton`  (
  `RB_ID` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `ROLE_ID` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `BUTTON_ID` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  PRIMARY KEY (`RB_ID`) USING BTREE,
  INDEX `角色表外键`(`ROLE_ID`) USING BTREE,
  INDEX `fbutton`(`BUTTON_ID`) USING BTREE,
  CONSTRAINT `sys_role_fhbutton_ibfk_1` FOREIGN KEY (`BUTTON_ID`) REFERENCES `sys_fhbutton` (`FHBUTTON_ID`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `sys_role_fhbutton_ibfk_2` FOREIGN KEY (`ROLE_ID`) REFERENCES `sys_role` (`ROLE_ID`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_role_fhbutton
-- ----------------------------
INSERT INTO `sys_role_fhbutton` VALUES ('0188077fa0d54e99a37e530011a314eb', 'fhadminzhuche', '117825711d484e719a2a3bf1d0d45c2b');
INSERT INTO `sys_role_fhbutton` VALUES ('2b3e72844e9642ed9056731643518bfa', 'ebdb884104f24671ad8d30440242adad', '7b9756f1455d418396d14bf5a1f8ed09');
INSERT INTO `sys_role_fhbutton` VALUES ('2e941c79fdd34387b292e27444ba081f', 'f1cb1a689f2a4b57bcf8761b350c0ffc', '117825711d484e719a2a3bf1d0d45c2b');
INSERT INTO `sys_role_fhbutton` VALUES ('3242f38b09d64bc2bb89bd845a1e10d0', 'f1cb1a689f2a4b57bcf8761b350c0ffc', '688f6db8b226468e82e0f2c40d377fd9');
INSERT INTO `sys_role_fhbutton` VALUES ('34edcd073e5e480c824166e5dfd9d2eb', 'e295424edf854dac8329dee5a6b7f016', 'c3d94fcfd3ee4a2a8f08bb1cd110bc5c');
INSERT INTO `sys_role_fhbutton` VALUES ('45caf625a8bd4f7d92233d6be53d1e67', 'ebdb884104f24671ad8d30440242adad', '117825711d484e719a2a3bf1d0d45c2b');
INSERT INTO `sys_role_fhbutton` VALUES ('4fd20366545d457d878469698300db7e', 'f1cb1a689f2a4b57bcf8761b350c0ffc', '1af9cef37b1f4cc79abacb52c25a3dfa');
INSERT INTO `sys_role_fhbutton` VALUES ('5220351589b7475588cbca6400f41cb1', 'e295424edf854dac8329dee5a6b7f016', '7b9756f1455d418396d14bf5a1f8ed09');
INSERT INTO `sys_role_fhbutton` VALUES ('53787c5736cc4b4da21e51f9f9c7b042', 'ebdb884104f24671ad8d30440242adad', 'c3d94fcfd3ee4a2a8f08bb1cd110bc5c');
INSERT INTO `sys_role_fhbutton` VALUES ('60fe1d06944e4925b4ae930da9e8b1eb', 'f1cb1a689f2a4b57bcf8761b350c0ffc', '7b9756f1455d418396d14bf5a1f8ed09');
INSERT INTO `sys_role_fhbutton` VALUES ('611c9bde27754b99896892368a0bed15', 'e295424edf854dac8329dee5a6b7f016', 'e2a0d76a25bd467aa29aaadf3a8def18');
INSERT INTO `sys_role_fhbutton` VALUES ('7d7400610af745af91a78b5320cf0078', 'e295424edf854dac8329dee5a6b7f016', '117825711d484e719a2a3bf1d0d45c2b');
INSERT INTO `sys_role_fhbutton` VALUES ('ab9337220acf41b78e7f6bb165dc8713', 'e295424edf854dac8329dee5a6b7f016', '1af9cef37b1f4cc79abacb52c25a3dfa');
INSERT INTO `sys_role_fhbutton` VALUES ('b49b9415a31c4de2818c32d13fd5209e', 'ebdb884104f24671ad8d30440242adad', 'e2a0d76a25bd467aa29aaadf3a8def18');
INSERT INTO `sys_role_fhbutton` VALUES ('cbe304e060bd485c83cded08bd976c6e', 'ebdb884104f24671ad8d30440242adad', '688f6db8b226468e82e0f2c40d377fd9');
INSERT INTO `sys_role_fhbutton` VALUES ('cdbfd62ecad149d4a7eeffaaefbc1c3b', 'e295424edf854dac8329dee5a6b7f016', '688f6db8b226468e82e0f2c40d377fd9');
INSERT INTO `sys_role_fhbutton` VALUES ('cf38f7e5708140a0923999442665faef', 'f1cb1a689f2a4b57bcf8761b350c0ffc', 'c3d94fcfd3ee4a2a8f08bb1cd110bc5c');
INSERT INTO `sys_role_fhbutton` VALUES ('f4db056c93044b3db39767ee857134a0', 'f1cb1a689f2a4b57bcf8761b350c0ffc', 'e2a0d76a25bd467aa29aaadf3a8def18');

-- ----------------------------
-- Table structure for sys_user
-- ----------------------------
DROP TABLE IF EXISTS `sys_user`;
CREATE TABLE `sys_user`  (
  `USER_ID` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '用户ID',
  `USERNAME` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '用户名',
  `PASSWORD` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '密码',
  `NAME` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '姓名',
  `ROLE_ID` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '角色ID',
  `LAST_LOGIN` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '最近登录时间',
  `IP` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'IP',
  `STATUS` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '状态',
  `BZ` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '备注',
  `SKIN` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '皮肤',
  `EMAIL` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '邮箱',
  `NUMBER` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '编码',
  `PHONE` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '电话',
  `ROLE_IDS` varchar(2000) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '副职角色ID组',
  PRIMARY KEY (`USER_ID`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_user
-- ----------------------------
INSERT INTO `sys_user` VALUES ('1', 'admin', 'de41b7fb99201d8334c23c014db35ecd92df81bc', '系统管理员', '1', '2019-07-11 18:15:28', '0:0:0:0:0:0:0:1', '0', 'admin', 'pcoded-navbar,navbar pcoded-header navbar-expand-lg navbar-light,', '<EMAIL>', '001', '18788888888', '');
INSERT INTO `sys_user` VALUES ('3a6c44458b6243fbb9c2bcf30d422e33', 'lisi', '2612ade71c1e48cd7150b5f4df152faa699cedfe', '李四', 'e295424edf854dac8329dee5a6b7f016', '2019-03-09 20:26:26', '127.0.0.1', '0', '李四', 'pcoded-navbar navbar-image-3,navbar pcoded-header navbar-expand-lg navbar-light header-dark,', '<EMAIL>', 'z002', '18788885858', '');
INSERT INTO `sys_user` VALUES ('62c0aafd97704c3a85ef0fca3048045d', 'zhangsan', '5ee5d458d02fde6170b9c3ebfe06af85dacd131d', '张三', 'ebdb884104f24671ad8d30440242adad', '2019-03-09 20:26:46', '127.0.0.1', '0', '张三', 'pcoded-navbar navbar-image-3,navbar pcoded-header navbar-expand-lg navbar-light header-dark,', '<EMAIL>', 'z001', '18765888888', 'e295424edf854dac8329dee5a6b7f016,f1cb1a689f2a4b57bcf8761b350c0ffc');
INSERT INTO `sys_user` VALUES ('d7b34acd89d6441da59787477425e91f', 'wangwu', '99162695b36d0b7d54bab64468bdbb89c6ad45c5', '王五', 'f1cb1a689f2a4b57bcf8761b350c0ffc', '2019-03-09 20:30:06', '127.0.0.1', '0', '王五', 'pcoded-navbar navbar-image-3,navbar pcoded-header navbar-expand-lg navbar-light header-dark,', '<EMAIL>', 'z003', '18756666666', '');

-- ----------------------------
-- Table structure for sys_userphoto
-- ----------------------------
DROP TABLE IF EXISTS `sys_userphoto`;
CREATE TABLE `sys_userphoto`  (
  `USERPHOTO_ID` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `USERNAME` varchar(30) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '用户名',
  `PHOTO0` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '原图',
  `PHOTO1` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '头像1',
  `PHOTO2` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '头像2',
  `PHOTO3` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '头像3',
  PRIMARY KEY (`USERPHOTO_ID`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

SET FOREIGN_KEY_CHECKS = 1;
