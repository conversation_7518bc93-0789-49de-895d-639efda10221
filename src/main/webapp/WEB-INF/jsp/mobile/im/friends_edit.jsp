<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt"%>
<%
	String path = request.getContextPath();
	String basePath = request.getScheme() + "://" + request.getServerName() + ":" + request.getServerPort()
			+ path + "/";
%>
<!DOCTYPE html>
<html lang="en">
<head>
	<base href="<%=basePath%>">
    <meta charset="utf-8">
    <title>${sessionScope.sysName}</title>
    <!-- HTML5 Shim and Respond.js IE10 support of HTML5 elements and media queries -->
    <!-- WARNING: Respond.js doesn't work if you view the page via file:// -->
    <!--[if lt IE 10]>
		<script src="https://oss.maxcdn.com/libs/html5shiv/3.7.0/html5shiv.js"></script>
		<script src="https://oss.maxcdn.com/libs/respond.js/1.4.2/respond.min.js"></script>
		<![endif]-->
    <!-- Meta -->
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=0, minimal-ui">
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="author" content="FH Admin QQ313596790" />

    <link rel="icon" href="assets/images/favicon.ico" type="image/x-icon">
    <link rel="stylesheet" href="assets/fonts/fontawesome/css/fontawesome-all.min.css">
    <link rel="stylesheet" href="assets/plugins/animation/css/animate.min.css">
    <link rel="stylesheet" href="assets/css/style.css">

</head>

<body style="background-color: white">
    
    <!-- [加载状态 ] start -->
    <div class="loader-bg">
        <div class="loader-track">
            <div class="loader-fill"></div>
        </div>
    </div>
    <!-- [ 加载状态  ] End -->

    <!-- [ 主内容区 ] start -->
        <div class="pcoded-wrapper">
            <div class="pcoded-content">
                <div class="pcoded-inner-content">
                    <div class="main-body">
                        <div class="page-wrapper">
                            <!-- [ Main Content ] start -->
                            <div class="row">
								<form action="" name="Form" id="Form" method="post" style="width: 100%;">
									<input type="hidden" name="FRIENDS_ID" id="FRIENDS_ID" value="${pd.FRIENDS_ID}"/>
									 <div id="showform" style="margin-left: 6px;">
							            <div class="input-group input-group-sm mb-3" style="max-width: 378px;">
		                                    <div class="input-group-prepend">
		                                        <span class="input-group-text" style="width: 79px;"><span style="width: 100%;">分组</span></span>
		                                    </div>
		                                    <select name="FGROUP_ID" id="FGROUP_ID"  title="分组" style="width:239px;padding-left:10px;border: 1px solid #CED4DA;transparent;appearance:none;-moz-appearance:none;-webkit-appearance:none;"></select>
		                                </div>
							            <div class="input-group" style="margin-top:10px;background-color: white" >
							            	<span style="width: 100%;text-align: center;">
							            		<a class="btn btn-light btn-sm" onclick="save();">保存</a>
							            		<a class="btn btn-light btn-sm" onclick="top.Dialog.close();">取消</a>
							            	</span>
							            </div>
								    </div>
									<!-- [加载状态 ] start -->
								    <div id="jiazai" style="display:none;margin-top:10px;">
								    	<div class="d-flex justify-content-center">
	                                        <div class="spinner-border" style="width: 3rem; height: 3rem;" role="status">
                                                <span class="sr-only">Loading...</span>
                                            </div>
                                        </div>
                                    </div>
								    <!-- [ 加载状态  ] End -->
								</form>
                            </div>
                            <!-- [ Main Content ] end -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    <!-- [ 主内容区 ] end -->
    
<script type="text/javascript" src="assets/js/jquery-3.3.1.min.js"></script>
<script type="text/javascript" src="assets/js/pre-loader.js"></script>
<!-- 表单验证提示 -->
<script src="assets/js/jquery.tips.js"></script>
		<script type="text/javascript">
		//保存
		function save(){
			$("#showform").hide();
			$("#jiazai").show();
			var FRIENDS_ID = $("#FRIENDS_ID").val();
			var FGROUP_ID = $("#FGROUP_ID").val();
			$.ajax({
				type: "POST",
				url: '<%=basePath%>mobfriends/edit?tm='+new Date().getTime(),
		    	data: {FRIENDS_ID:FRIENDS_ID,FGROUP_ID:FGROUP_ID},
				dataType:'json',
				cache: false,
				success: function(data){
					top.Dialog.close();
				}
			});
		}
		
		$(function() {
			var FGROUP_ID = "${pd.FGROUP_ID}";
			$.ajax({
				type: "POST",
				url: '<%=basePath%>mobfgroup/getFgroup?tm='+new Date().getTime(),
		    	data: {},
				dataType:'json',
				cache: false,
				success: function(data){
					 $("#FGROUP_ID").append("<option value=''>请选择分组</option>");
					 $.each(data.list, function(i, dvar){
						 if(FGROUP_ID == dvar.FGROUP_ID){
							 $("#FGROUP_ID").append("<option value="+dvar.FGROUP_ID+" selected='selected'>"+dvar.NAME+"</option>");
						 }else{
							 $("#FGROUP_ID").append("<option value="+dvar.FGROUP_ID+">"+dvar.NAME+"</option>");
						 }
					 });
				}
			});
		});
		</script>
</body>
</html>