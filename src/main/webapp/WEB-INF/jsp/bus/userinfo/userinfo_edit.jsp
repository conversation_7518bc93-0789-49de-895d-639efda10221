<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<%
    String path = request.getContextPath();
    String basePath =
            request.getScheme() + "://" + request.getServerName() + ":" + request.getServerPort()
                    + path + "/";
%>
<!DOCTYPE html>
<html lang="en">
<head>
    <base href="<%=basePath%>">
    <meta charset="utf-8">
    <title>${sessionScope.sysName}</title>
    <!-- HTML5 Shim and Respond.js IE10 support of HTML5 elements and media queries -->
    <!-- WARNING: Respond.js doesn't work if you view the page via file:// -->
    <!--[if lt IE 10]>
    <script src="https://oss.maxcdn.com/libs/html5shiv/3.7.0/html5shiv.js"></script>
    <script src="https://oss.maxcdn.com/libs/respond.js/1.4.2/respond.min.js"></script>
    <![endif]-->
    <!-- Meta -->
    <meta charset="utf-8">
    <meta name="viewport"
          content="width=device-width, initial-scale=1.0, user-scalable=0, minimal-ui">
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta name="author" content="FH Admin QQ313596790"/>

    <link rel="icon" href="assets/images/favicon.ico" type="image/x-icon">
    <link rel="stylesheet" href="assets/fonts/fontawesome/css/fontawesome-all.min.css">
    <link rel="stylesheet" href="assets/plugins/animation/css/animate.min.css">
    <link rel="stylesheet" href="assets/css/style.css">

    <!-- 日期插件 -->
    <link rel="stylesheet"
          href="assets/plugins/material-datetimepicker/css/bootstrap-material-datetimepicker.css">

    <!-- select插件 -->
    <link rel="stylesheet" href="assets/plugins/select2/css/select2.min.css">
    <link rel="stylesheet" href="assets/plugins/multi-select/css/multi-select.css">

</head>

<body style="background-color: white">

<!-- [加载状态 ] start -->
<div class="loader-bg">
    <div class="loader-track">
        <div class="loader-fill"></div>
    </div>
</div>
<!-- [ 加载状态  ] End -->

<!-- [ 主内容区 ] start -->
<div class="pcoded-wrapper">
    <div class="pcoded-content">
        <div class="pcoded-inner-content">
            <div class="main-body">
                <div class="page-wrapper">
                    <!-- [ Main Content ] start -->
                    <div class="row">

                        <form action="userinfo/${msg }" name="Form" id="Form" method="post"
                              style="width: 100%;">
                            <input type="hidden" name="USERINFO_ID" id="USERINFO_ID"
                                   value="${pd.USERINFO_ID}"/>
                            <div id="showform">
                                <div class="input-group input-group-sm mb-3"
                                     style="margin-top: -10px;">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text" style="width: 100px;"><span
                                                style="width: 100%;">工号</span></span>
                                    </div>
                                    <input type="text" class="form-control" name="EMP_ID"
                                           id="EMP_ID" value="${pd.EMP_ID}" maxlength="20"
                                           readonly="readonly"
                                           placeholder="工号" title="工号">
                                </div>
                                <div class="input-group input-group-sm mb-3"
                                     style="margin-top: -10px;">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text" style="width: 100px;"><span
                                                style="width: 100%;">NETWORK_ID</span></span>
                                    </div>
                                    <input type="text" class="form-control" name="NETWORK_ID"
                                           id="NETWORK_ID" value="${pd.NETWORK_ID}" maxlength="20"
                                           placeholder="这里输入NETWORK_ID" title="NETWORK_ID">
                                </div>
                                <div class="input-group input-group-sm mb-3"
                                     style="margin-top: -10px;">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text" style="width: 100px;"><span
                                                style="width: 100%;">姓名</span></span>
                                    </div>
                                    <input type="text" class="form-control" name="EMP_NAME"
                                           id="EMP_NAME" value="${pd.EMP_NAME}" maxlength="50"
                                           readonly="readonly"
                                           placeholder="这里输入姓名" title="姓名">
                                </div>
                                <div class="input-group input-group-sm mb-3"
                                     style="margin-top: -10px;">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text" style="width: 100px;"><span
                                                style="width: 100%;">部门</span></span>
                                    </div>
                                    <input type="text" class="form-control" name="DEPT" id="DEPT"
                                           value="${pd.DEPT}" maxlength="50" placeholder="这里输入部门"
                                           title="部门">
                                </div>
                                <div class="input-group input-group-sm mb-3"
                                     style="margin-top: -10px;">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text" style="width: 100px;"><span
                                                style="width: 100%;">职位</span></span>
                                    </div>
                                    <input type="text" class="form-control" name="POSITION"
                                           id="POSITION" value="${pd.POSITION}" maxlength="50"
                                           placeholder="这里输入职位" title="职位">
                                </div>
                                <div class="input-group input-group-sm mb-3"
                                     style="margin-top: -10px;">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text" style="width: 100px;"><span
                                                style="width: 100%;">在职状态</span></span>
                                    </div>
                                    <input type="number" class="form-control" name="STATUS_ON"
                                           id="STATUS_ON" value="${pd.STATUS_ON}" maxlength="32"
                                           placeholder="这里输入在职状态" title="在职状态">
                                </div>
                                <div class="input-group input-group-sm mb-3"
                                     style="margin-top: -10px;">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text" style="width: 100px;"><span
                                                style="width: 100%;">1st Level Mgr ID</span></span>
                                    </div>
                                    <input type="text" class="form-control"
                                           name="FIRST_LEVEL_MGR_ID" id="FIRST_LEVEL_MGR_ID"
                                           value="${pd.FIRST_LEVEL_MGR_ID}" maxlength="100"
                                           placeholder="这里输入1st Level Mgr ID"
                                           title="1st Level Mgr ID">
                                </div>
                                <div class="input-group input-group-sm mb-3"
                                     style="margin-top: -10px;">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text" style="width: 100px;"><span
                                                style="width: 100%;">2nd Level Mgr ID</span></span>
                                    </div>
                                    <input type="text" class="form-control"
                                           name="SECOND_LEVEL_MGR_ID" id="SECOND_LEVEL_MGR_ID"
                                           value="${pd.SECOND_LEVEL_MGR_ID}" maxlength="100"
                                           placeholder="这里输入2nd Level Mgr ID"
                                           title="2nd Level Mgr ID">
                                </div>
                                <div class="input-group input-group-sm mb-3"
                                     style="margin-top: -10px;">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text" style="width: 100px;"><span
                                                style="width: 100%;">工时制类型</span></span>
                                    </div>
                                    <input type="number" class="form-control" name="WH_TYPE"
                                           id="WH_TYPE" value="${pd.WH_TYPE}" maxlength="32"
                                           placeholder="这里输入工时制类型" title="工时制类型">
                                </div>
                                <div class="input-group input-group-sm mb-3"
                                     style="margin-top: -10px;">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text" style="width: 100px;"><span
                                                style="width: 100%;">班组</span></span>
                                    </div>
                                    <input type="number" class="form-control" name="TEAM" id="TEAM"
                                           value="${pd.TEAM}" maxlength="32" placeholder="这里输入班组"
                                           title="班组">
                                </div>
                                <div class="input-group input-group-sm mb-3"
                                     style="margin-top: -10px;">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text" style="width: 100px;"><span
                                                style="width: 100%;">是否借调中</span></span>
                                    </div>
                                    <input type="number" class="form-control" name="TRANSFERED"
                                           id="TRANSFERED" value="${pd.TRANSFERED}" maxlength="32"
                                           placeholder="这里输入是否借调中" title="是否借调中">
                                </div>
                                <div class="input-group input-group-sm mb-3"
                                     style="margin-top: -10px;">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text" style="width: 100px;"><span
                                                style="width: 100%;">绑定微信号</span></span>
                                    </div>
                                    <input type="text" class="form-control" name="OPEN_ID"
                                           id="OPEN_ID" value="${pd.OPEN_ID}" maxlength="255"
                                           readonly="readonly"
                                           placeholder="绑定微信号" title="绑定微信号">
                                </div>
                                <div class="input-group input-group-sm mb-3"
                                     style="margin-top: -10px;">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text" style="width: 100px;"><span
                                                style="width: 100%;">人脸更新时间</span></span>
                                    </div>
                                    <input type="text" class="form-control date"
                                           name="FACE_UPDATE_TIME" id="FACE_UPDATE_TIME"
                                           value="${pd.FACE_UPDATE_TIME}" maxlength="32"
                                           readonly="readonly"
                                           placeholder="人脸更新时间" title="人脸更新时间">
                                </div>
                                <div class="input-group input-group-sm mb-3"
                                     style="margin-top: -10px;">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text" style="width: 100px;"><span
                                                style="width: 100%;">人脸地址</span></span>
                                    </div>
                                    <input type="text" class="form-control" name="FACE_URL"
                                           id="FACE_URL" value="${pd.FACE_URL}" maxlength="500"
                                           placeholder="这里输入人脸地址" title="人脸地址">
                                </div>
                                <div class="input-group"
                                     style="margin-top:10px;background-color: white">
							            	<span style="width: 100%;text-align: center;">
							            		<a class="btn btn-light btn-sm"
                                                   onclick="save();">保存</a>
							            		<a class="btn btn-light btn-sm"
                                                   onclick="top.Dialog.close();">取消</a>
							            	</span>
                                </div>
                            </div>
                            <!-- [加载状态 ] start -->
                            <div id="jiazai" style="display:none;margin-top:50px;">
                                <div class="d-flex justify-content-center">
                                    <div class="spinner-border" style="width: 3rem; height: 3rem;"
                                         role="status">
                                        <span class="sr-only">Loading...</span>
                                    </div>
                                </div>
                            </div>
                            <!-- [ 加载状态  ] End -->
                        </form>

                    </div>
                    <!-- [ Main Content ] end -->
                </div>
            </div>
        </div>
    </div>
</div>
<!-- [ 主内容区 ] end -->

<script type="text/javascript" src="assets/js/jquery-1.7.2.js"></script>
<script type="text/javascript" src="assets/js/pre-loader.js"></script>

<!-- 日期插件 -->
<script src="assets/js/pages/moment-with-locales.min.js"></script>
<script src="assets/plugins/material-datetimepicker/js/bootstrap-material-datetimepicker.js"></script>
<script src="assets/js/pages/form-picker-custom.js"></script>

<!-- select插件 -->
<script src="assets/plugins/select2/js/select2.full.min.js"></script>
<script src="assets/plugins/multi-select/js/jquery.quicksearch.js"></script>
<script src="assets/plugins/multi-select/js/jquery.multi-select.js"></script>
<script src="assets/js/pages/form-select-custom.js"></script>

<!-- 表单验证提示 -->
<script src="assets/js/jquery.tips.js"></script>
<script type="text/javascript">
  //保存
  function save() {
    // if ($("#EMP_ID").val() == "") {
    //   $("#EMP_ID").tips({
    //     side: 3,
    //     msg: '请输入工号',
    //     bg: '#AE81FF',
    //     time: 2
    //   });
    //   $("#EMP_ID").focus();
    //   return false;
    // }
    // if ($("#NETWORK_ID").val() == "") {
    //   $("#NETWORK_ID").tips({
    //     side: 3,
    //     msg: '请输入NETWORK_ID',
    //     bg: '#AE81FF',
    //     time: 2
    //   });
    //   $("#NETWORK_ID").focus();
    //   return false;
    // }
    // if ($("#EMP_NAME").val() == "") {
    //   $("#EMP_NAME").tips({
    //     side: 3,
    //     msg: '请输入姓名',
    //     bg: '#AE81FF',
    //     time: 2
    //   });
    //   $("#EMP_NAME").focus();
    //   return false;
    // }
    // if ($("#DEPT").val() == "") {
    //   $("#DEPT").tips({
    //     side: 3,
    //     msg: '请输入部门',
    //     bg: '#AE81FF',
    //     time: 2
    //   });
    //   $("#DEPT").focus();
    //   return false;
    // }
    // if ($("#POSITION").val() == "") {
    //   $("#POSITION").tips({
    //     side: 3,
    //     msg: '请输入职位',
    //     bg: '#AE81FF',
    //     time: 2
    //   });
    //   $("#POSITION").focus();
    //   return false;
    // }
    // if ($("#STATUS_ON").val() == "") {
    //   $("#STATUS_ON").tips({
    //     side: 3,
    //     msg: '请输入在职状态',
    //     bg: '#AE81FF',
    //     time: 2
    //   });
    //   $("#STATUS_ON").focus();
    //   return false;
    // }
    // if ($("#FIRST_LEVEL_MGR_ID").val() == "") {
    //   $("#FIRST_LEVEL_MGR_ID").tips({
    //     side: 3,
    //     msg: '请输入1st Level Mgr ID',
    //     bg: '#AE81FF',
    //     time: 2
    //   });
    //   $("#FIRST_LEVEL_MGR_ID").focus();
    //   return false;
    // }
    // if ($("#SECOND_LEVEL_MGR_ID").val() == "") {
    //   $("#SECOND_LEVEL_MGR_ID").tips({
    //     side: 3,
    //     msg: '请输入2nd Level Mgr ID',
    //     bg: '#AE81FF',
    //     time: 2
    //   });
    //   $("#SECOND_LEVEL_MGR_ID").focus();
    //   return false;
    // }
    // if ($("#WH_TYPE").val() == "") {
    //   $("#WH_TYPE").tips({
    //     side: 3,
    //     msg: '请输入工时制类型',
    //     bg: '#AE81FF',
    //     time: 2
    //   });
    //   $("#WH_TYPE").focus();
    //   return false;
    // }
    // if ($("#TEAM").val() == "") {
    //   $("#TEAM").tips({
    //     side: 3,
    //     msg: '请输入班组',
    //     bg: '#AE81FF',
    //     time: 2
    //   });
    //   $("#TEAM").focus();
    //   return false;
    // }
    // if ($("#TRANSFERED").val() == "") {
    //   $("#TRANSFERED").tips({
    //     side: 3,
    //     msg: '请输入是否借调中',
    //     bg: '#AE81FF',
    //     time: 2
    //   });
    //   $("#TRANSFERED").focus();
    //   return false;
    // }
    // if ($("#OPEN_ID").val() == "") {
    //   $("#OPEN_ID").tips({
    //     side: 3,
    //     msg: '请输入绑定微信号',
    //     bg: '#AE81FF',
    //     time: 2
    //   });
    //   $("#OPEN_ID").focus();
    //   return false;
    // }
    // if ($("#FACE_UPDATE_TIME").val() == "") {
    //   $("#FACE_UPDATE_TIME").tips({
    //     side: 3,
    //     msg: '请输入人脸更新时间',
    //     bg: '#AE81FF',
    //     time: 2
    //   });
    //   $("#FACE_UPDATE_TIME").focus();
    //   return false;
    // }
    // if ($("#FACE_URL").val() == "") {
    //   $("#FACE_URL").tips({
    //     side: 3,
    //     msg: '请输入人脸地址',
    //     bg: '#AE81FF',
    //     time: 2
    //   });
    //   $("#FACE_URL").focus();
    //   return false;
    // }
    $("#Form").submit();
    $("#showform").hide();
    $("#jiazai").show();
  }

  $(function () {

  });
</script>

</body>
</html>