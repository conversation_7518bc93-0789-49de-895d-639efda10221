# THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
# yarn lockfile v1


add-dom-event-listener@^1.1.0:
  version "1.1.0"
  resolved "https://registry.npm.taobao.org/add-dom-event-listener/download/add-dom-event-listener-1.1.0.tgz#6a92db3a0dd0abc254e095c0f1dc14acbbaae310"
  integrity sha1-apLbOg3Qq8JU4JXA8dwUrLuq4xA=
  dependencies:
    object-assign "4.x"

antd-mobile@^2.2.14:
  version "2.2.14"
  resolved "https://registry.npm.taobao.org/antd-mobile/download/antd-mobile-2.2.14.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fantd-mobile%2Fdownload%2Fantd-mobile-2.2.14.tgz#0c3b4da1ef383799ec33f268786860e9cc4533d7"
  integrity sha1-DDtNoe84N5nsM/JoeGhg6cxFM9c=
  dependencies:
    array-tree-filter "~2.1.0"
    babel-runtime "6.x"
    classnames "^2.2.1"
    normalize.css "^7.0.0"
    rc-checkbox "~2.0.0"
    rc-collapse "~1.9.1"
    rc-slider "~8.2.0"
    rc-swipeout "~2.0.0"
    rmc-calendar "^1.0.0"
    rmc-cascader "~5.0.0"
    rmc-date-picker "^6.0.8"
    rmc-dialog "^1.0.1"
    rmc-drawer "^0.4.11"
    rmc-feedback "^2.0.0"
    rmc-input-number "^1.0.0"
    rmc-list-view "^0.11.0"
    rmc-notification "~1.0.0"
    rmc-nuka-carousel "~3.0.0"
    rmc-picker "~5.0.0"
    rmc-pull-to-refresh "~1.0.1"
    rmc-steps "~1.0.0"
    rmc-tabs "~1.2.0"
    rmc-tooltip "~1.0.0"

array-tree-filter@2.1.x, array-tree-filter@~2.1.0:
  version "2.1.0"
  resolved "https://registry.npm.taobao.org/array-tree-filter/download/array-tree-filter-2.1.0.tgz#873ac00fec83749f255ac8dd083814b4f6329190"
  integrity sha1-hzrAD+yDdJ8lWsjdCDgUtPYykZA=

asap@~2.0.3:
  version "2.0.6"
  resolved "https://registry.npm.taobao.org/asap/download/asap-2.0.6.tgz#e50347611d7e690943208bbdafebcbc2fb866d46"
  integrity sha1-5QNHYR1+aQlDIIu9r+vLwvuGbUY=

babel-runtime@6.x, babel-runtime@^6.23.0, babel-runtime@^6.26.0:
  version "6.26.0"
  resolved "https://registry.npm.taobao.org/babel-runtime/download/babel-runtime-6.26.0.tgz#965c7058668e82b55d7bfe04ff2337bc8b5647fe"
  integrity sha1-llxwWGaOgrVde/4E/yM3vItWR/4=
  dependencies:
    core-js "^2.4.0"
    regenerator-runtime "^0.11.0"

classnames@2.x, classnames@^2.2.0, classnames@^2.2.1, classnames@^2.2.3, classnames@^2.2.4, classnames@^2.2.5, classnames@^2.2.6:
  version "2.2.6"
  resolved "https://registry.npm.taobao.org/classnames/download/classnames-2.2.6.tgz#43935bffdd291f326dad0a205309b38d00f650ce"
  integrity sha1-Q5Nb/90pHzJtrQogUwmzjQD2UM4=

component-classes@^1.2.5:
  version "1.2.6"
  resolved "https://registry.npm.taobao.org/component-classes/download/component-classes-1.2.6.tgz#c642394c3618a4d8b0b8919efccbbd930e5cd691"
  integrity sha1-xkI5TDYYpNiwuJGe/Mu9kw5c1pE=
  dependencies:
    component-indexof "0.0.3"

component-indexof@0.0.3:
  version "0.0.3"
  resolved "https://registry.npm.taobao.org/component-indexof/download/component-indexof-0.0.3.tgz#11d091312239eb8f32c8f25ae9cb002ffe8d3c24"
  integrity sha1-EdCRMSI5648yyPJa6csAL/6NPCQ=

core-js@^1.0.0:
  version "1.2.7"
  resolved "https://registry.npm.taobao.org/core-js/download/core-js-1.2.7.tgz?cache=0&sync_timestamp=1560599811627&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fcore-js%2Fdownload%2Fcore-js-1.2.7.tgz#652294c14651db28fa93bd2d5ff2983a4f08c636"
  integrity sha1-ZSKUwUZR2yj6k70tX/KYOk8IxjY=

core-js@^2.4.0:
  version "2.6.9"
  resolved "https://registry.npm.taobao.org/core-js/download/core-js-2.6.9.tgz?cache=0&sync_timestamp=1560599811627&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fcore-js%2Fdownload%2Fcore-js-2.6.9.tgz#6b4b214620c834152e179323727fc19741b084f2"
  integrity sha1-a0shRiDINBUuF5Mjcn/Bl0GwhPI=

create-react-class@^15.6.0:
  version "15.6.3"
  resolved "https://registry.npm.taobao.org/create-react-class/download/create-react-class-15.6.3.tgz#2d73237fb3f970ae6ebe011a9e66f46dbca80036"
  integrity sha1-LXMjf7P5cK5uvgEanmb0bbyoADY=
  dependencies:
    fbjs "^0.8.9"
    loose-envify "^1.3.1"
    object-assign "^4.1.1"

css-animation@1.x, css-animation@^1.3.2:
  version "1.5.0"
  resolved "https://registry.npm.taobao.org/css-animation/download/css-animation-1.5.0.tgz#c96b9097a5ef74a7be8480b45cc44e4ec6ca2bf5"
  integrity sha1-yWuQl6XvdKe+hIC0XMROTsbKK/U=
  dependencies:
    babel-runtime "6.x"
    component-classes "^1.2.5"

dom-align@1.x, dom-align@^1.7.0:
  version "1.9.0"
  resolved "https://registry.npm.taobao.org/dom-align/download/dom-align-1.9.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fdom-align%2Fdownload%2Fdom-align-1.9.0.tgz#27339be5a2301e7d8470bdee54575c835c981654"
  integrity sha1-JzOb5aIwHn2EcL3uVFdcg1yYFlQ=

encoding@^0.1.11:
  version "0.1.12"
  resolved "https://registry.npm.taobao.org/encoding/download/encoding-0.1.12.tgz#538b66f3ee62cd1ab51ec323829d1f9480c74beb"
  integrity sha1-U4tm8+5izRq1HsMjgp0flIDHS+s=
  dependencies:
    iconv-lite "~0.4.13"

exenv@^1.2.0:
  version "1.2.2"
  resolved "https://registry.npm.taobao.org/exenv/download/exenv-1.2.2.tgz#2ae78e85d9894158670b03d47bec1f03bd91bb9d"
  integrity sha1-KueOhdmJQVhnCwPUe+wfA72Ru50=

fbjs@^0.8.3, fbjs@^0.8.9:
  version "0.8.17"
  resolved "https://registry.npm.taobao.org/fbjs/download/fbjs-0.8.17.tgz#c4d598ead6949112653d6588b01a5cdcd9f90fdd"
  integrity sha1-xNWY6taUkRJlPWWIsBpc3Nn5D90=
  dependencies:
    core-js "^1.0.0"
    isomorphic-fetch "^2.1.1"
    loose-envify "^1.0.0"
    object-assign "^4.1.0"
    promise "^7.1.1"
    setimmediate "^1.0.5"
    ua-parser-js "^0.7.18"

iconv-lite@~0.4.13:
  version "0.4.24"
  resolved "https://registry.npm.taobao.org/iconv-lite/download/iconv-lite-0.4.24.tgz#2022b4b25fbddc21d2f524974a474aafe733908b"
  integrity sha1-ICK0sl+93CHS9SSXSkdKr+czkIs=
  dependencies:
    safer-buffer ">= 2.1.2 < 3"

is-stream@^1.0.1:
  version "1.1.0"
  resolved "https://registry.npm.taobao.org/is-stream/download/is-stream-1.1.0.tgz#12d4a3dd4e68e0b79ceb8dbc84173ae80d91ca44"
  integrity sha1-EtSj3U5o4Lec6428hBc66A2RykQ=

isomorphic-fetch@^2.1.1:
  version "2.2.1"
  resolved "https://registry.npm.taobao.org/isomorphic-fetch/download/isomorphic-fetch-2.2.1.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fisomorphic-fetch%2Fdownload%2Fisomorphic-fetch-2.2.1.tgz#611ae1acf14f5e81f729507472819fe9733558a9"
  integrity sha1-YRrhrPFPXoH3KVB0coGf6XM1WKk=
  dependencies:
    node-fetch "^1.0.1"
    whatwg-fetch ">=0.10.0"

js-cookie@^2.2.0:
  version "2.2.0"
  resolved "https://registry.npm.taobao.org/js-cookie/download/js-cookie-2.2.0.tgz#1b2c279a6eece380a12168b92485265b35b1effb"
  integrity sha1-Gywnmm7s44ChIWi5JIUmWzWx7/s=

"js-tokens@^3.0.0 || ^4.0.0":
  version "4.0.0"
  resolved "https://registry.npm.taobao.org/js-tokens/download/js-tokens-4.0.0.tgz#19203fb59991df98e3a287050d4647cdeaf32499"
  integrity sha1-GSA/tZmR35jjoocFDUZHzerzJJk=

lodash._getnative@^3.0.0:
  version "3.9.1"
  resolved "https://registry.npm.taobao.org/lodash._getnative/download/lodash._getnative-3.9.1.tgz#570bc7dede46d61cdcde687d65d3eecbaa3aaff5"
  integrity sha1-VwvH3t5G1hzc3mh9ZdPuy6o6r/U=

lodash.isarguments@^3.0.0:
  version "3.1.0"
  resolved "https://registry.npm.taobao.org/lodash.isarguments/download/lodash.isarguments-3.1.0.tgz#2f573d85c6a24289ff00663b491c1d338ff3458a"
  integrity sha1-L1c9hcaiQon/AGY7SRwdM4/zRYo=

lodash.isarray@^3.0.0:
  version "3.0.4"
  resolved "https://registry.npm.taobao.org/lodash.isarray/download/lodash.isarray-3.0.4.tgz#79e4eb88c36a8122af86f844aa9bcd851b5fbb55"
  integrity sha1-eeTriMNqgSKvhvhEqpvNhRtfu1U=

lodash.keys@^3.1.2:
  version "3.1.2"
  resolved "https://registry.npm.taobao.org/lodash.keys/download/lodash.keys-3.1.2.tgz#4dbc0472b156be50a0b286855d1bd0b0c656098a"
  integrity sha1-TbwEcrFWvlCgsoaFXRvQsMZWCYo=
  dependencies:
    lodash._getnative "^3.0.0"
    lodash.isarguments "^3.0.0"
    lodash.isarray "^3.0.0"

loose-envify@^1.0.0, loose-envify@^1.3.1, loose-envify@^1.4.0:
  version "1.4.0"
  resolved "https://registry.npm.taobao.org/loose-envify/download/loose-envify-1.4.0.tgz#71ee51fa7be4caec1a63839f7e682d8132d30caf"
  integrity sha1-ce5R+nvkyuwaY4OffmgtgTLTDK8=
  dependencies:
    js-tokens "^3.0.0 || ^4.0.0"

node-fetch@^1.0.1:
  version "1.7.3"
  resolved "https://registry.npm.taobao.org/node-fetch/download/node-fetch-1.7.3.tgz#980f6f72d85211a5347c6b2bc18c5b84c3eb47ef"
  integrity sha1-mA9vcthSEaU0fGsrwYxbhMPrR+8=
  dependencies:
    encoding "^0.1.11"
    is-stream "^1.0.1"

normalize.css@^7.0.0:
  version "7.0.0"
  resolved "https://registry.npm.taobao.org/normalize.css/download/normalize.css-7.0.0.tgz#abfb1dd82470674e0322b53ceb1aaf412938e4bf"
  integrity sha1-q/sd2CRwZ04DIrU86xqvQSk45L8=

object-assign@4.x, object-assign@^4.1.0, object-assign@^4.1.1:
  version "4.1.1"
  resolved "https://registry.npm.taobao.org/object-assign/download/object-assign-4.1.1.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fobject-assign%2Fdownload%2Fobject-assign-4.1.1.tgz#2109adc7965887cfc05cbbd442cac8bfbb360863"
  integrity sha1-IQmtx5ZYh8/AXLvUQsrIv7s2CGM=

performance-now@^2.1.0:
  version "2.1.0"
  resolved "https://registry.npm.taobao.org/performance-now/download/performance-now-2.1.0.tgz#6309f4e0e5fa913ec1c69307ae364b4b377c9e7b"
  integrity sha1-Ywn04OX6kT7BxpMHrjZLSzd8nns=

promise@^7.1.1:
  version "7.3.1"
  resolved "https://registry.npm.taobao.org/promise/download/promise-7.3.1.tgz#064b72602b18f90f29192b8b1bc418ffd1ebd3bf"
  integrity sha1-BktyYCsY+Q8pGSuLG8QY/9Hr078=
  dependencies:
    asap "~2.0.3"

prop-types@15.x, prop-types@^15.5.10, prop-types@^15.5.4, prop-types@^15.5.6, prop-types@^15.5.8:
  version "15.7.2"
  resolved "https://registry.npm.taobao.org/prop-types/download/prop-types-15.7.2.tgz#52c41e75b8c87e72b9d9360e0206b99dcbffa6c5"
  integrity sha1-UsQedbjIfnK52TYOAga5ncv/psU=
  dependencies:
    loose-envify "^1.4.0"
    object-assign "^4.1.1"
    react-is "^16.8.1"

raf@^3.1.0, raf@^3.3.2, raf@^3.4.0:
  version "3.4.1"
  resolved "https://registry.npm.taobao.org/raf/download/raf-3.4.1.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fraf%2Fdownload%2Fraf-3.4.1.tgz#0742e99a4a6552f445d73e3ee0328af0ff1ede39"
  integrity sha1-B0LpmkplUvRF1z4+4DKK8P8e3jk=
  dependencies:
    performance-now "^2.1.0"

rc-align@^2.4.0:
  version "2.4.5"
  resolved "https://registry.npm.taobao.org/rc-align/download/rc-align-2.4.5.tgz#c941a586f59d1017f23a428f0b468663fb7102ab"
  integrity sha1-yUGlhvWdEBfyOkKPC0aGY/txAqs=
  dependencies:
    babel-runtime "^6.26.0"
    dom-align "^1.7.0"
    prop-types "^15.5.8"
    rc-util "^4.0.4"

rc-animate@2.x, rc-animate@^2.4.4:
  version "2.9.2"
  resolved "https://registry.npm.taobao.org/rc-animate/download/rc-animate-2.9.2.tgz?cache=0&sync_timestamp=1563332895143&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Frc-animate%2Fdownload%2Frc-animate-2.9.2.tgz#5964767805c886f1bdc7563d3935a74912a0b78f"
  integrity sha1-WWR2eAXIhvG9x1Y9OTWnSRKgt48=
  dependencies:
    babel-runtime "6.x"
    classnames "^2.2.6"
    css-animation "^1.3.2"
    prop-types "15.x"
    raf "^3.4.0"
    rc-util "^4.8.0"
    react-lifecycles-compat "^3.0.4"

rc-checkbox@~2.0.0:
  version "2.0.3"
  resolved "https://registry.npm.taobao.org/rc-checkbox/download/rc-checkbox-2.0.3.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Frc-checkbox%2Fdownload%2Frc-checkbox-2.0.3.tgz#436a9d508948e224980f0535ea738b48177a8f25"
  integrity sha1-Q2qdUIlI4iSYDwU16nOLSBd6jyU=
  dependencies:
    babel-runtime "^6.23.0"
    classnames "2.x"
    prop-types "15.x"
    rc-util "^4.0.4"

rc-collapse@~1.9.1:
  version "1.9.3"
  resolved "https://registry.npm.taobao.org/rc-collapse/download/rc-collapse-1.9.3.tgz#d9741db06a823353e1fd1aec3ba4c0f9d8af4b26"
  integrity sha1-2XQdsGqCM1Ph/RrsO6TA+divSyY=
  dependencies:
    classnames "2.x"
    css-animation "1.x"
    prop-types "^15.5.6"
    rc-animate "2.x"

rc-gesture@~0.0.18, rc-gesture@~0.0.22:
  version "0.0.22"
  resolved "https://registry.npm.taobao.org/rc-gesture/download/rc-gesture-0.0.22.tgz#fbcbdd5b46387a978b3ede48b42748e8ff77dddd"
  integrity sha1-+8vdW0Y4epeLPt5ItCdI6P933d0=
  dependencies:
    babel-runtime "6.x"

rc-slider@~8.2.0:
  version "8.2.0"
  resolved "https://registry.npm.taobao.org/rc-slider/download/rc-slider-8.2.0.tgz#ae37d17144cad60e1da6eac0ee4ffcfea0b0a6e8"
  integrity sha1-rjfRcUTK1g4dpurA7k/8/qCwpug=
  dependencies:
    babel-runtime "6.x"
    classnames "^2.2.5"
    prop-types "^15.5.4"
    rc-tooltip "^3.4.2"
    rc-util "^4.0.4"
    shallowequal "^1.0.1"
    warning "^3.0.0"

rc-swipeout@~2.0.0:
  version "2.0.11"
  resolved "https://registry.npm.taobao.org/rc-swipeout/download/rc-swipeout-2.0.11.tgz#dfad9c7b38a15ea0376e39cb3356e36fed7a4155"
  integrity sha1-362cezihXqA3bjnLM1bjb+16QVU=
  dependencies:
    babel-runtime "6.x"
    classnames "2.x"
    rc-gesture "~0.0.22"
    react-native-swipeout "^2.2.2"

rc-tooltip@^3.4.2:
  version "3.7.3"
  resolved "https://registry.npm.taobao.org/rc-tooltip/download/rc-tooltip-3.7.3.tgz#280aec6afcaa44e8dff0480fbaff9e87fc00aecc"
  integrity sha1-KArsavyqROjf8EgPuv+eh/wArsw=
  dependencies:
    babel-runtime "6.x"
    prop-types "^15.5.8"
    rc-trigger "^2.2.2"

rc-trigger@^2.2.2:
  version "2.6.5"
  resolved "https://registry.npm.taobao.org/rc-trigger/download/rc-trigger-2.6.5.tgz#140a857cf28bd0fa01b9aecb1e26a50a700e9885"
  integrity sha1-FAqFfPKL0PoBua7LHialCnAOmIU=
  dependencies:
    babel-runtime "6.x"
    classnames "^2.2.6"
    prop-types "15.x"
    rc-align "^2.4.0"
    rc-animate "2.x"
    rc-util "^4.4.0"
    react-lifecycles-compat "^3.0.4"

rc-util@4.x, rc-util@^4.0.4, rc-util@^4.4.0, rc-util@^4.8.0:
  version "4.8.1"
  resolved "https://registry.npm.taobao.org/rc-util/download/rc-util-4.8.1.tgz?cache=0&sync_timestamp=1562815056825&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Frc-util%2Fdownload%2Frc-util-4.8.1.tgz#1ca4faba70b2915e5511fc732805294c38019cee"
  integrity sha1-HKT6unCykV5VEfxzKAUpTDgBnO4=
  dependencies:
    add-dom-event-listener "^1.1.0"
    babel-runtime "6.x"
    prop-types "^15.5.10"
    react-lifecycles-compat "^3.0.4"
    shallowequal "^0.2.2"

react-is@^16.8.1:
  version "16.8.6"
  resolved "https://registry.npm.taobao.org/react-is/download/react-is-16.8.6.tgz#5bbc1e2d29141c9fbdfed456343fe2bc430a6a16"
  integrity sha1-W7weLSkUHJ+9/tRWND/ivEMKahY=

react-lifecycles-compat@^3.0.4:
  version "3.0.4"
  resolved "https://registry.npm.taobao.org/react-lifecycles-compat/download/react-lifecycles-compat-3.0.4.tgz#4f1a273afdfc8f3488a8c516bfda78f872352362"
  integrity sha1-TxonOv38jzSIqMUWv9p4+HI1I2I=

react-native-swipeout@^2.2.2:
  version "2.3.6"
  resolved "https://registry.npm.taobao.org/react-native-swipeout/download/react-native-swipeout-2.3.6.tgz#47dac8a835825cf3f2eef9e495574a3d9ab6d3fa"
  integrity sha1-R9rIqDWCXPPy7vnklVdKPZq20/o=
  dependencies:
    create-react-class "^15.6.0"
    prop-types "^15.5.10"
    react-tween-state "^0.1.5"

react-tween-state@^0.1.5:
  version "0.1.5"
  resolved "https://registry.npm.taobao.org/react-tween-state/download/react-tween-state-0.1.5.tgz#e98b066551efb93cb92dd1be14995c2e3deae339"
  integrity sha1-6YsGZVHvuTy5LdG+FJlcLj3q4zk=
  dependencies:
    raf "^3.1.0"
    tween-functions "^1.0.1"

regenerator-runtime@^0.11.0:
  version "0.11.1"
  resolved "https://registry.npm.taobao.org/regenerator-runtime/download/regenerator-runtime-0.11.1.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fregenerator-runtime%2Fdownload%2Fregenerator-runtime-0.11.1.tgz#be05ad7f9bf7d22e056f9726cee5017fbf19e2e9"
  integrity sha1-vgWtf5v30i4Fb5cmzuUBf78Z4uk=

rmc-align@~1.0.0:
  version "1.0.0"
  resolved "https://registry.npm.taobao.org/rmc-align/download/rmc-align-1.0.0.tgz#8d64ab484609a041ab424506012a15b7c5b933dd"
  integrity sha1-jWSrSEYJoEGrQkUGASoVt8W5M90=
  dependencies:
    babel-runtime "6.x"
    dom-align "1.x"
    rc-util "4.x"

rmc-calendar@^1.0.0:
  version "1.1.4"
  resolved "https://registry.npm.taobao.org/rmc-calendar/download/rmc-calendar-1.1.4.tgz#7db4990087877cd49a7772f4524d33b8016d3bd2"
  integrity sha1-fbSZAIeHfNSad3L0Uk0zuAFtO9I=
  dependencies:
    babel-runtime "^6.26.0"
    rc-animate "^2.4.4"
    rmc-date-picker "^6.0.8"

rmc-cascader@~5.0.0:
  version "5.0.3"
  resolved "https://registry.npm.taobao.org/rmc-cascader/download/rmc-cascader-5.0.3.tgz#c605b1eac6613e4c54aa6aed2cbae7f9c5a8c65f"
  integrity sha1-xgWx6sZhPkxUqmrtLLrn+cWoxl8=
  dependencies:
    array-tree-filter "2.1.x"
    babel-runtime "6.x"
    rmc-picker "~5.0.0"

rmc-date-picker@^6.0.8:
  version "6.0.9"
  resolved "https://registry.npm.taobao.org/rmc-date-picker/download/rmc-date-picker-6.0.9.tgz#7b59538a048002e33f215da9bef2e5e4eda53476"
  integrity sha1-e1lTigSAAuM/IV2pvvLl5O2lNHY=
  dependencies:
    babel-runtime "6.x"
    rmc-picker "~5.0.0"

rmc-dialog@^1.0.1, rmc-dialog@^1.1.1:
  version "1.1.1"
  resolved "https://registry.npm.taobao.org/rmc-dialog/download/rmc-dialog-1.1.1.tgz#1d7fbc6b2cad5b0b53fbab71fe29636d76f78217"
  integrity sha1-HX+8ayytWwtT+6tx/iljbXb3ghc=
  dependencies:
    babel-runtime "6.x"
    rc-animate "2.x"

rmc-drawer@^0.4.11:
  version "0.4.11"
  resolved "https://registry.npm.taobao.org/rmc-drawer/download/rmc-drawer-0.4.11.tgz#9a8c6125a4ccd37b916f32f7e8b477d11d413ee3"
  integrity sha1-moxhJaTM03uRbzL36LR30R1BPuM=
  dependencies:
    babel-runtime "6.x"
    classnames "^2.2.4"
    prop-types "^15.5.10"

rmc-feedback@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npm.taobao.org/rmc-feedback/download/rmc-feedback-2.0.0.tgz#cbc6cb3ae63c7a635eef0e25e4fbaf5ac366eeaa"
  integrity sha1-y8bLOuY8emNe7w4l5PuvWsNm7qo=
  dependencies:
    babel-runtime "6.x"
    classnames "^2.2.5"

rmc-input-number@^1.0.0:
  version "1.0.5"
  resolved "https://registry.npm.taobao.org/rmc-input-number/download/rmc-input-number-1.0.5.tgz#42e02a27b0c3c366be9ff0ce19d818b71e406f8f"
  integrity sha1-QuAqJ7DDw2a+n/DOGdgYtx5Ab48=
  dependencies:
    babel-runtime "6.x"
    classnames "^2.2.0"
    rmc-feedback "^2.0.0"

rmc-list-view@^0.11.0:
  version "0.11.5"
  resolved "https://registry.npm.taobao.org/rmc-list-view/download/rmc-list-view-0.11.5.tgz#8e152a5dbec6aec45a8ccd1f33cb8ef140b93a1e"
  integrity sha1-jhUqXb7GrsRajM0fM8uO8UC5Oh4=
  dependencies:
    babel-runtime "6.x"
    classnames "^2.2.5"
    fbjs "^0.8.3"
    prop-types "^15.5.8"
    warning "^3.0.0"
    zscroller "~0.4.0"

rmc-notification@~1.0.0:
  version "1.0.0"
  resolved "https://registry.npm.taobao.org/rmc-notification/download/rmc-notification-1.0.0.tgz#1fcee98f99b9733f7ce63a91d7663a578743d075"
  integrity sha1-H87pj5m5cz985jqR12Y6V4dD0HU=
  dependencies:
    babel-runtime "6.x"
    classnames "2.x"
    prop-types "^15.5.8"
    rc-animate "2.x"
    rc-util "^4.0.4"

rmc-nuka-carousel@~3.0.0:
  version "3.0.1"
  resolved "https://registry.npm.taobao.org/rmc-nuka-carousel/download/rmc-nuka-carousel-3.0.1.tgz#a2a997676b0f986354976dac39ec66d8701b4b71"
  integrity sha1-oqmXZ2sPmGNUl22sOexm2HAbS3E=
  dependencies:
    exenv "^1.2.0"
    raf "^3.3.2"

rmc-picker@~5.0.0:
  version "5.0.10"
  resolved "https://registry.npm.taobao.org/rmc-picker/download/rmc-picker-5.0.10.tgz?cache=0&sync_timestamp=1562139848690&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Frmc-picker%2Fdownload%2Frmc-picker-5.0.10.tgz#9ca0acf45ad2c8afe9015a103a898436d825e18f"
  integrity sha1-nKCs9FrSyK/pAVoQOomENtgl4Y8=
  dependencies:
    babel-runtime "6.x"
    classnames "^2.2.6"
    rmc-dialog "^1.1.1"
    rmc-feedback "^2.0.0"

rmc-pull-to-refresh@~1.0.1:
  version "1.0.11"
  resolved "https://registry.npm.taobao.org/rmc-pull-to-refresh/download/rmc-pull-to-refresh-1.0.11.tgz#e31fee9f82ab903fa35617509970b28dc61a9857"
  integrity sha1-4x/un4KrkD+jVhdQmXCyjcYamFc=
  dependencies:
    babel-runtime "6.x"
    classnames "^2.2.5"

rmc-steps@~1.0.0:
  version "1.0.0"
  resolved "https://registry.npm.taobao.org/rmc-steps/download/rmc-steps-1.0.0.tgz#0c50d0dff9d3e72e101914300a781993552dc526"
  integrity sha1-DFDQ3/nT5y4QGRQwCngZk1UtxSY=
  dependencies:
    babel-runtime "^6.23.0"
    classnames "^2.2.3"

rmc-tabs@~1.2.0:
  version "1.2.29"
  resolved "https://registry.npm.taobao.org/rmc-tabs/download/rmc-tabs-1.2.29.tgz#dd2191525debbf8521e85aeb6d97670f652e4c83"
  integrity sha1-3SGRUl3rv4Uh6FrrbZdnD2UuTIM=
  dependencies:
    babel-runtime "6.x"
    rc-gesture "~0.0.18"

rmc-tooltip@~1.0.0:
  version "1.0.1"
  resolved "https://registry.npm.taobao.org/rmc-tooltip/download/rmc-tooltip-1.0.1.tgz#5af16a3e8f764fa26d2b11932975bd88b1d848d2"
  integrity sha1-WvFqPo92T6JtKxGTKXW9iLHYSNI=
  dependencies:
    babel-runtime "6.x"
    rmc-trigger "1.x"

rmc-trigger@1.x:
  version "1.0.12"
  resolved "https://registry.npm.taobao.org/rmc-trigger/download/rmc-trigger-1.0.12.tgz#34df10a16f1fc8f9e8b14d13d58cabe294ab7488"
  integrity sha1-NN8QoW8fyPnosU0T1Yyr4pSrdIg=
  dependencies:
    babel-runtime "6.x"
    rc-animate "2.x"
    rc-util "4.x"
    rmc-align "~1.0.0"

"safer-buffer@>= 2.1.2 < 3":
  version "2.1.2"
  resolved "https://registry.npm.taobao.org/safer-buffer/download/safer-buffer-2.1.2.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fsafer-buffer%2Fdownload%2Fsafer-buffer-2.1.2.tgz#44fa161b0187b9549dd84bb91802f9bd8385cd6a"
  integrity sha1-RPoWGwGHuVSd2Eu5GAL5vYOFzWo=

setimmediate@^1.0.5:
  version "1.0.5"
  resolved "https://registry.npm.taobao.org/setimmediate/download/setimmediate-1.0.5.tgz#290cbb232e306942d7d7ea9b83732ab7856f8285"
  integrity sha1-KQy7Iy4waULX1+qbg3Mqt4VvgoU=

shallowequal@^0.2.2:
  version "0.2.2"
  resolved "https://registry.npm.taobao.org/shallowequal/download/shallowequal-0.2.2.tgz#1e32fd5bcab6ad688a4812cb0cc04efc75c7014e"
  integrity sha1-HjL9W8q2rWiKSBLLDMBO/HXHAU4=
  dependencies:
    lodash.keys "^3.1.2"

shallowequal@^1.0.1:
  version "1.1.0"
  resolved "https://registry.npm.taobao.org/shallowequal/download/shallowequal-1.1.0.tgz#188d521de95b9087404fd4dcb68b13df0ae4e7f8"
  integrity sha1-GI1SHelbkIdAT9TctosT3wrk5/g=

tween-functions@^1.0.1:
  version "1.2.0"
  resolved "https://registry.npm.taobao.org/tween-functions/download/tween-functions-1.2.0.tgz#1ae3a50e7c60bb3def774eac707acbca73bbc3ff"
  integrity sha1-GuOlDnxguz3vd06scHrLynO7w/8=

ua-parser-js@^0.7.18:
  version "0.7.20"
  resolved "https://registry.npm.taobao.org/ua-parser-js/download/ua-parser-js-0.7.20.tgz#7527178b82f6a62a0f243d1f94fd30e3e3c21098"
  integrity sha1-dScXi4L2pioPJD0flP0w4+PCEJg=

warning@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npm.taobao.org/warning/download/warning-3.0.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fwarning%2Fdownload%2Fwarning-3.0.0.tgz#32e5377cb572de4ab04753bdf8821c01ed605b7c"
  integrity sha1-MuU3fLVy3kqwR1O9+IIcAe1gW3w=
  dependencies:
    loose-envify "^1.0.0"

whatwg-fetch@>=0.10.0:
  version "3.0.0"
  resolved "https://registry.npm.taobao.org/whatwg-fetch/download/whatwg-fetch-3.0.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fwhatwg-fetch%2Fdownload%2Fwhatwg-fetch-3.0.0.tgz#fc804e458cc460009b1a2b966bc8817d2578aefb"
  integrity sha1-/IBORYzEYACbGiuWa8iBfSV4rvs=

zscroller@~0.4.0:
  version "0.4.8"
  resolved "https://registry.npm.taobao.org/zscroller/download/zscroller-0.4.8.tgz#69eed68690808eedf81f9714014356b36cdd20f4"
  integrity sha1-ae7WhpCAju34H5cUAUNWs2zdIPQ=
  dependencies:
    babel-runtime "6.x"
