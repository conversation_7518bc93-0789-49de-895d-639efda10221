// https://umijs.org/config/

const plugins = [
  // ['import', { libraryName: 'antd-mobile', style: true }], // `style: true` 会加载 less 文件
  [
    'umi-plugin-react',
    {
      antd: true,
      dva: {
        immer: true,
      },
      dynamicImport: false,
      title: 'wechat_f_src',
      dll: false,
      routes: {
        exclude: [/components\//],
      },
    },
  ],
];

export default {
  // add for transfer to umi
  plugins,
  targets: {
    ie: 11,
  },
  define: {
    APP_TYPE: process.env.APP_TYPE || '',
  },
  proxy: {
    '/api/wechat': {
      target: 'http://localhost:3000/api/wechat',
      changeOrigin: true,
      pathRewrite: { '^/': '' },
    },
  },
  lessLoaderOptions: {
    javascriptEnabled: true,
  },
  base: '/',
  publicPath: '/wechat/',
  manifest: {
    basePath: '/',
  },
  history: 'hash',
  outputPath: '../src/main/webapp/wechat',
  // chainWebpack: webpackPlugin,
};
