import React from 'react';
import { List, InputItem } from 'antd-mobile';
import { hasOwnProperty } from '@/utils/utils';

function getFormItem(row, index, getFieldDecorator) {
  if (hasOwnProperty(row, 'show') && row.show === false) {
    return null;
  }
  const extraProps = hasOwnProperty(row, 'extra') ? row.extra : {};
  switch (row.type) {
    case 'text':
    case 'bankCard':
    case 'phone':
    case 'password':
    case 'number':
    case 'digit':
    case 'money':
      return getFieldDecorator(row.name, {
        rules: row.rules,
        initialValue: row.initialValue || [],
      })(
        <InputItem
          key={index.toString()}
          title={row.title || row.label}
          placeholder={row.placeholder || ''}
          disabled={row.readonly || false}
          onChange={row.onChange || false}
          mode={row.mode || ''}
          type={row.type}
          {...extraProps}
        >
          {row.label}
        </InputItem>,
      );

    // 自定义组件 ↓↓↓
    default:
      // if (row.formItem) {
      //   return (
      //     <Col
      //       key={`${row.name}-${index.toString()}`}
      //       span={row.width || defaultFieldSpan}
      //       offset={row.offset || 0}
      //       className={row.className || ''}
      //     >
      //       {row.formItem || 'wrong!'}
      //     </Col>
      //   );
      // }
      console.warn('Wrong Form Item!');
      return null;
  }
}

function FormBuilder({ formData, formFunction }) {
  const { getFieldDecorator } = formFunction;
  return formData.map((row, index) => {
    if (row.rules && row.rules[0]) {
      if (!row.rules[0].message) {
        row.rules[0].message = 'Required!';
      }
    }
    if (Object.prototype.hasOwnProperty.call(row, 'group')) {
      return (
        <List renderHeader={row.group} key={`${row.name}-${index.toString()}`}>
          {row.items.map((row1, index1) =>
            getFormItem(row1, `${index}_${index1}`, getFieldDecorator),
          )}
        </List>
      );
    }
    return getFormItem(row, index, getFieldDecorator);
  });
}

export default FormBuilder;
