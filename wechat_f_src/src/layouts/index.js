import { checkLogin } from '@/utils/utils';
import Cookies from 'js-cookie';
import { Fragment } from 'react';
import '@/global.less';
import { defaultPage } from '@/utils/consts';

function BasicLayout(props) {
  const { history } = props;
  if (!['/login'].includes(history.location.pathname) && !checkLogin()) {
    // 设置loginBack
    // console.log('history :', history);
    let loginBack = history.location.pathname;
    // 如果当前是login，则登录成功返回页默认个人信息页
    if (loginBack === '/login') {
      loginBack = defaultPage;
    }
    Cookies.set('loginBack', loginBack);
    history.replace('/login');
    return null;
  }
  return <Fragment>{props.children}</Fragment>;
}

export default BasicLayout;
