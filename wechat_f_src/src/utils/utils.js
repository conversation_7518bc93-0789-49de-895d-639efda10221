import Cookies from 'js-cookie';
import moment from 'moment';

// 检查是否有登录标志
export const checkLogin = () => {
  // Cookies.set('authToken','abc');
  const token = Cookies.get('authToken');
  if (!token) {
    return false;
  }
  return true;
};

/*
 * 对象中是否存在某属性
 */
export const hasOwnProperty = (obj, attr) => Object.prototype.hasOwnProperty.call(obj, attr);

/**
 * 格式化moment
 * @param {*} values
 */
export function filterMomentValues(values) {
  for (const key in values) {
    if (moment.isMoment(values[key])) {
      values[key] = values[key].format('YYYY-MM-DD HH:mm:ss');
    }
  }
  return values;
}
