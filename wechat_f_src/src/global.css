html,
body,
#root {
  height: 100%;
}
html {
  font-size: 10px;
}
body {
  margin: 0;
  background-color: #f0f0f0;
}
.result-page .am-result {
  background-color: rgba(255, 255, 255, 0.5);
}
.result-page .spe {
  width: 60px;
  height: 60px;
}
.center-v {
  height: 100%;
  display: flex;
  outline: solid 1px;
  justify-content: center;
  align-content: center;
  align-items: center;
}
.center-v .center-v-item {
  width: 100%;
}
.info {
  min-height: 100%;
  background-color: #ffffff;
  padding: 2rem;
  font-size: 2rem;
}
.info .avatar {
  width: 100%;
  text-align: center;
}
.info .avatar img {
  border-radius: 50%;
  box-shadow: 0 0 19px 1px #ccc;
  width: 10rem;
  height: 10rem;
}
.info .avatar .tips {
  margin-top: 5px;
  color: #a5a5a5;
  font-size: 1rem;
}
.info .row {
  padding: 1rem 3rem;
}
