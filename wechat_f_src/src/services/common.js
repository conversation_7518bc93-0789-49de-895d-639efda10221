/*
 * @Description: 公共请求类
 * @Author: <PERSON><PERSON><PERSON><<EMAIL>>
 * @Date: 2019-07-23 10:18:12
 * @LastEditors: <PERSON><PERSON><PERSON><<EMAIL>>
 * @LastEditTime: 2019-07-23 11:02:43
 */
import { stringify } from 'qs';
import request from '../utils/request';

export const apiRoot = '/api/wechat';

export async function get(url, params) {
  const res = await request(`${url}?${stringify(params)}`);
  return res;
}

export async function post(url, params) {
  const res = await request(url, {
    method: 'POST',
    body: {
      ...params,
    },
  });
  return res;
}

// 请求等待
export function wait(seconds) {
  return new Promise(resolve => setTimeout(resolve, seconds * 1000));
}
