/*
 * @Description: 用户绑定页
 * @Author: <PERSON><PERSON><PERSON><<EMAIL>>
 * @Date: 2019-07-18 14:19:52
 * @LastEditors: <PERSON><PERSON><PERSON><<EMAIL>>
 * @LastEditTime: 2019-07-23 11:18:36
 */
import React, { Component } from 'react';
import FormBuilder from '@/components/FormBuilder';
import { createForm } from 'rc-form';
import { Button, WingBlank, WhiteSpace, Toast } from 'antd-mobile';
import { connect } from 'dva';
import Cookies from 'js-cookie';
import router from 'umi/router';
import { defaultPage } from '@/utils/consts';

@createForm()
@connect(({ user }) => ({ user }))
class Bind extends Component {
  constructor(props) {
    super(props);
    document.title = '绑定用户';
  }

  getFormSet = () => {
    return [
      {
        group: '员工绑定',
        items: [
          {
            type: 'text',
            name: 'user_no',
            label: '工号',
            placeholder: '请输入工号',
            rules: [{ required: true, message: '请输入工号' }],
            extra: {
              maxLength: 8,
            },
          },
          {
            type: 'text',
            name: 'name',
            label: '姓名',
            placeholder: '请输入姓名',
            rules: [{ required: true, message: '请输入姓名' }],
          },
          {
            type: 'phone',
            name: 'tel',
            label: '手机号',
            placeholder: '请输入手机号',
            rules: [{ required: true, message: '请输入手机号' }],
          },
          {
            type: 'phone',
            name: 'tel_confirm',
            label: '确认手机号',
            placeholder: '请再次输入手机号',
            rules: [{ required: true, message: '请输入确认手机号' }],
          },
        ],
      },
    ];
  };

  onSaveForm = () => {
    const { validateFields } = this.props.form;
    validateFields((errors, values) => {
      if (errors) {
        const errMsg = errors[Object.keys(errors)[0]].errors[0].message;
        Toast.fail(errMsg, 1.5);
        return;
      }
      // 判断两次手机号是否一致
      if (values.tel !== values.tel_confirm) {
        Toast.fail('两次手机号不一致', 1.5);
        return;
      }
      this.props.dispatch({
        type: 'user/bind',
        payload: values,
        callback: data => {
          Toast.success('绑定成功');
          // 跳转页面
          console.log('success');
          let bindBack = Cookies.get('bindBack');
          if (!bindBack) {
            bindBack = defaultPage;
          }
          if (bindBack) {
            setTimeout(() => {
              router.replace(bindBack);
            }, 1500);
          }
        },
      });
    });
  };

  render() {
    const { submitLoading } = this.props.user;
    return (
      <div>
        <FormBuilder formData={this.getFormSet()} formFunction={this.props.form} />
        <WhiteSpace />
        <WingBlank>
          <Button type="primary" onClick={this.onSaveForm} loading={submitLoading}>
            提交
          </Button>
        </WingBlank>
      </div>
    );
  }
}

export default Bind;
