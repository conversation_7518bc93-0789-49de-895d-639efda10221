/*
 * @Description: 人脸管理
 * @Author: <PERSON><PERSON><PERSON><<EMAIL>>
 * @Date: 2019-07-23 14:34:58
 * @LastEditors: <PERSON><PERSON><PERSON><<EMAIL>>
 * @LastEditTime: 2019-07-23 14:37:06
 */
import React, { Component } from 'react';
import { connect } from 'dva';

// @connect(({ face }) => ({ face }))
class Face extends Component {
  render() {
    return <div>face</div>;
  }
}

export default Face;
