/*
 * @Description: 个人信息页
 * @Author: <PERSON><PERSON><PERSON><<EMAIL>>
 * @Date: 2019-07-18 14:18:24
 * @LastEditors: <PERSON><PERSON><PERSON><<EMAIL>>
 * @LastEditTime: 2019-07-23 14:33:58
 */
import React, { Component } from 'react';
import { connect } from 'dva';
import { router } from 'umi';
import { WhiteSpace } from 'antd-mobile';

@connect(({ user }) => ({ user }))
class Info extends Component {
  constructor(props) {
    super(props);
    this.fetchUserInfo();
    document.title = '个人信息';
  }

  fetchUserInfo = () => {
    this.props.dispatch({
      type: 'user/getInfo',
    });
  };

  getInfoArray = () => {
    const { info } = this.props.user;
    if (info && Object.keys(info).length > 0) {
      return [
        { label: '姓名', value: info.name || '' },
        { label: '工号', value: info.user_no || '' },
        { label: '部门', value: info.department || '' },
        { label: '职位', value: info.position || '' },
        { label: 'Plant', value: info.plant || '' },
        { label: 'Workshop', value: info.workshop || '' },
        { label: '生产线', value: info.line || '' },
      ];
    }
    return [];
  };

  onChangeAvatar = () => {
    router.push('/user/face');
  };

  render() {
    const { info } = this.props.user;
    return (
      <div className="info">
        <div className="avatar">
          <img src={info.avatar || ''} alt="" onClick={this.onChangeAvatar} />
          <div className="tips">点击照片可修改人脸数据</div>
        </div>
        <WhiteSpace size="lg" />
        {this.getInfoArray().map((item, index) => (
          <div className="row" key={index.toString()}>
            {item.label}: {item.value}
          </div>
        ))}
      </div>
    );
  }
}

export default Info;
