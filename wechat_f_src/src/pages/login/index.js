/*
 * @Description: 登录页
 * @Author: <PERSON><PERSON><PERSON><<EMAIL>>
 * @Date: 2019-07-18 14:12:49
 * @LastEditors: <PERSON><PERSON><PERSON><<EMAIL>>
 * @LastEditTime: 2019-07-23 11:12:55
 */

import React, { Component } from 'react';
import { Result, Icon, WhiteSpace } from 'antd-mobile';

class Login extends Component {
  constructor(props) {
    super(props);
    document.title = '登录中...';
  }

  // TODO: 登录成功但未绑定，跳转到绑定界面，并设置bindBack

  render() {
    return (
      <div className="center-v">
        <div className="result-page center-v-item">
          <Result
            img={<Icon type="loading" className="spe" style={{ fill: '#1F90E6' }} />}
            title="登录中..."
            message="即将前往微信验证您的身份"
          />
          <WhiteSpace />
        </div>
      </div>
    );
  }
}

export default Login;
