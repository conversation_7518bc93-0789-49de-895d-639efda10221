import { get, post, apiRoot } from '@/services/common';
import { Toast } from 'antd-mobile';

const defaultState = {
  submitLoading: false, // 表单提交中
  getInfoLoading: false, // 获取个人信息
  info: {}, //个人信息
};

export default {
  namespace: 'user',
  state: { ...defaultState },
  effects: {
    // 绑定微信
    *bind({ payload, callback }, { call, put }) {
      yield put({ type: 'save', payload: { submitLoading: true } });
      const res = yield call(post, `${apiRoot}/login/bindUser`, payload);
      if (res) {
        const { code, msg } = res;
        if (code === 1) {
          callback && typeof callback === 'function' && callback();
        } else {
          Toast.fail(msg, 1.5);
        }
      } else {
        Toast.fail('网络错误', 1.5);
      }
      yield put({ type: 'save', payload: { submitLoading: false } });
    },
    // 获取用户信息
    *getInfo({ payload, callback }, { call, put }) {
      yield put({ type: 'save', payload: { getInfoLoading: true } });
      const res = yield call(get, `${apiRoot}/user/info`, payload);
      if (res) {
        const { data, code, msg } = res;
        if (code === 1) {
          yield put({ type: 'save', payload: { info: data } });
          callback && typeof callback === 'function' && callback();
        } else {
          Toast.fail(msg, 1.5);
        }
      } else {
        Toast.fail('网络错误', 1.5);
      }
      yield put({ type: 'save', payload: { getInfoLoading: false } });
    },
  },

  reducers: {
    clear() {
      return { ...defaultState };
    },
    save(state, { payload }) {
      return { ...state, ...payload };
    },
  },
};
