{"private": true, "scripts": {"start": "umi dev", "build": "umi build", "test": "umi test", "lint": "eslint --ext .js src mock tests", "precommit": "lint-staged"}, "dependencies": {"rc-form": "^2.4.8", "react": "^16.8.6", "react-dom": "^16.8.6"}, "devDependencies": {"babel-eslint": "^9.0.0", "eslint": "^5.4.0", "eslint-config-umi": "^1.4.0", "eslint-plugin-flowtype": "^2.50.0", "eslint-plugin-import": "^2.14.0", "eslint-plugin-jsx-a11y": "^5.1.1", "eslint-plugin-react": "^7.11.1", "husky": "^0.14.3", "lint-staged": "^7.2.2", "react-test-renderer": "^16.7.0", "umi": "^2.7.7", "umi-plugin-react": "^1.9.10"}, "lint-staged": {"*.{js,jsx}": ["eslint --fix", "git add"]}, "engines": {"node": ">=8.0.0"}}